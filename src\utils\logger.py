#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
日志系统模块

提供应用程序全局日志功能，支持多种输出目标和日志级别。
"""

import datetime
import logging
import os
import sys
from logging.handlers import RotatingFileHandler, TimedRotatingFileHandler
from pathlib import Path
from typing import Optional, Dict, Any, List, Union

# 尝试导入Qt类，用于发送日志信号
try:
    from PyQt5.QtCore import QObject, pyqtSignal
    qt_available = True
except ImportError:
    try:
        from PySide6.QtCore import QObject, Signal as pyqtSignal
        qt_available = True
    except ImportError:
        qt_available = False

# 定义日志级别映射
LOG_LEVELS = {
    'debug': logging.DEBUG,
    'info': logging.INFO,
    'warning': logging.WARNING,
    'error': logging.ERROR,
    'critical': logging.CRITICAL
}

class QtSignalHandler(logging.Handler):
    """
    自定义日志处理器，将日志消息发送为Qt信号
    """
    def __init__(self, signal_emitter):
        super().__init__()
        self.signal_emitter = signal_emitter

    def emit(self, record):
        msg = self.format(record)
        # 发送日志信号，包含级别、消息和时间戳
        self.signal_emitter.log_message.emit(
            record.levelname,
            msg,
            record.created
        )

class LogSignalEmitter(QObject):
    """
    Qt信号发射器，用于在UI中显示日志信息
    """
    # 定义信号：日志级别, 消息内容, 时间戳
    log_message = pyqtSignal(str, str, float)

class Logger:
    """
    应用程序日志管理类

    提供全局日志记录功能，支持控制台、文件和Qt UI多种输出方式。
    采用单例模式，确保全局只有一个日志实例。
    """
    _instance = None
    _initialized = False
    _logger = None
    _signal_emitter = None if not qt_available else LogSignalEmitter()

    def __new__(cls):
        """实现单例模式"""
        if cls._instance is None:
            cls._instance = super(Logger, cls).__new__(cls)
        return cls._instance

    def __init__(self):
        """初始化日志系统"""
        if self._initialized:
            return
        
        # 创建根日志记录器
        self._logger = logging.getLogger('data_show')
        self._logger.setLevel(logging.DEBUG)  # 默认设置为最低级别
        self._logger.propagate = False  # 避免日志消息传播到根记录器
        
        # 保存当前配置
        self._config = {
            'level': 'debug',  # 默认使用debug级别
            'console_enabled': True,
            'file_enabled': True,
            'qt_enabled': qt_available,
            'log_dir': os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'logs'),
            'file_name_format': 'data_show_%Y%m%d.log',
            'rotate_by_size': False,
            'max_file_size_mb': 10,
            'file_count': 5,
            'rotate_by_time': True,
            'time_rotate_when': 'midnight',
            'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        }

        # 创建日志目录
        os.makedirs(self._config['log_dir'], exist_ok=True)
        
        # 标记为已初始化
        self._initialized = True
        
        # 应用默认配置
        self.configure()

    def configure(self, config: Dict[str, Any] = None) -> None:
        """
        配置日志系统
        
        Args:
            config: 配置字典，包含以下可选项：
                - level: 日志级别 ('debug', 'info', 'warning', 'error', 'critical')
                - console_enabled: 是否输出到控制台
                - file_enabled: 是否输出到文件
                - qt_enabled: 是否启用Qt信号
                - log_dir: 日志文件目录
                - file_name_format: 日志文件名格式
                - rotate_by_size: 是否按大小滚动
                - max_file_size_mb: 最大文件大小(MB)
                - file_count: 保留的日志文件数量
                - rotate_by_time: 是否按时间滚动
                - time_rotate_when: 时间滚动方式 ('midnight', 'h', 'd', 'w0'-'w6')
                - format: 日志格式字符串
        """
        if config:
            # 更新配置
            self._config.update(config)
        
        # 应用日志级别
        level = LOG_LEVELS.get(self._config['level'].lower(), logging.INFO)
        self._logger.setLevel(level)
        
        # 移除所有现有处理器
        for handler in self._logger.handlers[:]:
            self._logger.removeHandler(handler)
        
        # 创建格式化器
        formatter = logging.Formatter(self._config['format'])
        
        # 添加控制台处理器
        if self._config['console_enabled']:
            console_handler = logging.StreamHandler(sys.stdout)
            console_handler.setFormatter(formatter)
            self._logger.addHandler(console_handler)
        
        # 添加文件处理器
        if self._config['file_enabled']:
            now = datetime.datetime.now()
            log_file = os.path.join(
                self._config['log_dir'],
                now.strftime(self._config['file_name_format'])
            )
            
            if self._config['rotate_by_size']:
                # 按大小滚动
                file_handler = RotatingFileHandler(
                    log_file,
                    maxBytes=self._config['max_file_size_mb'] * 1024 * 1024,
                    backupCount=self._config['file_count']
                )
            elif self._config['rotate_by_time']:
                # 按时间滚动
                file_handler = TimedRotatingFileHandler(
                    log_file,
                    when=self._config['time_rotate_when'],
                    backupCount=self._config['file_count']
                )
            else:
                # 不滚动
                file_handler = logging.FileHandler(log_file)
                
            file_handler.setFormatter(formatter)
            self._logger.addHandler(file_handler)
        
        # 添加Qt信号处理器
        if self._config['qt_enabled'] and qt_available and self._signal_emitter:
            qt_handler = QtSignalHandler(self._signal_emitter)
            qt_handler.setFormatter(formatter)
            self._logger.addHandler(qt_handler)

    def get_signal_emitter(self) -> Optional[QObject]:
        """
        获取信号发射器对象，用于连接到UI
        
        Returns:
            LogSignalEmitter对象或None（如果Qt不可用）
        """
        return self._signal_emitter

    def debug(self, message: str, *args, **kwargs) -> None:
        """记录DEBUG级别日志"""
        self._logger.debug(message, *args, **kwargs)

    def info(self, message: str, *args, **kwargs) -> None:
        """记录INFO级别日志"""
        self._logger.info(message, *args, **kwargs)

    def warning(self, message: str, *args, **kwargs) -> None:
        """记录WARNING级别日志"""
        self._logger.warning(message, *args, **kwargs)

    def error(self, message: str, *args, **kwargs) -> None:
        """记录ERROR级别日志"""
        self._logger.error(message, *args, **kwargs)

    def critical(self, message: str, *args, **kwargs) -> None:
        """记录CRITICAL级别日志"""
        self._logger.critical(message, *args, **kwargs)

    def exception(self, message: str, *args, exc_info=True, **kwargs) -> None:
        """记录异常日志，包含堆栈跟踪"""
        self._logger.exception(message, *args, exc_info=exc_info, **kwargs)

    @staticmethod
    def get_instance() -> 'Logger':
        """获取Logger单例实例"""
        if Logger._instance is None:
            Logger()
        return Logger._instance


# 创建全局实例，方便导入
logger = Logger.get_instance()

# 简便的全局函数，可以直接导入使用
def debug(message: str, *args, **kwargs) -> None:
    """记录DEBUG级别日志"""
    logger.debug(message, *args, **kwargs)

def info(message: str, *args, **kwargs) -> None:
    """记录INFO级别日志"""
    logger.info(message, *args, **kwargs)

def warning(message: str, *args, **kwargs) -> None:
    """记录WARNING级别日志"""
    logger.warning(message, *args, **kwargs)

def error(message: str, *args, **kwargs) -> None:
    """记录ERROR级别日志"""
    logger.error(message, *args, **kwargs)

def critical(message: str, *args, **kwargs) -> None:
    """记录CRITICAL级别日志"""
    logger.critical(message, *args, **kwargs)

def exception(message: str, *args, **kwargs) -> None:
    """记录异常日志，包含堆栈跟踪"""
    logger.exception(message, *args, **kwargs)

def configure(config: Dict[str, Any] = None) -> None:
    """配置日志系统"""
    logger.configure(config)

def get_signal_emitter() -> Optional[QObject]:
    """获取信号发射器对象"""
    return logger.get_signal_emitter()

if __name__ == "__main__":
    # 简单的测试代码
    configure({
        'level': 'debug',
        'console_enabled': True,
        'file_enabled': True
    })
    
    debug("这是一条调试信息")
    info("这是一条信息")
    warning("这是一条警告")
    error("这是一条错误")
    critical("这是一条严重错误")
    
    try:
        1/0
    except Exception as e:
        exception("发生了异常")
