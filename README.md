# 数据可视化工具

## 项目简介
这是一个基于 Python (Qt) 的数据可视化软件，支持多协议硬件接口和丰富的图表类型，旨在满足不同应用场景的需求。

## 功能特性
- 支持多种类型的数据可视化：
  - 折线图
  - 散点图
  - 时间序列图
  - 柱状图
  - 热力图
  - 饼图
  - 箱线图
  - 雷达图
- 支持解析常用的硬件接口协议：
  - 自由串口
  - Modbus
  - TCP/IP
  - CAN总线
  - MQTT
  - OPC UA
  - I2C
  - SPI

## 安装说明
1. 克隆项目：
   ```bash
   git clone https://github.com/liuyazui/data-show.git
   cd data-show
   ```
2. 使用 `uv` 进行包管理：
   ```bash
   uv install
   ```

## 使用示例
```python
# 示例代码
import your_module

# 创建图表
chart = your_module.create_chart(data)
chart.show()
```

## 依赖项
本项目依赖以下库：
- 核心框架：
  - PyQt5
- 数据可视化：
  - pyqtgraph
  - matplotlib
- 硬件协议：
  - pymodbus
  - pyserial
  - python-can
  - paho-mqtt
  - opcua
- 数据处理：
  - numpy
  - pandas
  - scipy
- 辅助工具：
  - python-dotenv

## 贡献指南
欢迎任何形式的贡献！请查看 [CONTRIBUTING.md](CONTRIBUTING.md) 了解更多信息。

## 许可证
本项目采用 MIT 许可证，详细信息请查看 [LICENSE](LICENSE)。

## 联系方式
如有问题，请联系 [buaalyz](mailto:<EMAIL>)。
