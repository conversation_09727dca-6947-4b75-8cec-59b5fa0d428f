#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
日志管理器模块

提供日志系统的核心功能，包括日志的格式化、过滤和UI集成。
"""

import logging
import os
import sys
from datetime import datetime
from typing import Optional, Tuple, Dict, Any

try:
    # 尝试导入PyQt5
    from PyQt5.QtCore import QObject, pyqtSignal, Qt
    from PyQt5.QtWidgets import QPlainTextEdit, QApplication
    from PyQt5.QtGui import QTextCursor, QColor, QTextCharFormat
except ImportError:
    try:
        # 尝试导入PySide6
        from PySide6.QtCore import QObject, Signal as pyqtSignal, Qt
        from PySide6.QtWidgets import QPlainTextEdit, QApplication
        from PySide6.QtGui import QTextCursor, QColor, QTextCharFormat
    except ImportError:
        # 运行时可能会另行处理
        pass


class LogEmitter(QObject):
    """
    日志信号发射器，用于在UI线程中安全地更新日志显示
    
    使用Qt的信号-槽机制，确保跨线程的日志更新安全可靠
    """
    # 定义信号：参数为已格式化的HTML日志消息
    log_signal = pyqtSignal(str)
    
    def __init__(self):
        """初始化日志信号发射器"""
        super().__init__()


class QtLogHandler(logging.Handler):
    """
    Qt日志处理器，将日志输出重定向到Qt控件
    
    将标准的Python日志记录重定向到Qt组件（通常是QPlainTextEdit）并添加颜色支持
    """
    
    def __init__(self, log_widget: QPlainTextEdit):
        """
        初始化Qt日志处理器
        
        Args:
            log_widget: 用于显示日志的Qt控件
        """
        super().__init__()
        self.log_widget = log_widget
        
        # 创建日志信号发射器，用于线程安全的UI更新
        self.log_emitter = LogEmitter()
        self.log_emitter.log_signal.connect(self._append_log_to_widget)
        
        # 定义日志级别对应的颜色和名称
        self.log_levels = {
            logging.DEBUG: ("调试", "#0000FF"),      # 蓝色
            logging.INFO: ("信息", "#000000"),       # 黑色
            logging.WARNING: ("警告", "#FFA500"),    # 橙色
            logging.ERROR: ("错误", "#FF0000"),      # 红色
            logging.CRITICAL: ("严重", "#8B0000")    # 深红色
        }
        
        # 默认显示所有级别的日志
        self.min_level = logging.DEBUG
    
    def _append_log_to_widget(self, formatted_msg: str) -> None:
        """
        将格式化的日志消息追加到日志控件
        
        这个方法在UI线程中被调用
        
        Args:
            formatted_msg: 已格式化的HTML日志消息
        """
        # 添加HTML格式日志并滚动到底部
        self.log_widget.appendHtml(formatted_msg)
        self.log_widget.moveCursor(QTextCursor.End)
        
    def emit(self, record: logging.LogRecord) -> None:
        """
        处理日志记录
        
        Args:
            record: 日志记录对象
        """
        # 注意：不在这里过滤日志级别，让基类的filter和handle方法来处理
        
        try:
            # 格式化日志消息
            msg = self.format(record)
            level_name, color = self.log_levels.get(record.levelno, ("未知", "#000000"))
            
            # 创建带时间戳的格式化日志
            timestamp = datetime.fromtimestamp(record.created).strftime('%Y-%m-%d %H:%M:%S')
            formatted_msg = f'<span style="color:{color};">[{level_name}] {timestamp} - {record.name} - {msg}</span>'
            
            # 使用信号-槽机制在UI线程中安全地更新日志显示
            self.log_emitter.log_signal.emit(formatted_msg)
            
        except Exception as e:
            # 出现异常时输出到stderr（避免无限递归）
            import sys
            print(f"日志处理异常: {str(e)}", file=sys.stderr)

    def set_level(self, level: int) -> None:
        """
        设置最低显示级别
        
        Args:
            level: 日志级别（logging模块常量）
        """
        self.min_level = level
        # 同时调用父类的setLevel方法，确保日志过滤在Handler基类级别生效
        super().setLevel(level)


class LogManager:
    """
    日志管理器，协调文件日志和UI日志
    
    为应用程序提供集中的日志管理，支持文件和UI日志输出
    """
    
    def __init__(self):
        """初始化日志管理器"""
        self.qt_handler = None
        self.file_handler = None
        self.console_handler = None
        self.log_level = logging.INFO
        self.log_dir = ""
        self.log_file = ""
        self.initialized = False
        
    def initialize(self, log_widget: QPlainTextEdit, log_level: int = logging.DEBUG,
                  log_dir: str = None, enable_file_logging: bool = True) -> bool:
        """
        初始化日志系统
        
        Args:
            log_widget: 用于显示日志的Qt控件
            log_level: 默认日志级别，默认为DEBUG以捕获所有日志
            log_dir: 日志文件目录（如果为None，则使用默认目录）
            enable_file_logging: 是否启用文件日志
            
        Returns:
            初始化是否成功
        """
        if self.initialized:
            return True
            
        try:
            # 设置根日志格式
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            
            # 设置根日志器，确保使用最低级别以捕获所有日志
            root_logger = logging.getLogger()
            # 先移除所有已有的处理器，避免重复
            for handler in root_logger.handlers[:]:
                root_logger.removeHandler(handler)
            # 设置根日志器级别    
            root_logger.setLevel(log_level)
            
            # 创建Qt日志处理器
            self.qt_handler = QtLogHandler(log_widget)
            self.qt_handler.setFormatter(formatter)
            # 确保Qt日志处理器级别与根日志器一致
            self.qt_handler.setLevel(log_level)
            # 确保Qt处理器的min_level与根日志器一致
            self.qt_handler.set_level(log_level)
            root_logger.addHandler(self.qt_handler)
            
            # 设置当前日志级别
            self.log_level = log_level
            
            # 启用文件日志
            if enable_file_logging:
                self._setup_file_logging(log_dir, formatter, log_level)
                
            # 设置控制台日志（开发模式）
            self.console_handler = logging.StreamHandler(sys.stdout)
            self.console_handler.setFormatter(formatter)
            self.console_handler.setLevel(log_level)
            root_logger.addHandler(self.console_handler)
            
            self.initialized = True
            
            # 记录初始化成功日志
            logging.getLogger(__name__).info("日志系统初始化完成")
            if log_level <= logging.DEBUG:
                logging.getLogger(__name__).debug("调试日志已启用")
            return True
            
        except Exception as e:
            # 在控制台输出错误
            print(f"初始化日志系统失败: {str(e)}")
            import traceback
            print(traceback.format_exc())
            return False
            
    def _setup_file_logging(self, log_dir: str = None, formatter: logging.Formatter = None, log_level: int = logging.INFO) -> None:
        """
        设置文件日志
        
        Args:
            log_dir: 日志文件目录
            formatter: 日志格式化器
            log_level: 日志级别
        """
        try:
            # 设置日志目录
            if log_dir is None:
                # 获取应用程序根目录
                app_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
                log_dir = os.path.join(app_dir, 'logs')
                
            # 确保日志目录存在
            os.makedirs(log_dir, exist_ok=True)
            self.log_dir = log_dir
            
            # 创建日志文件名
            self.log_file = os.path.join(log_dir, f'data_show_{datetime.now().strftime("%Y%m%d")}.log')
            
            # 创建文件处理器
            self.file_handler = logging.FileHandler(self.log_file, encoding='utf-8')
            
            if formatter:
                self.file_handler.setFormatter(formatter)
                
            self.file_handler.setLevel(log_level)
            
            # 添加到根日志器
            logging.getLogger().addHandler(self.file_handler)
            
        except Exception as e:
            print(f"设置文件日志失败: {str(e)}")
            
    def set_level(self, level: int) -> None:
        """
        设置日志级别
        
        Args:
            level: 日志级别（logging模块常量）
        """
        self.log_level = level
        
        # 更新所有处理器的级别
        if self.qt_handler:
            # 同时更新Handler的setLevel和自定义的set_level
            self.qt_handler.setLevel(level)
            self.qt_handler.set_level(level)
            
        if self.file_handler:
            self.file_handler.setLevel(level)
            
        if self.console_handler:
            self.console_handler.setLevel(level)
            
        # 同时更新根日志器级别，确保所有日志都能传递到处理器
        logging.getLogger().setLevel(level)
            
        # 记录日志级别更改
        logging.getLogger(__name__).info(f"日志级别已设置为 {logging.getLevelName(level)}")
            
    def save_logs(self, file_path: str) -> Tuple[bool, str]:
        """
        保存当前UI日志到文件
        
        Args:
            file_path: 保存路径
            
        Returns:
            (是否成功, 错误消息)
        """
        try:
            if not self.qt_handler or not self.qt_handler.log_widget:
                return False, "日志控件未初始化"
                
            # 根据文件扩展名决定保存格式
            if file_path.lower().endswith('.html'):
                # 保存为HTML格式
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write("<html><body>")
                    f.write(self.qt_handler.log_widget.toHtml())
                    f.write("</body></html>")
            else:
                # 保存为纯文本
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(self.qt_handler.log_widget.toPlainText())
                    
            return True, ""
            
        except Exception as e:
            return False, str(e)
            
    def get_current_log_file(self) -> str:
        """
        获取当前日志文件路径
        
        Returns:
            当前日志文件的完整路径
        """
        return self.log_file
        
    def connect_logger(self, log_widget: QPlainTextEdit = None) -> bool:
        """
        连接logger.py中的日志系统，使其日志也能显示在UI上
        
        Args:
            log_widget: 用于显示日志的Qt控件，如未提供则使用当前控件
            
        Returns:
            是否成功连接
        """
        try:
            # 如果未提供log_widget，使用当前log_widget
            if log_widget is None and self.qt_handler is not None:
                log_widget = self.qt_handler.log_widget
                
            if log_widget is None:
                return False
                
            # 导入logger模块中的信号发射器
            from src.utils.logger import get_signal_emitter
            logger_emitter = get_signal_emitter()
            
            if logger_emitter is None:
                return False
                
            # 创建一个处理函数，将logger的信号转换为UI日志
            def handle_logger_signal(level_name, message, timestamp):
                # 获取颜色
                level_value = getattr(logging, level_name, logging.INFO)
                level_name_zh, color = self.qt_handler.log_levels.get(
                    level_value, 
                    ("信息", "#000000")
                )
                
                # 格式化时间戳
                time_str = datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d %H:%M:%S')
                
                # 创建格式化日志消息
                formatted_msg = f'<span style="color:{color};">[{level_name_zh}] {time_str} - data_show - {message}</span>'
                
                # 使用我们的LogEmitter发送到UI
                self.qt_handler.log_emitter.log_signal.emit(formatted_msg)
                
            # 连接信号
            logger_emitter.log_message.connect(handle_logger_signal)
            
            # 输出成功日志
            logging.getLogger(__name__).info("成功连接logger模块的日志系统到UI")
            return True
            
        except Exception as e:
            # 异常处理
            print(f"连接logger模块日志系统失败: {str(e)}")
            import traceback
            print(traceback.format_exc())
            return False


# 创建全局日志管理器实例
log_manager = LogManager() 