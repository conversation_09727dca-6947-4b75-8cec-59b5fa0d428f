#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试运行脚本

该脚本用于运行项目的单元测试。
可以运行所有测试或指定特定的测试模块。
"""

import os
import sys
import argparse
import unittest

def run_tests(test_modules=None, verbose=True):
    """
    运行单元测试
    
    Args:
        test_modules: 要运行的测试模块列表，None表示运行所有测试
        verbose: 是否显示详细输出
    
    Returns:
        测试结果
    """
    # 添加项目根目录到Python路径
    sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))
    
    # 创建测试加载器
    loader = unittest.TestLoader()
    
    # 确定测试套件
    if test_modules:
        # 运行指定的测试模块
        suite = unittest.TestSuite()
        for module in test_modules:
            try:
                tests = loader.loadTestsFromName(module)
                suite.addTests(tests)
            except (ImportError, AttributeError) as e:
                print(f"错误: 无法加载测试模块 {module}: {str(e)}")
                return False
    else:
        # 运行所有测试
        tests_dir = os.path.join(os.path.dirname(__file__), 'tests')
        suite = loader.discover(tests_dir)
    
    # 创建测试运行器
    verbosity = 2 if verbose else 1
    runner = unittest.TextTestRunner(verbosity=verbosity)
    
    # 运行测试
    result = runner.run(suite)
    
    # 返回测试是否成功
    return result.wasSuccessful()

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="运行单元测试")
    parser.add_argument(
        '-m', '--module', 
        action='append', 
        help='要运行的测试模块，格式如 "tests.core.test_config"。可以指定多个。'
    )
    parser.add_argument(
        '-c', '--core', 
        action='store_true', 
        help='运行核心模块测试'
    )
    parser.add_argument(
        '-q', '--quiet', 
        action='store_true', 
        help='减少输出详细程度'
    )
    
    return parser.parse_args()

def main():
    """主函数"""
    args = parse_args()
    
    test_modules = []
    
    # 如果指定了要运行的测试模块，添加它们
    if args.module:
        test_modules.extend(args.module)
    
    # 如果指定了运行核心模块测试
    if args.core:
        test_modules.extend([
            'tests.core.test_config',
            'tests.core.test_event_bus',
            'tests.core.test_thread_manager'
        ])
    
    # 运行测试
    success = run_tests(
        test_modules=test_modules if test_modules else None,
        verbose=not args.quiet
    )
    
    # 根据测试结果设置退出代码
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main() 