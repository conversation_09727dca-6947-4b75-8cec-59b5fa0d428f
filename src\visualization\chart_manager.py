#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
图表管理器模块

管理所有图表实例，提供添加、删除、获取图表的功能。
支持动态布局系统，处理图表之间的交互。
"""

import json
import os
import uuid
from enum import Enum, auto
from typing import Dict, List, Optional, Tuple, Union, Any, Type, cast

try:
    from PyQt5.QtWidgets import (
        QWidget, QVBoxLayout, QHBoxLayout, QGridLayout, 
        QSplitter, QFrame, QLabel, QPushButton, QMenu,
        QAction, QSizePolicy
    )
    from PyQt5.QtCore import Qt, QSize, pyqtSignal
    from PyQt5.QtGui import QIcon, QCursor
except ImportError:
    try:
        from PySide6.QtWidgets import (
            QWidget, QVBoxLayout, QHBoxLayout, QGridLayout, 
            QSplitter, QFrame, QLabel, QPushButton, QMenu,
            QAction, QSizePolicy
        )
        from PySide6.QtCore import Qt, QSize, Signal as pyqtSignal
        from PySide6.QtGui import QIcon, QCursor
    except ImportError:
        # 如果导入失败，可能会在运行时另行处理
        pass

from .base_chart import BaseChart, ChartType
from ..utils.logger import logger


class LayoutType(Enum):
    """布局类型枚举"""
    GRID = auto()  # 网格布局
    TABS = auto()  # 标签页布局
    FLOW = auto()  # 流式布局
    SPLITTER = auto()  # 分割器布局


class ChartContainer(QFrame):
    """
    图表容器
    
    封装单个图表的容器，添加标题栏和控制按钮。
    """
    
    # 信号：请求删除、最大化、恢复等
    delete_requested = pyqtSignal(str)  # 图表ID
    maximize_requested = pyqtSignal(str)  # 图表ID
    restore_requested = pyqtSignal(str)  # 图表ID
    
    def __init__(self, chart_id: str, chart: BaseChart, parent=None):
        """
        初始化图表容器
        
        Args:
            chart_id: 图表唯一标识符
            chart: 图表实例
            parent: 父控件
        """
        super().__init__(parent)
        
        self.chart_id = chart_id
        self.chart = chart
        self._is_maximized = False
        
        # 设置边框和样式
        self.setFrameShape(QFrame.StyledPanel)
        self.setFrameShadow(QFrame.Raised)
        self.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        
        # 创建布局
        layout = QVBoxLayout(self)
        layout.setContentsMargins(2, 2, 2, 2)
        layout.setSpacing(0)
        
        # 创建标题栏
        title_bar = QWidget()
        title_bar.setMinimumHeight(24)
        title_bar.setMaximumHeight(30)
        title_layout = QHBoxLayout(title_bar)
        title_layout.setContentsMargins(5, 0, 5, 0)
        
        # 添加标题文本
        title_label = QLabel(chart.title)
        title_layout.addWidget(title_label)
        
        # 添加控制按钮
        title_layout.addStretch()
        
        # 最大化/恢复按钮
        self.max_restore_btn = QPushButton("□")
        self.max_restore_btn.setFlat(True)
        self.max_restore_btn.setMaximumSize(24, 24)
        self.max_restore_btn.clicked.connect(self._toggle_maximize)
        title_layout.addWidget(self.max_restore_btn)
        
        # 关闭按钮
        close_btn = QPushButton("×")
        close_btn.setFlat(True)
        close_btn.setMaximumSize(24, 24)
        close_btn.clicked.connect(self._request_delete)
        title_layout.addWidget(close_btn)
        
        layout.addWidget(title_bar)
        
        # 添加图表
        chart.initialize(self)
        layout.addWidget(chart.widget)
        
        # 更新标题
        self.update_title(chart.title)
    
    def update_title(self, title: str):
        """更新标题"""
        self.chart.set_title(title)
        # 更新标题栏文本
        title_label = self.findChild(QLabel)
        if title_label:
            title_label.setText(title)
    
    def _toggle_maximize(self):
        """切换最大化/恢复状态"""
        if self._is_maximized:
            self.restore_requested.emit(self.chart_id)
            self.max_restore_btn.setText("□")
            self._is_maximized = False
        else:
            self.maximize_requested.emit(self.chart_id)
            self.max_restore_btn.setText("❐")
            self._is_maximized = True
    
    def _request_delete(self):
        """请求删除图表"""
        self.delete_requested.emit(self.chart_id)


class ChartManager(QWidget):
    """
    图表管理器
    
    管理多个图表实例，提供布局和操作功能。
    """
    
    def __init__(self, parent=None, layout_type: LayoutType = LayoutType.GRID):
        """
        初始化图表管理器
        
        Args:
            parent: 父控件
            layout_type: 布局类型
        """
        super().__init__(parent)
        
        self._charts = {}  # 类型: Dict[str, Tuple[BaseChart, ChartContainer]]
        self._layout_type = layout_type
        self._previous_layout = None  # 用于从最大化状态恢复
        self._log_prefix = "[ChartManager]"
        
        # 创建布局
        self._main_layout = QVBoxLayout(self)
        self._main_layout.setContentsMargins(0, 0, 0, 0)
        
        # 创建图表区域
        self._chart_area = QWidget()
        
        # 根据不同布局类型创建不同的布局管理器
        if layout_type == LayoutType.GRID:
            self._chart_layout = QGridLayout(self._chart_area)
            self._chart_layout.setContentsMargins(5, 5, 5, 5)
            self._chart_layout.setSpacing(10)
        elif layout_type == LayoutType.FLOW:
            self._chart_layout = QHBoxLayout(self._chart_area)
            self._chart_layout.setContentsMargins(5, 5, 5, 5)
            self._chart_layout.setSpacing(10)
        elif layout_type == LayoutType.SPLITTER:
            self._chart_layout = QVBoxLayout(self._chart_area)
            self._chart_layout.setContentsMargins(0, 0, 0, 0)
            self._chart_layout.setSpacing(0)
            self._splitter = QSplitter(Qt.Horizontal)
            self._chart_layout.addWidget(self._splitter)
        else:
            # 默认使用网格布局
            self._chart_layout = QGridLayout(self._chart_area)
            self._chart_layout.setContentsMargins(5, 5, 5, 5)
            self._chart_layout.setSpacing(10)
        
        self._main_layout.addWidget(self._chart_area)
        
        # 占位符标签（在没有图表时显示）
        self._placeholder = QLabel("暂无图表，请添加图表")
        self._placeholder.setAlignment(Qt.AlignCenter)
        self._chart_layout.addWidget(self._placeholder)
        
        logger.info(f"{self._log_prefix} 图表管理器初始化完成，布局类型: {layout_type.name}")
    
    def add_chart(self, chart: BaseChart, chart_id: Optional[str] = None) -> str:
        """
        添加图表
        
        Args:
            chart: 图表实例
            chart_id: 图表ID，如果为None则自动生成
            
        Returns:
            str: 图表ID
        """
        # 如果没有提供ID，则生成一个唯一ID
        if chart_id is None:
            chart_id = str(uuid.uuid4())
        
        # 确保ID唯一
        if chart_id in self._charts:
            logger.warning(f"{self._log_prefix} 图表ID已存在: {chart_id}，将生成新ID")
            chart_id = str(uuid.uuid4())
        
        # 如果有占位符，移除它
        if self._placeholder.parent():
            self._chart_layout.removeWidget(self._placeholder)
            self._placeholder.setParent(None)
        
        # 创建图表容器
        container = ChartContainer(chart_id, chart, self)
        container.delete_requested.connect(self.remove_chart)
        container.maximize_requested.connect(self._maximize_chart)
        container.restore_requested.connect(self._restore_layout)
        
        # 存储图表和容器
        self._charts[chart_id] = (chart, container)
        
        # 根据布局类型添加到布局中
        if self._layout_type == LayoutType.GRID:
            row, col = self._find_grid_position()
            self._chart_layout.addWidget(container, row, col)
        elif self._layout_type == LayoutType.FLOW:
            self._chart_layout.addWidget(container)
        elif self._layout_type == LayoutType.SPLITTER:
            self._splitter.addWidget(container)
        
        logger.info(f"{self._log_prefix} 添加图表: {chart_id}, 类型: {chart.chart_type.name}")
        return chart_id
    
    def _find_grid_position(self) -> Tuple[int, int]:
        """
        找到网格布局中的下一个可用位置
        
        Returns:
            Tuple[int, int]: (行, 列)
        """
        layout = cast(QGridLayout, self._chart_layout)
        count = len(self._charts)
        
        # 简单的网格布局策略
        cols = max(1, min(4, count // 2 + 1))  # 根据图表数量调整列数，最多4列
        row = count // cols
        col = count % cols
        
        return row, col
    
    def remove_chart(self, chart_id: str):
        """
        移除图表
        
        Args:
            chart_id: 图表ID
        """
        if chart_id in self._charts:
            chart, container = self._charts[chart_id]
            
            # 从布局中移除容器
            if self._layout_type == LayoutType.GRID:
                self._chart_layout.removeWidget(container)
            elif self._layout_type == LayoutType.FLOW:
                self._chart_layout.removeWidget(container)
            elif self._layout_type == LayoutType.SPLITTER:
                # 从分割器中移除
                container.setParent(None)
            
            # 销毁容器
            container.deleteLater()
            
            # 移除图表记录
            del self._charts[chart_id]
            
            logger.info(f"{self._log_prefix} 移除图表: {chart_id}")
            
            # 如果没有图表了，显示占位符
            if not self._charts:
                if self._layout_type == LayoutType.GRID:
                    self._chart_layout.addWidget(self._placeholder, 0, 0)
                elif self._layout_type == LayoutType.FLOW:
                    self._chart_layout.addWidget(self._placeholder)
                elif self._layout_type == LayoutType.SPLITTER:
                    # 移除旧的分割器
                    self._splitter.setParent(None)
                    # 创建新的分割器
                    self._splitter = QSplitter(Qt.Horizontal)
                    self._chart_layout.addWidget(self._splitter)
                    # 添加占位符
                    self._splitter.addWidget(self._placeholder)
        else:
            logger.warning(f"{self._log_prefix} 尝试移除不存在的图表: {chart_id}")
    
    def get_chart(self, chart_id: str) -> Optional[BaseChart]:
        """
        获取图表实例
        
        Args:
            chart_id: 图表ID
            
        Returns:
            Optional[BaseChart]: 图表实例，如果不存在则返回None
        """
        if chart_id in self._charts:
            return self._charts[chart_id][0]
        return None
    
    def get_chart_ids(self) -> List[str]:
        """
        获取所有图表ID
        
        Returns:
            List[str]: 图表ID列表
        """
        return list(self._charts.keys())
    
    def clear(self):
        """清除所有图表"""
        chart_ids = list(self._charts.keys())
        for chart_id in chart_ids:
            self.remove_chart(chart_id)
        logger.info(f"{self._log_prefix} 清除所有图表")
    
    def _maximize_chart(self, chart_id: str):
        """
        最大化图表
        
        Args:
            chart_id: 图表ID
        """
        if chart_id not in self._charts:
            return
        
        # 保存当前布局状态
        self._save_current_layout()
        
        # 获取图表容器
        _, container = self._charts[chart_id]
        
        # 隐藏所有其他图表
        for cid, (_, cont) in self._charts.items():
            if cid != chart_id:
                cont.setVisible(False)
        
        logger.info(f"{self._log_prefix} 最大化图表: {chart_id}")
    
    def _restore_layout(self, chart_id: str):
        """
        恢复布局
        
        Args:
            chart_id: 图表ID
        """
        # 显示所有图表
        for _, (_, container) in self._charts.items():
            container.setVisible(True)
        
        logger.info(f"{self._log_prefix} 恢复布局")
    
    def _save_current_layout(self):
        """保存当前布局状态，用于恢复"""
        # 根据布局类型保存不同的状态
        if self._layout_type == LayoutType.GRID:
            # 对于网格布局，可以保存每个图表的位置
            self._previous_layout = {}
            layout = cast(QGridLayout, self._chart_layout)
            for chart_id, (_, container) in self._charts.items():
                index = layout.indexOf(container)
                if index != -1:
                    row, col, rowSpan, colSpan = 0, 0, 0, 0
                    layout.getItemPosition(index, row, col, rowSpan, colSpan)
                    self._previous_layout[chart_id] = (row, col, rowSpan, colSpan)
        elif self._layout_type == LayoutType.SPLITTER:
            # 对于分割器布局，可以保存分割器的大小
            self._previous_layout = []
            for i in range(self._splitter.count()):
                self._previous_layout.append(self._splitter.sizes()[i])
    
    def _apply_layout(self, layout_config: Dict[str, Any]):
        """
        应用布局配置
        
        Args:
            layout_config: 布局配置
        """
        # 根据布局类型应用不同的配置
        if self._layout_type == LayoutType.GRID and isinstance(layout_config, dict):
            # 对于网格布局，恢复每个图表的位置
            layout = cast(QGridLayout, self._chart_layout)
            for chart_id, (row, col, rowSpan, colSpan) in layout_config.items():
                if chart_id in self._charts:
                    _, container = self._charts[chart_id]
                    # 首先从当前位置移除
                    layout.removeWidget(container)
                    # 然后添加到保存的位置
                    layout.addWidget(container, row, col, rowSpan, colSpan)
        elif self._layout_type == LayoutType.SPLITTER and isinstance(layout_config, list):
            # 对于分割器布局，恢复分割器的大小
            if len(layout_config) == self._splitter.count():
                self._splitter.setSizes(layout_config)
    
    def save_layout(self, file_path: str):
        """
        保存布局配置到文件
        
        Args:
            file_path: 文件路径
        """
        try:
            # 保存当前布局状态
            self._save_current_layout()
            
            # 创建配置字典
            config = {
                "layout_type": self._layout_type.name,
                "charts": {},
                "layout_data": self._previous_layout
            }
            
            # 保存每个图表的基本信息
            for chart_id, (chart, _) in self._charts.items():
                config["charts"][chart_id] = {
                    "title": chart.title,
                    "type": chart.chart_type.name,
                    "x_label": chart.x_label,
                    "y_label": chart.y_label
                }
            
            # 创建目录（如果不存在）
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            
            # 写入文件
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
            
            logger.info(f"{self._log_prefix} 布局配置已保存到: {file_path}")
            return True
        
        except Exception as e:
            logger.error(f"{self._log_prefix} 保存布局配置失败: {str(e)}")
            return False
    
    def set_layout_type(self, layout_type: LayoutType):
        """
        设置布局类型
        
        Args:
            layout_type: 布局类型
        """
        if self._layout_type == layout_type:
            return
        
        # 保存当前图表列表
        charts_backup = list(self._charts.items())
        
        # 清除所有图表
        self.clear()
        
        # 更改布局类型
        self._layout_type = layout_type
        
        # 移除旧布局
        if self._chart_area.layout():
            QWidget().setLayout(self._chart_area.layout())
        
        # 创建新布局
        if layout_type == LayoutType.GRID:
            self._chart_layout = QGridLayout(self._chart_area)
            self._chart_layout.setContentsMargins(5, 5, 5, 5)
            self._chart_layout.setSpacing(10)
        elif layout_type == LayoutType.FLOW:
            self._chart_layout = QHBoxLayout(self._chart_area)
            self._chart_layout.setContentsMargins(5, 5, 5, 5)
            self._chart_layout.setSpacing(10)
        elif layout_type == LayoutType.SPLITTER:
            self._chart_layout = QVBoxLayout(self._chart_area)
            self._chart_layout.setContentsMargins(0, 0, 0, 0)
            self._chart_layout.setSpacing(0)
            self._splitter = QSplitter(Qt.Horizontal)
            self._chart_layout.addWidget(self._splitter)
        
        # 添加占位符
        if not charts_backup:
            if layout_type == LayoutType.GRID:
                self._chart_layout.addWidget(self._placeholder, 0, 0)
            elif layout_type == LayoutType.FLOW:
                self._chart_layout.addWidget(self._placeholder)
            elif layout_type == LayoutType.SPLITTER:
                self._splitter.addWidget(self._placeholder)
        
        # 重新添加图表
        for chart_id, (chart, _) in charts_backup:
            self.add_chart(chart, chart_id)
        
        logger.info(f"{self._log_prefix} 更改布局类型为: {layout_type.name}")
    
    def get_layout_type(self) -> LayoutType:
        """获取当前布局类型"""
        return self._layout_type
