#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
图表初始化模块

负责初始化并将各种图表控件添加到UI中的指定位置
"""

import os
import sys
from typing import Optional, Dict, Any

try:
    # 根据实际导入的Qt库确定导入内容
    from PyQt5.QtCore import Qt, QObject, pyqtSlot
    from PyQt5.QtWidgets import QWidget, QVBoxLayout, QFrame
    from PyQt5.QtGui import QColor
except ImportError:
    try:
        from PySide6.QtCore import Qt, QObject
        from PySide6.QtWidgets import QWidget, QVBoxLayout, QFrame
        from PySide6.QtGui import QColor
        
        # PySide6兼容性处理
        pyqtSlot = lambda *args, **kwargs: lambda func: func
    except ImportError:
        # 运行时可能会另行处理
        pass

from .charts.time_series import TimeSeriesChart
from .charts.line_chart import LineChart
from ..utils.logger import logger


class ChartInitializer:
    """图表初始化器，负责创建和管理图表实例"""
    
    def __init__(self):
        """初始化图表初始化器"""
        self.charts = {}  # 存储所有创建的图表实例
        self.log_prefix = "[ChartInitializer]"
        
    def initialize_time_series_chart(self, parent_widget: QWidget) -> Optional[TimeSeriesChart]:
        """
        初始化时序图并添加到指定的父控件
        
        Args:
            parent_widget: 父控件，通常是UI中的可视化区域
            
        Returns:
            创建的时序图实例，如果失败则返回None
        """
        try:
            # 确保父控件布局为垂直布局
            layout = parent_widget.layout()
            if not layout:
                layout = QVBoxLayout(parent_widget)
                layout.setContentsMargins(0, 0, 0, 0)
                parent_widget.setLayout(layout)
            
            # 清除现有内容
            while layout.count():
                item = layout.takeAt(0)
                if item.widget():
                    item.widget().deleteLater()
            
            # 创建时序图
            time_series = TimeSeriesChart(
                title="实时数据监测",
                x_label="时间",
                y_label="数值"
            )
            time_series.initialize(parent_widget)
            
            # 获取图表控件并添加到布局
            chart_widget = time_series.widget
            if chart_widget:
                layout.addWidget(chart_widget)
                
                # 存储创建的图表实例
                self.charts['time_series'] = time_series
                logger.info(f"{self.log_prefix} 时序图初始化完成")
                return time_series
            else:
                logger.error(f"{self.log_prefix} 时序图控件创建失败")
                return None
                
        except Exception as e:
            logger.error(f"{self.log_prefix} 初始化时序图失败: {str(e)}")
            import traceback
            logger.debug(traceback.format_exc())
            return None
    
    def initialize_line_chart(self, parent_widget: QWidget) -> Optional[LineChart]:
        """
        初始化折线图并添加到指定的父控件
        
        Args:
            parent_widget: 父控件，通常是UI中的图表区域
            
        Returns:
            创建的折线图实例，如果失败则返回None
        """
        try:
            # 确保父控件布局为垂直布局
            layout = parent_widget.layout()
            if not layout:
                layout = QVBoxLayout(parent_widget)
                layout.setContentsMargins(0, 0, 0, 0)
                parent_widget.setLayout(layout)
            
            # 清除现有内容
            while layout.count():
                item = layout.takeAt(0)
                if item.widget():
                    item.widget().deleteLater()
            
            # 创建折线图
            line_chart = LineChart(
                title="数据分析",
                x_label="X轴",
                y_label="Y轴"
            )
            line_chart.initialize(parent_widget)
            
            # 获取图表控件并添加到布局
            chart_widget = line_chart.widget
            if chart_widget:
                layout.addWidget(chart_widget)
                
                # 存储创建的图表实例
                self.charts['line_chart'] = line_chart
                logger.info(f"{self.log_prefix} 折线图初始化完成")
                return line_chart
            else:
                logger.error(f"{self.log_prefix} 折线图控件创建失败")
                return None
                
        except Exception as e:
            logger.error(f"{self.log_prefix} 初始化折线图失败: {str(e)}")
            import traceback
            logger.debug(traceback.format_exc())
            return None
    
    def initialize_default_charts(self, main_window) -> bool:
        """
        初始化所有默认图表
        
        Args:
            main_window: 主窗口实例
            
        Returns:
            是否成功初始化所有图表
        """
        success = True
        
        try:
            # 初始化时序图（红色区域）
            if hasattr(main_window, 'visualizationFrame'):
                vis_frame = main_window.visualizationFrame
                time_series = self.initialize_time_series_chart(vis_frame)
                if not time_series:
                    success = False
                    
                # 不再添加示例数据
            
            # 初始化折线图（绿色区域）
            if hasattr(main_window, 'defaultChartPage'):
                chart_frame = main_window.defaultChartPage
                # 隐藏原有的占位符标签
                if hasattr(main_window, 'placeholderLabel'):
                    main_window.placeholderLabel.hide()
                
                line_chart = self.initialize_line_chart(chart_frame)
                if not line_chart:
                    success = False
                
                # 不再添加示例数据
            
            return success
            
        except Exception as e:
            logger.error(f"{self.log_prefix} 初始化默认图表失败: {str(e)}")
            import traceback
            logger.debug(traceback.format_exc())
            return False
    
    def get_chart(self, chart_id: str) -> Any:
        """
        获取指定ID的图表实例
        
        Args:
            chart_id: 图表ID
            
        Returns:
            图表实例，如果不存在则返回None
        """
        return self.charts.get(chart_id)


# 创建全局图表初始化器实例
chart_initializer = ChartInitializer() 