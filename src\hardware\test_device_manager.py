#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
设备管理器测试模块

测试设备管理器和串口协议的集成。
"""

import logging
import sys
import time
from typing import Any

# 配置日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)

# 导入设备管理器
try:
    from src.hardware.device_manager import device_manager
    from src.hardware.device_factory import DeviceType
    from src.hardware.protocols.serial_protocol import SerialProtocol
except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保当前目录是项目根目录，或者已正确设置PYTHONPATH。")
    sys.exit(1)

logger = logging.getLogger("DeviceManagerTest")

# 设备事件回调
def on_device_connected(device_id: str) -> None:
    """设备连接回调"""
    logger.info(f"设备已连接: {device_id}")

def on_device_disconnected(device_id: str) -> None:
    """设备断开连接回调"""
    logger.info(f"设备已断开连接: {device_id}")

def on_device_error(device_id: str, error: Any) -> None:
    """设备错误回调"""
    logger.error(f"设备错误: {device_id} - {error}")

def on_device_data_received(device_id: str, data: Any) -> None:
    """设备数据接收回调"""
    if isinstance(data, bytes):
        # 如果是字节数据，以十六进制显示
        hex_data = ' '.join([f'{b:02X}' for b in data])
        logger.info(f"收到数据 ({device_id}) (HEX): {hex_data}")
    else:
        logger.info(f"收到数据 ({device_id}): {data}")

def list_available_serial_ports() -> None:
    """列出可用的串口设备"""
    ports = SerialProtocol.list_available_ports()
    if not ports:
        print("未找到可用的串口设备")
        return
        
    print("\n可用串口设备:")
    print("-" * 80)
    print(f"{'端口名':<10} | {'描述':<40} | {'硬件ID':<30}")
    print("-" * 80)
    
    for port, desc, hwid in ports:
        print(f"{port:<10} | {desc[:40]:<40} | {hwid[:30]:<30}")

def test_serial_device() -> None:
    """测试串口设备"""
    # 列出可用串口
    list_available_serial_ports()
    
    # 用户选择端口
    port = input("\n请输入要使用的串口名称 (如 COM1 或 /dev/ttyUSB0): ")
    if not port:
        logger.error("未指定串口名称")
        return
    
    # 注册设备管理器信号
    device_manager.signals.device_connected.connect(on_device_connected)
    device_manager.signals.device_disconnected.connect(on_device_disconnected)
    device_manager.signals.device_error.connect(on_device_error)
    device_manager.signals.device_data_received.connect(on_device_data_received)
    
    # 创建串口设备
    config = {
        "port": port,
        "baudrate": 9600,
        "bytesize": 8,
        "parity": "N",
        "stopbits": 1,
        "timeout": 1.0,
        "read_mode": "line"
    }
    
    device_id = device_manager.create_device(DeviceType.SERIAL, config, "测试串口")
    if device_id is None:
        logger.error("创建串口设备失败")
        return
    
    try:
        # 连接设备
        logger.info(f"正在连接串口设备: {port}")
        if device_manager.connect_device(device_id):
            logger.info(f"成功连接到串口设备: {port}")
            
            # 发送测试命令
            command = input("\n请输入要发送的测试命令 (默认: AT\\r\\n): ")
            if not command:
                command = "AT\r\n"
                
            logger.info(f"发送命令: {command}")
            try:
                response = device_manager.send_command(device_id, command, wait_response=True, timeout=2.0)
                if response is not None:
                    logger.info(f"收到响应: {response}")
                else:
                    logger.warning("未收到响应")
            except Exception as e:
                logger.error(f"发送命令失败: {e}")
            
            # 持续接收数据一段时间
            duration = 10  # 秒
            logger.info(f"等待接收数据 ({duration} 秒)...")
            for i in range(duration):
                time.sleep(1)
                print(f"剩余时间: {duration - i} 秒", end="\r")
            print()
                
            # 断开连接
            if device_manager.disconnect_device(device_id):
                logger.info("成功断开串口设备连接")
            else:
                logger.error("断开串口设备连接失败")
                
        else:
            logger.error(f"连接串口设备失败: {port}")
            
    finally:
        # 移除设备
        device_manager.remove_device(device_id)
        logger.info("测试完成")

def main() -> None:
    """主函数"""
    print("\n==== 设备管理器测试程序 ====\n")
    
    print("1. 测试串口设备")
    print("2. 退出程序")
    
    choice = input("请选择操作: ")
    
    if choice == "1":
        test_serial_device()
    else:
        print("程序已退出")

if __name__ == "__main__":
    main() 