# X. 持续上下文与智能管理文档

**文档版本：** v1.0
**创建日期：** YYYY-MM-DD
**最后更新：** YYYY-MM-DD
**维护角色：** CM (上下文管理者)
**对应模式：** 模式X - 持续上下文管理 (CONTINUOUS_CONTEXT)
**工作模式：** 与模式1-5并行运行，为所有模式提供上下文增强支持

| 版本 | 最后更新时间 | 更新者 | 变更摘要 |
| :--- | :--- | :--- | :--- |
| 1.0  | YYYY-MM-DD HH:MM:SS | CM | 初始创建 |
|      |                     |    |          |

---

## 1. 上下文增强历史记录 (APEX-6模式X核心功能)

**角色切换声明：** `[INTERNAL_ACTION: Switching to CM via PromptX MCP for MODEX-CONTINUOUS_CONTEXT.]`

### 1.1 上下文增强会话记录
| 会话ID | 时间戳 | 用户问题 | 目标角色 | 上下文来源 | 增强效果 | 用户反馈 |
| --- | --- | --- | --- | --- | --- | --- |
| CTX-001 | YYYY-MM-DD HH:MM:SS | [用户问题摘要] | PM/BA/SA/PL/LD | [对话+文档+项目状态] | [效果评估] | [用户反馈] |
| CTX-002 | YYYY-MM-DD HH:MM:SS | [用户问题摘要] | PM/BA/SA/PL/LD | [对话+文档+项目状态] | [效果评估] | [用户反馈] |

### 1.2 详细上下文包存档

#### CTX-001 详细记录
**时间：** YYYY-MM-DD HH:MM:SS
**用户问题：** "[具体用户问题]"
**目标角色：** [角色名称和简称]
**上下文收集范围：**
- 当前模式：[当前模式名称]
- 前序模式：[相关前序模式列表]
- 对话历史：[分析的对话范围]

**收集的关键信息：**
- 从当前模式提取：[关键信息1]、[关键信息2]
- 从前序模式提取：[关键决策]、[约束条件]、[用户偏好]
- 从对话历史：[用户背景]、[技术偏好]、[团队情况]

**生成的上下文包：**
```markdown
## 🎯 上下文增强包 [CTX-001]

### 📋 当前对话上下文
**用户问题：** "[具体问题]"
**问题类型：** [问题分类]
**紧急程度：** [高/中/低]
**预期角色：** [目标角色]

### 📊 当前模式状态 [模式X: 模式名称]
**文档完整度：** [百分比] ([具体状态描述])
**关键进展：** [已完成的重要工作]
**待决策点：** [需要决策的关键问题]
**质量状态：** [质量评估结果]

### 🔗 前序模式关键信息
#### 模式Y影响 ([模式名称])
- **关键决策：** [重要决策内容]
- **约束条件：** [技术或业务约束]
- **用户偏好：** [用户明确表达的偏好]
- **技术要求：** [技术实现要求]

### 💡 智能分析建议
**关键考虑因素：**
1. [因素1] ([具体说明])
2. [因素2] ([具体说明])

**潜在风险提示：**
- [风险1及其影响]
- [风险2及其影响]

**建议决策路径：**
1. [建议步骤1]
2. [建议步骤2]
```

**传递效果：** [目标角色基于上下文的响应质量评估]
**用户反馈：** [用户对响应的满意度和具体反馈]

## 2. 模式文档深度分析 (APEX-6递进式工作流支持)

**文档读取声明：** `[INTERNAL_ACTION: Reading from Serena Memory - analyzing all mode documents in .serena\memories\ via Serena MCP.]`

### 2.1 当前模式状态分析
| 模式 | 文档完整度 | 关键信息 | 待完成任务 | 质量评估 | 最后更新 |
| --- | --- | --- | --- | --- | --- |
| 模式1 | [百分比] | [需求分析、开发规范] | [待完成任务] | [质量状态] | [更新时间] |
| 模式2 | [百分比] | [解决方案、用户沟通] | [待完成任务] | [质量状态] | [更新时间] |
| 模式3 | [百分比] | [技术栈、系统架构] | [待完成任务] | [质量状态] | [更新时间] |
| 模式4 | [百分比] | [项目规划、任务管理] | [待完成任务] | [质量状态] | [更新时间] |
| 模式5 | [百分比] | [开发进度、测试结果] | [待完成任务] | [质量状态] | [更新时间] |

### 2.2 前序模式影响分析 (递进依赖关系)
| 前序模式 | 关键决策 | 约束条件 | 当前影响 | 相关性 | 风险等级 |
| --- | --- | --- | --- | --- | --- |
| 模式1→2 | [需求确认决策] | [功能约束] | [对方案设计的影响] | [高/中/低] | [风险等级] |
| 模式2→3 | [方案选择决策] | [技术约束] | [对架构设计的影响] | [高/中/低] | [风险等级] |
| 模式3→4 | [技术选型决策] | [架构约束] | [对项目规划的影响] | [高/中/低] | [风险等级] |
| 模式4→5 | [规划决策] | [任务约束] | [对开发工作的影响] | [高/中/低] | [风险等级] |

## 3. 智能信息整合

### 3.1 信息关联图谱
```
用户问题 → 相关模式 → 关键决策 → 约束条件 → 建议方案
    ↓         ↓         ↓         ↓         ↓
[具体问题] → [模式X] → [决策Y] → [约束Z] → [建议A]
```

### 3.2 冲突检测记录
| 冲突ID | 冲突类型 | 涉及信息源 | 冲突描述 | 解决方案 | 状态 |
| --- | --- | --- | --- | --- | --- |
| CON-001 | [冲突类型] | [信息源1 vs 信息源2] | [冲突具体描述] | [解决方案] | [已解决/待解决] |

### 3.3 缺失信息清单
| 信息类别 | 缺失内容 | 重要程度 | 获取方式 | 预计时间 | 负责角色 |
| --- | --- | --- | --- | --- | --- |
| [信息类别] | [具体缺失内容] | [高/中/低] | [获取方法] | [预计时间] | [负责角色] |

## 4. 上下文包历史

### 4.1 生成的上下文包记录
| 包ID | 生成时间 | 目标角色 | 包大小 | 主要内容 | 使用效果 |
| --- | --- | --- | --- | --- | --- |
| PKG-001 | YYYY-MM-DD HH:MM:SS | [角色] | [信息量] | [主要内容摘要] | [效果评估] |

### 4.2 使用效果评估
| 评估维度 | 评分(1-5) | 具体表现 | 改进建议 |
| --- | --- | --- | --- |
| 信息完整性 | [评分] | [具体表现描述] | [改进建议] |
| 相关性准确度 | [评分] | [具体表现描述] | [改进建议] |
| 传递效率 | [评分] | [具体表现描述] | [改进建议] |
| 用户满意度 | [评分] | [具体表现描述] | [改进建议] |

### 4.3 优化建议记录
| 优化ID | 发现时间 | 问题描述 | 优化建议 | 实施状态 | 效果验证 |
| --- | --- | --- | --- | --- | --- |
| OPT-001 | YYYY-MM-DD | [问题描述] | [优化建议] | [实施状态] | [效果验证] |

## 5. 学习与优化

### 5.1 上下文收集效果分析
**成功案例分析：**
- **案例1：** [成功案例描述和关键成功因素]
- **案例2：** [成功案例描述和关键成功因素]

**失败案例分析：**
- **案例1：** [失败案例描述和失败原因分析]
- **案例2：** [失败案例描述和失败原因分析]

### 5.2 用户反馈整合
**正面反馈：**
- [用户正面反馈内容和频次]
- [从正面反馈中提取的优化方向]

**负面反馈：**
- [用户负面反馈内容和问题点]
- [针对负面反馈的改进措施]

### 5.3 策略优化记录
| 优化版本 | 优化时间 | 优化内容 | 预期效果 | 实际效果 | 下一步计划 |
| --- | --- | --- | --- | --- | --- |
| v1.1 | YYYY-MM-DD | [优化内容] | [预期效果] | [实际效果] | [下一步计划] |

## 6. 系统状态监控

### 6.1 性能指标
| 指标名称 | 当前值 | 目标值 | 趋势 | 状态 |
| --- | --- | --- | --- | --- |
| 平均响应时间 | [当前值] | [目标值] | [上升/下降/稳定] | [正常/警告/异常] |
| 上下文准确率 | [当前值] | [目标值] | [上升/下降/稳定] | [正常/警告/异常] |
| 用户满意度 | [当前值] | [目标值] | [上升/下降/稳定] | [正常/警告/异常] |

### 6.2 异常记录
| 异常ID | 发生时间 | 异常类型 | 异常描述 | 处理措施 | 状态 |
| --- | --- | --- | --- | --- | --- |
| EXC-001 | YYYY-MM-DD HH:MM:SS | [异常类型] | [异常描述] | [处理措施] | [已处理/处理中] |

---

## 7. 并行工作模式说明 (模式X特殊性)

### 7.1 并行运行机制
**工作模式：** 与模式1-5并行运行，为所有模式提供上下文增强支持
**激活时机：** 每次用户对话都会自动激活CM角色进行上下文收集
**工作流程：** 用户输入→CM角色激活→多维度上下文收集→深度分析整合→上下文包生成→目标角色切换→增强对话执行→上下文更新

### 7.2 与其他模式的协同
| 目标模式 | 提供的上下文增强 | 关键支持内容 | 效果评估 |
| --- | --- | --- | --- |
| 模式1 | 项目初始化上下文 | 用户背景、技术偏好、团队情况 | [效果评估] |
| 模式2 | 方案设计上下文 | 需求约束、用户关切、决策历史 | [效果评估] |
| 模式3 | 技术选型上下文 | 方案约束、性能要求、兼容性 | [效果评估] |
| 模式4 | 项目规划上下文 | 技术约束、资源限制、时间要求 | [效果评估] |
| 模式5 | 开发执行上下文 | 任务约束、质量标准、进度要求 | [效果评估] |

**文档维护说明：**
- 本文档由CM(上下文管理者)角色负责维护
- 每次上下文增强活动后必须更新相关记录
- 定期进行效果分析和策略优化
- 重要变更需要记录详细的变更原因和影响分析
- **文档更新声明：** `[INTERNAL_ACTION: Writing to Serena Memory - .serena\memories\X.Continuous Context and Intelligence Document.md via Serena MCP with mcp.server_time timestamp based on context enhancement activity.]`

**并行关系说明：** 本文档作为模式X的产出，与模式1-5并行运行，为所有模式提供上下文增强支持，确保AI响应基于完整的项目历史和当前状态。
