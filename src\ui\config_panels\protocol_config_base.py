#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
协议配置面板基类

为不同类型的协议配置面板提供统一的接口。
"""

from typing import Any, Dict, Optional

try:
    from PyQt5.QtWidgets import QWidget
    from PyQt5.QtCore import pyqtSignal as Signal
except ImportError:
    try:
        from PySide6.QtWidgets import QWidget
        from PySide6.QtCore import Signal
    except ImportError:
        raise ImportError("未安装PyQt5或PySide6，请安装其中之一")


class ProtocolConfigPanelBase(QWidget):
    """
    协议配置面板基类
    
    为所有协议配置面板提供统一的接口。
    """
    
    # 信号：配置已更改
    config_changed = Signal()
    
    def __init__(self, parent=None):
        """初始化协议配置面板基类"""
        super().__init__(parent)
        
        # 协议名称
        self._protocol_name = "未命名协议"
        
        # 协议描述
        self._protocol_description = "协议描述"
        
        # 当前配置
        self._config = {}
    
    @property
    def protocol_name(self) -> str:
        """获取协议名称"""
        return self._protocol_name
    
    @property
    def protocol_description(self) -> str:
        """获取协议描述"""
        return self._protocol_description
    
    def get_config(self) -> Dict[str, Any]:
        """
        获取当前配置
        
        Returns:
            当前协议配置字典
        """
        return self._config.copy()
    
    def set_config(self, config: Dict[str, Any]) -> None:
        """
        设置当前配置
        
        Args:
            config: 协议配置字典
        """
        self._config = config.copy()
        self._update_ui_from_config()
    
    def load_defaults(self) -> None:
        """加载默认配置"""
        raise NotImplementedError("子类必须实现load_defaults方法")
    
    def validate_config(self) -> bool:
        """
        验证当前配置
        
        Returns:
            配置是否有效
        """
        raise NotImplementedError("子类必须实现validate_config方法")
    
    def _update_ui_from_config(self) -> None:
        """根据配置更新UI控件"""
        raise NotImplementedError("子类必须实现_update_ui_from_config方法")
    
    def _update_config_from_ui(self) -> None:
        """从UI控件更新配置"""
        raise NotImplementedError("子类必须实现_update_config_from_ui方法") 