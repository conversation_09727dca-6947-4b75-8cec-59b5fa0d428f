#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
配置管理模块

该模块负责加载、保存和管理应用程序的配置。
使用JSON格式存储配置，支持默认配置和用户配置。
"""

import os
import json
import logging
from typing import Any, Dict, Optional

class Config:
    """配置管理类，使用单例模式确保全局只有一个配置实例"""
    
    _instance = None

    @classmethod
    def get_instance(cls):
        """获取配置管理的单例实例"""
        if cls._instance is None:
            cls._instance = cls()  # 这会调用 __new__ 和 __init__ 创建实例
        return cls._instance
    
    def __new__(cls):
        """实现单例模式"""
        if cls._instance is None:
            cls._instance = super(Config, cls).__new__(cls)
            cls._instance._initialized = False
        return cls._instance
    
    def __init__(self):
        """初始化配置管理器"""
        if self._initialized:
            return
            
        self._initialized = True
        self._config_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'config')
        self._default_config_path = os.path.join(self._config_dir, 'default_settings.json')
        self._user_config_path = os.path.join(self._config_dir, 'user_settings.json')
        self._config = {}
        
        # 确保配置目录存在
        os.makedirs(self._config_dir, exist_ok=True)
        
        # 加载配置
        self.load_config()
        
    def load_config(self) -> None:
        """加载配置文件，首先加载默认配置，然后加载用户配置进行覆盖"""
        # 加载默认配置
        default_config = self._load_json_file(self._default_config_path)
        if not default_config:
            default_config = self._create_default_config()
            
        # 加载用户配置
        user_config = self._load_json_file(self._user_config_path)
        
        # 合并配置
        self._config = default_config
        if user_config:
            self._deep_update(self._config, user_config)
    
    def _create_default_config(self) -> Dict[str, Any]:
        """创建默认配置并保存到文件"""
        default_config = {
            "app": {
                "name": "Data-Show",
                "version": "0.1.0",
                "theme": "light",
                "language": "zh_CN",
                "log_level": "INFO"
            },
            "ui": {
                "window_width": 1280,
                "window_height": 800,
                "font_size": 12,
                "show_toolbar": True,
                "show_statusbar": True
            },
            "visualization": {
                "default_chart_type": "line",
                "update_interval": 100,  # ms
                "max_data_points": 1000
            },
            "protocol": {
                "serial": {
                    "baudrate": 9600,
                    "bytesize": 8,
                    "parity": "N",
                    "stopbits": 1,
                    "timeout": 1
                },
                "modbus": {
                    "timeout": 3,
                    "retries": 3
                },
                "tcp_ip": {
                    "buffer_size": 1024,
                    "timeout": 5
                },
                "mqtt": {
                    "keep_alive": 60
                }
            },
            "data": {
                "storage_type": "sqlite",
                "db_path": "./data/data.db",
                "auto_save": True,
                "save_interval": 60  # seconds
            }
        }
        
        # 保存默认配置到文件
        self._save_json_file(self._default_config_path, default_config)
        return default_config
    
    def _load_json_file(self, file_path: str) -> Optional[Dict[str, Any]]:
        """从JSON文件加载配置"""
        if not os.path.exists(file_path):
            return None
            
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except (json.JSONDecodeError, IOError) as e:
            logging.error(f"无法加载配置文件 {file_path}: {str(e)}")
            return None
    
    def _save_json_file(self, file_path: str, data: Dict[str, Any]) -> bool:
        """保存配置到JSON文件"""
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=4)
            return True
        except IOError as e:
            logging.error(f"无法保存配置到文件 {file_path}: {str(e)}")
            return False
    
    def _deep_update(self, d: Dict[str, Any], u: Dict[str, Any]) -> Dict[str, Any]:
        """递归更新嵌套字典"""
        for k, v in u.items():
            if isinstance(v, dict) and k in d and isinstance(d[k], dict):
                self._deep_update(d[k], v)
            else:
                d[k] = v
        return d
    
    def get(self, key_path: str, default: Any = None) -> Any:
        """
        获取配置项值
        
        Args:
            key_path: 配置项路径，使用点号分隔，如 "app.theme"
            default: 如果配置项不存在，返回的默认值
            
        Returns:
            配置项的值
        """
        keys = key_path.split('.')
        value = self._config
        
        for key in keys:
            if isinstance(value, dict) and key in value:
                value = value[key]
            else:
                return default
                
        return value
    
    def set(self, key_path: str, value: Any) -> None:
        """
        设置配置项值
        
        Args:
            key_path: 配置项路径，使用点号分隔，如 "app.theme"
            value: 要设置的值
        """
        keys = key_path.split('.')
        config = self._config
        
        # 导航到最后一级的父级
        for key in keys[:-1]:
            if key not in config:
                config[key] = {}
            config = config[key]
            
        # 设置值
        config[keys[-1]] = value
    
    def save(self) -> bool:
        """保存当前配置到用户配置文件"""
        return self._save_json_file(self._user_config_path, self._config)
    
    def reset(self) -> None:
        """重置为默认配置"""
        if os.path.exists(self._user_config_path):
            os.remove(self._user_config_path)
        self.load_config()
    
    @property
    def all(self) -> Dict[str, Any]:
        """获取所有配置项"""
        return self._config.copy()
