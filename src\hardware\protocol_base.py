#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
硬件协议基类模块

定义所有硬件协议适配器的接口和共同行为，实现基础的事件机制和错误处理。
"""

import logging
import threading
import time
from abc import ABC, abstractmethod
from enum import Enum, auto
from typing import Any, Callable, Dict, List, Optional, Tuple, Union

try:
    from PyQt5.QtCore import QObject, pyqtSignal
except ImportError:
    try:
        from PySide6.QtCore import QObject, Signal as pyqtSignal
    except ImportError:
        # 如果没有安装Qt库，则创建一个简单的替代类
        class QObject:
            pass
        
        class PyQtSignalMock:
            def __init__(self, *args):
                pass
                
            def emit(self, *args):
                pass
                
            def connect(self, func):
                pass
                
        # 将模拟类赋值给pyqtSignal
        pyqtSignal = PyQtSignalMock

# 获取logger
logger = logging.getLogger(__name__)

class ProtocolEventType(Enum):
    """协议事件类型枚举"""
    CONNECTED = auto()        # 连接成功
    DISCONNECTED = auto()     # 断开连接
    DATA_RECEIVED = auto()    # 接收到数据
    ERROR = auto()            # 发生错误
    TIMEOUT = auto()          # 通信超时
    COMMAND_SENT = auto()     # 命令已发送
    STATE_CHANGED = auto()    # 状态改变


class ProtocolError(Exception):
    """协议错误基类"""
    def __init__(self, message: str, error_code: int = 0, details: Dict = None):
        self.message = message
        self.error_code = error_code
        self.details = details or {}
        super().__init__(message)
        
    def __str__(self):
        return f"{self.message} (Code: {self.error_code})"


class ConnectionError(ProtocolError):
    """连接错误"""
    pass


class TimeoutError(ProtocolError):
    """超时错误"""
    pass


class CommandError(ProtocolError):
    """命令错误"""
    pass


class ProtocolSignals(QObject):
    """协议信号类，用于Qt事件通知"""
    # 定义信号
    data_received = pyqtSignal(object)  # 数据接收信号
    connected = pyqtSignal()            # 连接建立信号
    disconnected = pyqtSignal()         # 连接断开信号
    error_occurred = pyqtSignal(object) # 错误发生信号
    state_changed = pyqtSignal(str)     # 状态改变信号


class ProtocolBase(ABC):
    """
    协议基类
    
    定义所有硬件协议适配器应遵循的接口，提供基础的事件处理和错误管理功能。
    子类应该实现所有抽象方法以提供具体的协议实现。
    """
    
    def __init__(self, protocol_name: str):
        """
        初始化协议基类
        
        Args:
            protocol_name: 协议名称
        """
        self._protocol_name = protocol_name
        self._is_connected = False
        self._is_running = False
        self._lock = threading.RLock()  # 重入锁，保证线程安全
        self._event_callbacks = {event_type: [] for event_type in ProtocolEventType}
        self._read_thread = None
        self._config = {}  # 协议配置
        self._signals = ProtocolSignals()  # Qt信号对象
        
        # 连接信号到事件处理
        self._signals.data_received.connect(
            lambda data: self._handle_event(ProtocolEventType.DATA_RECEIVED, data)
        )
        self._signals.connected.connect(
            lambda: self._handle_event(ProtocolEventType.CONNECTED)
        )
        self._signals.disconnected.connect(
            lambda: self._handle_event(ProtocolEventType.DISCONNECTED)
        )
        self._signals.error_occurred.connect(
            lambda error: self._handle_event(ProtocolEventType.ERROR, error)
        )
        self._signals.state_changed.connect(
            lambda state: self._handle_event(ProtocolEventType.STATE_CHANGED, state)
        )
        
        logger.debug(f"初始化协议适配器: {protocol_name}")
    
    @property
    def name(self) -> str:
        """获取协议名称"""
        return self._protocol_name
    
    @property
    def is_connected(self) -> bool:
        """获取连接状态"""
        return self._is_connected
    
    @property
    def config(self) -> Dict:
        """获取协议配置"""
        return self._config.copy()  # 返回配置的副本，避免外部修改
    
    @property
    def signals(self) -> ProtocolSignals:
        """获取信号对象，用于Qt信号槽连接"""
        return self._signals
    
    def set_config(self, config: Dict) -> None:
        """
        设置协议配置
        
        Args:
            config: 协议配置字典
        """
        with self._lock:
            # 更新配置（仅在未连接时）
            if self._is_connected:
                logger.warning(f"{self.name}: 无法在连接状态下更改配置，请先断开连接")
                return
                
            # 验证配置
            try:
                self._validate_config(config)
                self._config.update(config)
                logger.debug(f"{self.name}: 配置已更新")
            except Exception as e:
                logger.error(f"{self.name}: 配置验证失败: {str(e)}")
                raise
    
    def register_event_callback(self, event_type: ProtocolEventType, 
                               callback: Callable) -> None:
        """
        注册事件回调函数
        
        Args:
            event_type: 事件类型
            callback: 回调函数
        """
        with self._lock:
            if callback not in self._event_callbacks[event_type]:
                self._event_callbacks[event_type].append(callback)
                logger.debug(f"{self.name}: 已注册事件回调: {event_type.name}")
    
    def unregister_event_callback(self, event_type: ProtocolEventType, 
                                 callback: Callable) -> None:
        """
        取消注册事件回调函数
        
        Args:
            event_type: 事件类型
            callback: 回调函数
        """
        with self._lock:
            if callback in self._event_callbacks[event_type]:
                self._event_callbacks[event_type].remove(callback)
                logger.debug(f"{self.name}: 已取消事件回调: {event_type.name}")
    
    def _handle_event(self, event_type: ProtocolEventType, data: Any = None) -> None:
        """
        处理事件
        
        Args:
            event_type: 事件类型
            data: 事件数据
        """
        with self._lock:
            callbacks = self._event_callbacks[event_type].copy()
        
        # 在锁外执行回调，避免死锁
        for callback in callbacks:
            try:
                if data is not None:
                    callback(data)
                else:
                    callback()
            except Exception as e:
                logger.error(f"{self.name}: 事件回调执行失败: {str(e)}")
    
    def _start_read_thread(self) -> None:
        """启动读取线程"""
        if self._read_thread is None or not self._read_thread.is_alive():
            self._is_running = True
            self._read_thread = threading.Thread(
                target=self._read_loop,
                name=f"{self.name}-ReadThread",
                daemon=True  # 设置为守护线程，主线程结束时自动结束
            )
            self._read_thread.start()
            logger.debug(f"{self.name}: 读取线程已启动")
    
    def _stop_read_thread(self) -> None:
        """停止读取线程"""
        self._is_running = False
        if self._read_thread and self._read_thread.is_alive():
            # 等待线程结束，最多等待1秒
            self._read_thread.join(1.0)
            if self._read_thread.is_alive():
                logger.warning(f"{self.name}: 读取线程未能正常结束")
            else:
                logger.debug(f"{self.name}: 读取线程已停止")
    
    def start_reading(self) -> bool:
        """
        开始读取数据线程
        
        Returns:
            是否成功启动读取线程
        """
        if not self._is_connected:
            logger.warning(f"{self.name}: 设备未连接，无法开始读取数据")
            return False
        
        if self._is_running:
            logger.warning(f"{self.name}: 数据读取线程已经在运行")
            return True
        
        try:
            self._start_read_thread()
            logger.info(f"{self.name}: 成功启动数据读取")
            return True
        except Exception as e:
            logger.error(f"{self.name}: 启动数据读取线程失败: {str(e)}")
            return False
    
    def stop_reading(self) -> bool:
        """
        停止读取数据线程
        
        Returns:
            是否成功停止读取线程
        """
        if not self._is_running:
            logger.warning(f"{self.name}: 数据读取线程未在运行")
            return True
        
        try:
            self._stop_read_thread()
            logger.info(f"{self.name}: 成功停止数据读取")
            return True
        except Exception as e:
            logger.error(f"{self.name}: 停止数据读取线程失败: {str(e)}")
            return False
    
    @abstractmethod
    def _read_loop(self) -> None:
        """
        读取循环，在单独线程中运行
        
        子类应该实现此方法，处理数据读取逻辑
        """
        pass
    
    @abstractmethod
    def _validate_config(self, config: Dict) -> None:
        """
        验证配置
        
        Args:
            config: 要验证的配置字典
            
        Raises:
            ValueError: 如果配置无效
        """
        pass
    
    @abstractmethod
    def connect(self) -> bool:
        """
        连接到硬件
        
        Returns:
            连接是否成功
            
        Raises:
            ConnectionError: 连接失败时抛出
        """
        pass
    
    @abstractmethod
    def disconnect(self) -> bool:
        """
        断开与硬件的连接
        
        Returns:
            断开连接是否成功
        """
        pass
    
    @abstractmethod
    def read_data(self, timeout: float = 1.0) -> Any:
        """
        读取数据（同步）
        
        Args:
            timeout: 超时时间（秒）
            
        Returns:
            读取到的数据
            
        Raises:
            TimeoutError: 读取超时时抛出
        """
        pass
    
    @abstractmethod
    def send_command(self, command: Any, wait_response: bool = False, 
                    timeout: float = 1.0) -> Optional[Any]:
        """
        发送命令
        
        Args:
            command: 要发送的命令
            wait_response: 是否等待响应
            timeout: 等待响应的超时时间（秒）
            
        Returns:
            如果wait_response为True，返回响应数据；否则返回None
            
        Raises:
            CommandError: 命令发送失败时抛出
            TimeoutError: 等待响应超时时抛出
        """
        pass
    
    def __str__(self) -> str:
        """返回协议的字符串表示"""
        status = "已连接" if self._is_connected else "未连接"
        return f"{self._protocol_name} ({status})" 