#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
雷达图模块

实现基于PyQtGraph的雷达图，支持多组数据、自定义样式和交互等功能。
"""

import numpy as np
import math
from typing import Dict, List, Optional, Tuple, Union, Any

try:
    import pyqtgraph as pg
    from pyqtgraph.Qt import QtCore, QtGui
except ImportError:
    # 如果导入失败，可能会在运行时另行处理
    pass

from ..base_chart import BaseChart, ChartType
from ...utils.logger import logger


class RadarPlotItem(pg.GraphicsObject):
    """雷达图项目"""
    
    def __init__(
        self, 
        values: List[float], 
        labels: List[str], 
        scale: float = 1.0,
        color: Tuple[int, int, int] = (0, 0, 255),
        fill_color: Optional[Tuple[int, int, int, int]] = None,
        line_width: float = 2.0,
        symbol: str = None,
        symbol_size: int = 10,
        name: str = ""
    ):
        """
        初始化雷达图项目
        
        Args:
            values: 数据值列表
            labels: 对应的标签列表
            scale: 缩放比例
            color: 线条颜色
            fill_color: 填充颜色，默认为透明
            line_width: 线条宽度
            symbol: 节点符号，如 'o', 't', '+', 'd', 's' 等
            symbol_size: 节点大小
            name: 名称
        """
        super().__init__()
        
        self.values = values
        self.labels = labels
        self.scale = scale
        self.color = color
        self.line_width = line_width
        self.symbol = symbol
        self.symbol_size = symbol_size
        self.name = name
        
        # 设置填充颜色
        if fill_color is None:
            # 默认使用半透明的线条颜色
            self.fill_color = (*color, 50)  # 透明度50
        else:
            self.fill_color = fill_color
        
        # 创建画笔和画刷
        self.pen = pg.mkPen(color=color, width=line_width)
        self.fill_brush = pg.mkBrush(color=self.fill_color)
        
        # 计算路径和点
        self.polygon_path = None
        self.points = []
        self._calculate_polygon()
    
    def _calculate_polygon(self):
        """计算多边形路径"""
        # 获取维度数量
        n_dims = len(self.values)
        if n_dims < 3:
            # 至少需要3个维度才能绘制雷达图
            raise ValueError("雷达图至少需要3个维度")
        
        # 计算每个轴的角度
        angles = [i * 2 * np.pi / n_dims for i in range(n_dims)]
        
        # 计算多边形的顶点坐标
        self.points = []
        for i, value in enumerate(self.values):
            # 标准化到0-1范围
            normalized_value = value * self.scale
            
            # 计算坐标
            x = normalized_value * np.cos(angles[i])
            y = normalized_value * np.sin(angles[i])
            
            self.points.append((x, y))
        
        # 创建多边形路径
        self.polygon_path = QtGui.QPainterPath()
        self.polygon_path.moveTo(self.points[0][0], self.points[0][1])
        for x, y in self.points[1:]:
            self.polygon_path.lineTo(x, y)
        self.polygon_path.closeSubpath()
    
    def boundingRect(self):
        """获取包围盒"""
        # 确保多边形路径已计算
        if self.polygon_path is None:
            self._calculate_polygon()
        
        # 获取路径的包围盒并添加一些边距
        rect = self.polygon_path.boundingRect()
        padding = max(self.line_width, self.symbol_size if self.symbol else 0) + 2
        return rect.adjusted(-padding, -padding, padding, padding)
    
    def paint(self, painter, option, widget=None):
        """绘制雷达图"""
        # 确保多边形路径已计算
        if self.polygon_path is None:
            self._calculate_polygon()
        
        # 设置抗锯齿
        painter.setRenderHint(QtGui.QPainter.Antialiasing)
        
        # 绘制填充区域
        painter.setBrush(self.fill_brush)
        painter.setPen(QtCore.Qt.NoPen)
        painter.drawPath(self.polygon_path)
        
        # 绘制轮廓线
        painter.setBrush(QtCore.Qt.NoBrush)
        painter.setPen(self.pen)
        painter.drawPath(self.polygon_path)
        
        # 绘制节点符号
        if self.symbol:
            sym_size = self.symbol_size
            half_size = sym_size / 2
            
            # 为不同的符号创建不同的路径
            sym_path = QtGui.QPainterPath()
            
            if self.symbol == 'o':  # 圆形
                sym_path.addEllipse(QtCore.QRectF(-half_size, -half_size, sym_size, sym_size))
            elif self.symbol == 's':  # 正方形
                sym_path.addRect(QtCore.QRectF(-half_size, -half_size, sym_size, sym_size))
            elif self.symbol == 'd':  # 菱形
                sym_path.moveTo(0, -half_size)
                sym_path.lineTo(half_size, 0)
                sym_path.lineTo(0, half_size)
                sym_path.lineTo(-half_size, 0)
                sym_path.closeSubpath()
            elif self.symbol == '+':  # 加号
                sym_path.moveTo(-half_size, 0)
                sym_path.lineTo(half_size, 0)
                sym_path.moveTo(0, -half_size)
                sym_path.lineTo(0, half_size)
            elif self.symbol == 't':  # 三角形
                sym_path.moveTo(0, -half_size)
                sym_path.lineTo(half_size, half_size)
                sym_path.lineTo(-half_size, half_size)
                sym_path.closeSubpath()
            else:  # 默认圆形
                sym_path.addEllipse(QtCore.QRectF(-half_size, -half_size, sym_size, sym_size))
            
            # 设置节点画刷
            painter.setBrush(pg.mkBrush(self.color))
            
            # 在每个顶点绘制符号
            for x, y in self.points:
                painter.save()
                painter.translate(x, y)
                painter.drawPath(sym_path)
                painter.restore()


class RadarAxisItem(pg.GraphicsObject):
    """雷达图轴项目"""
    
    def __init__(
        self, 
        labels: List[str], 
        max_value: float = 1.0,
        n_circles: int = 4,
        axis_color: Tuple[int, int, int] = (100, 100, 100),
        grid_color: Tuple[int, int, int] = (200, 200, 200),
        text_color: Tuple[int, int, int] = (0, 0, 0),
        label_offset: float = 0.1,
        grid_line_width: float = 1.0,
        axis_line_width: float = 1.5
    ):
        """
        初始化雷达图轴
        
        Args:
            labels: 各轴的标签列表
            max_value: 最大值
            n_circles: 同心圆数量
            axis_color: 轴线颜色
            grid_color: 网格线颜色
            text_color: 文本颜色
            label_offset: 标签偏移量
            grid_line_width: 网格线宽度
            axis_line_width: 轴线宽度
        """
        super().__init__()
        
        self.labels = labels
        self.max_value = max_value
        self.n_circles = n_circles
        self.label_offset = label_offset
        
        # 创建画笔
        self.axis_pen = pg.mkPen(color=axis_color, width=axis_line_width)
        self.grid_pen = pg.mkPen(color=grid_color, width=grid_line_width, style=QtCore.Qt.DashLine)
        self.text_color = text_color
        
        # 计算轴线和网格线
        self.axis_lines = []
        self.grid_circles = []
        self._calculate_axes()
    
    def _calculate_axes(self):
        """计算轴线和网格线"""
        # 获取维度数量
        n_dims = len(self.labels)
        if n_dims < 3:
            # 至少需要3个维度才能绘制雷达图
            raise ValueError("雷达图至少需要3个维度")
        
        # 计算每个轴的角度
        angles = [i * 2 * np.pi / n_dims for i in range(n_dims)]
        
        # 计算轴线
        self.axis_lines = []
        for angle in angles:
            # 轴延伸到稍微超过最大值的位置，以便绘制标签
            x = (self.max_value + self.label_offset) * np.cos(angle)
            y = (self.max_value + self.label_offset) * np.sin(angle)
            self.axis_lines.append(((0, 0), (x, y)))
        
        # 计算同心圆
        self.grid_circles = []
        for i in range(1, self.n_circles + 1):
            radius = self.max_value * i / self.n_circles
            self.grid_circles.append(radius)
    
    def boundingRect(self):
        """获取包围盒"""
        # 计算包含所有元素的矩形
        max_extent = self.max_value + self.label_offset + 0.2  # 添加标签的空间
        return QtCore.QRectF(-max_extent, -max_extent, 2 * max_extent, 2 * max_extent)
    
    def paint(self, painter, option, widget=None):
        """绘制雷达图轴"""
        # 设置抗锯齿
        painter.setRenderHint(QtGui.QPainter.Antialiasing)
        
        # 绘制同心圆网格
        painter.setPen(self.grid_pen)
        for radius in self.grid_circles:
            painter.drawEllipse(QtCore.QPointF(0, 0), radius, radius)
        
        # 绘制轴线
        painter.setPen(self.axis_pen)
        for start, end in self.axis_lines:
            painter.drawLine(QtCore.QPointF(*start), QtCore.QPointF(*end))
        
        # 绘制标签
        n_dims = len(self.labels)
        angles = [i * 2 * np.pi / n_dims for i in range(n_dims)]
        
        font = painter.font()
        font.setBold(True)
        painter.setFont(font)
        
        for i, label in enumerate(self.labels):
            angle = angles[i]
            
            # 计算标签位置
            label_radius = self.max_value + self.label_offset
            x = label_radius * np.cos(angle)
            y = label_radius * np.sin(angle)
            
            # 根据角度调整文本对齐方式
            flags = QtCore.Qt.AlignCenter
            if abs(np.cos(angle)) > 0.7:
                flags = QtCore.Qt.AlignHCenter | (QtCore.Qt.AlignTop if np.cos(angle) < 0 else QtCore.Qt.AlignBottom)
            elif abs(np.sin(angle)) > 0.7:
                flags = QtCore.Qt.AlignVCenter | (QtCore.Qt.AlignRight if np.sin(angle) < 0 else QtCore.Qt.AlignLeft)
            
            # 创建文本显示矩形
            text_rect = QtCore.QRectF(x - 50, y - 15, 100, 30)
            if np.cos(angle) < 0:
                text_rect.moveLeft(x - 100)
            if np.sin(angle) < 0:
                text_rect.moveTop(y - 30)
            
            # 设置文本颜色
            painter.setPen(pg.mkPen(self.text_color))
            
            # 绘制标签文本
            painter.drawText(text_rect, flags, label)
        
        # 绘制数值标签
        painter.setPen(pg.mkPen(self.text_color))
        for i, radius in enumerate(self.grid_circles):
            value = self.max_value * (i + 1) / self.n_circles
            value_str = str(round(value, 2))
            painter.drawText(QtCore.QPointF(radius + 5, 0), value_str)


class RadarChart(BaseChart):
    """
    雷达图类
    
    实现雷达图的创建、数据管理和样式设置等功能。
    """
    
    def __init__(
        self, 
        title: str = "雷达图", 
        x_label: str = "", 
        y_label: str = ""
    ):
        """
        初始化雷达图
        
        Args:
            title: 图表标题
            x_label: X轴标签（不显示）
            y_label: Y轴标签（不显示）
        """
        super().__init__(title, x_label, y_label, ChartType.RADAR)
        
        # 雷达图特有的属性
        self._radar_items = {}  # 存储雷达图项目
        self._axis_item = None  # 雷达图轴
        self._categories = []  # 类别名称
        self._max_value = 1.0  # 最大值
        self._auto_scale = True  # 自动调整比例
        self._fill_alpha = 50  # 填充透明度
        self._n_circles = 4  # 同心圆数量
        self._legend_visible = True  # 图例是否可见
    
    def _initialize_specific(self):
        """实现图表特定的初始化"""
        # 雷达图不需要坐标轴
        self._plot_item.getAxis('bottom').hide()
        self._plot_item.getAxis('left').hide()
        
        # 设置等比例显示
        self._plot_item.setAspectLocked(True)
        
        # 设置图例
        if hasattr(self._plot_item, 'addLegend'):
            self._plot_item.addLegend()
    
    def set_categories(self, categories: List[str]):
        """
        设置类别标签
        
        Args:
            categories: 类别名称列表
        """
        self._categories = categories
        
        if not self._initialized:
            return
        
        # 更新雷达图轴
        self._update_axis()
    
    def set_max_value(self, max_value: float, auto_scale: bool = False):
        """
        设置最大值和是否自动缩放
        
        Args:
            max_value: 最大值
            auto_scale: 是否自动缩放
        """
        self._max_value = max_value
        self._auto_scale = auto_scale
        
        if not self._initialized:
            return
        
        # 更新雷达图轴
        self._update_axis()
        
        # 更新所有雷达图项目
        if not auto_scale:
            for radar_item in self._radar_items.values():
                radar_item.scale = 1.0  # 重置缩放比例
    
    def _update_axis(self):
        """更新雷达图轴"""
        if not self._categories:
            return
        
        # 移除旧的轴
        if self._axis_item is not None:
            self._plot_item.removeItem(self._axis_item)
        
        # 创建新的轴
        self._axis_item = RadarAxisItem(
            labels=self._categories,
            max_value=self._max_value,
            n_circles=self._n_circles
        )
        
        # 添加到绘图项
        self._plot_item.addItem(self._axis_item)
    
    def add_data(
        self, 
        data_id: str, 
        data: Union[List[float], Dict[str, float]], 
        **kwargs
    ):
        """
        添加数据到雷达图
        
        Args:
            data_id: 数据标识符
            data: 数据内容，可以是数据列表或字典 {类别: 数值}
            **kwargs: 额外配置参数，包括：
                - name: 数据系列名称（显示在图例中）
                - color: 线条颜色，格式为 (R,G,B) 或 '#RRGGBB'
                - fill_color: 填充颜色，格式为 (R,G,B,A) 或 '#RRGGBB'
                - line_width: 线条宽度，默认为2
                - symbol: 节点符号，如 'o', 't', '+', 'd', 's' 等
                - symbol_size: 节点大小，默认为10
                - categories: 类别标签列表（当data为列表时使用）
                - max_value: 设置最大值
                - auto_scale: 是否自动缩放数据
                - fill_alpha: 填充透明度，0-255
        """
        if not self._initialized:
            self.initialize()
        
        try:
            # 解析参数
            name = kwargs.get('name', data_id)
            color = kwargs.get('color', None)
            fill_color = kwargs.get('fill_color', None)
            line_width = kwargs.get('line_width', 2.0)
            symbol = kwargs.get('symbol', 'o')
            symbol_size = kwargs.get('symbol_size', 10)
            categories = kwargs.get('categories', None)
            max_value = kwargs.get('max_value', None)
            auto_scale = kwargs.get('auto_scale', self._auto_scale)
            fill_alpha = kwargs.get('fill_alpha', self._fill_alpha)
            
            # 处理颜色
            if color is None:
                # 使用预定义的颜色列表
                colors = [
                    (255, 0, 0),      # 红色
                    (0, 0, 255),      # 蓝色
                    (0, 255, 0),      # 绿色
                    (255, 165, 0),    # 橙色
                    (128, 0, 128),    # 紫色
                    (255, 192, 203),  # 粉红色
                    (165, 42, 42),    # 棕色
                    (0, 255, 255),    # 青色
                ]
                color = colors[len(self._data_items) % len(colors)]
            
            # 处理填充颜色
            if fill_color is None:
                # 使用半透明的线条颜色
                fill_color = (*color[:3], fill_alpha)
            
            # 更新最大值设置
            if max_value is not None:
                self.set_max_value(max_value, auto_scale)
            
            # 处理数据
            values = []
            
            if isinstance(data, dict):
                # 如果是字典，提取键作为类别
                if categories is None:
                    categories = list(data.keys())
                
                # 按照类别顺序提取值
                values = [data.get(cat, 0) for cat in categories]
                
                # 设置类别标签
                if not self._categories:
                    self.set_categories(categories)
                
            elif isinstance(data, (list, np.ndarray)):
                # 如果是列表或数组，直接使用
                values = data
                
                # 设置类别标签
                if categories is not None and not self._categories:
                    self.set_categories(categories)
            
            # 确保值列表长度与类别数量匹配
            if len(values) != len(self._categories):
                raise ValueError(f"数据长度 ({len(values)}) 与类别数量 ({len(self._categories)}) 不匹配")
            
            # 计算缩放比例
            scale = 1.0
            if auto_scale and values:
                max_data = max(max(values), 0.001)  # 避免除以零
                scale = self._max_value / max_data
            
            # 创建雷达图项
            radar_item = RadarPlotItem(
                values=values,
                labels=self._categories,
                scale=scale,
                color=color,
                fill_color=fill_color,
                line_width=line_width,
                symbol=symbol,
                symbol_size=symbol_size,
                name=name
            )
            
            # 添加到绘图项
            self._plot_item.addItem(radar_item)
            
            # 存储雷达图项
            self._radar_items[data_id] = radar_item
            self._data_items[data_id] = radar_item
            
            logger.debug(f"{self._log_prefix} 添加雷达图数据: {data_id}, 维度: {len(values)}")
            
        except Exception as e:
            logger.error(f"{self._log_prefix} 添加数据失败: {str(e)}")
            raise
    
    def update_data(
        self, 
        data_id: str, 
        data: Union[List[float], Dict[str, float]], 
        **kwargs
    ):
        """
        更新雷达图中的数据
        
        Args:
            data_id: 数据标识符
            data: 新的数据内容
            **kwargs: 额外的样式参数，与add_data相同
        """
        if not self._initialized or data_id not in self._data_items:
            if data_id not in self._data_items:
                # 如果数据不存在，则添加新数据
                self.add_data(data_id, data, **kwargs)
            return
        
        try:
            # 移除旧的雷达图项
            radar_item = self._radar_items[data_id]
            self._plot_item.removeItem(radar_item)
            
            # 添加新的雷达图项
            self.add_data(data_id, data, **kwargs)
            
        except Exception as e:
            logger.error(f"{self._log_prefix} 更新数据失败: {str(e)}")
            raise
    
    def set_fill_alpha(self, data_id: str, alpha: int):
        """
        设置填充透明度
        
        Args:
            data_id: 数据标识符
            alpha: 透明度，0-255
        """
        if not self._initialized or data_id not in self._radar_items:
            return
        
        radar_item = self._radar_items[data_id]
        
        # 创建新的填充颜色
        fill_color = (*radar_item.color[:3], alpha)
        fill_brush = pg.mkBrush(color=fill_color)
        
        # 更新填充画刷
        radar_item.fill_color = fill_color
        radar_item.fill_brush = fill_brush
        
        # 重新绘制
        radar_item.update()
    
    def set_line_width(self, data_id: str, width: float):
        """
        设置线条宽度
        
        Args:
            data_id: 数据标识符
            width: 线条宽度
        """
        if not self._initialized or data_id not in self._radar_items:
            return
        
        radar_item = self._radar_items[data_id]
        
        # 更新线条宽度
        radar_item.line_width = width
        radar_item.pen = pg.mkPen(color=radar_item.color, width=width)
        
        # 重新绘制
        radar_item.update()
    
    def set_symbol(self, data_id: str, symbol: str, size: int = None):
        """
        设置节点符号
        
        Args:
            data_id: 数据标识符
            symbol: 符号类型，如 'o', 't', '+', 'd', 's' 等
            size: 符号大小
        """
        if not self._initialized or data_id not in self._radar_items:
            return
        
        radar_item = self._radar_items[data_id]
        
        # 更新符号
        radar_item.symbol = symbol
        
        # 更新大小（如果提供）
        if size is not None:
            radar_item.symbol_size = size
        
        # 重新绘制
        radar_item.update()
    
    def set_color(self, data_id: str, color):
        """
        设置颜色
        
        Args:
            data_id: 数据标识符
            color: 颜色，格式为 (R,G,B) 或 '#RRGGBB'
        """
        if not self._initialized or data_id not in self._radar_items:
            return
        
        radar_item = self._radar_items[data_id]
        
        # 更新颜色
        radar_item.color = color
        radar_item.pen = pg.mkPen(color=color, width=radar_item.line_width)
        
        # 更新填充颜色（保持原透明度）
        alpha = radar_item.fill_color[3] if len(radar_item.fill_color) > 3 else 50
        radar_item.fill_color = (*color[:3], alpha)
        radar_item.fill_brush = pg.mkBrush(color=radar_item.fill_color)
        
        # 重新绘制
        radar_item.update()
    
    def set_n_circles(self, n: int):
        """
        设置同心圆数量
        
        Args:
            n: 同心圆数量
        """
        self._n_circles = n
        
        if not self._initialized:
            return
        
        # 更新雷达图轴
        self._update_axis()
    
    def normalize_data(self, data_values: Dict[str, List[float]]):
        """
        将多组数据标准化到相同比例
        
        Args:
            data_values: 数据字典，格式为 {data_id: values}
        """
        if not data_values:
            return
        
        # 找出所有数据的最大值
        all_values = []
        for values in data_values.values():
            all_values.extend(values)
        
        max_value = max(max(all_values), 0.001)  # 避免除以零
        
        # 更新最大值设置
        self.set_max_value(max_value, auto_scale=False)
        
        # 更新所有雷达图项的缩放比例
        for data_id, values in data_values.items():
            if data_id in self._radar_items:
                radar_item = self._radar_items[data_id]
                radar_item.scale = 1.0  # 使用统一比例
                
                # 更新值
                radar_item.values = values
                
                # 重新计算多边形
                radar_item._calculate_polygon()
                
                # 重新绘制
                radar_item.update()
    
    def set_legend_visible(self, visible: bool):
        """
        设置图例是否可见
        
        Args:
            visible: 是否可见
        """
        self._legend_visible = visible
        
        if not self._initialized:
            return
        
        # 更新图例可见性
        if hasattr(self._plot_item, 'legend'):
            if visible:
                self._plot_item.legend.show()
            else:
                self._plot_item.legend.hide()
    
    def clear_data(self, data_id: Optional[str] = None):
        """
        清除图表数据
        
        Args:
            data_id: 要清除的数据标识符，如果为None则清除所有数据
        """
        # 调用父类方法清除数据项
        super().clear_data(data_id)
        
        # 清除雷达图项
        if data_id is None:
            # 清除所有
            self._radar_items = {}
        else:
            # 清除指定ID
            if data_id in self._radar_items:
                del self._radar_items[data_id]
