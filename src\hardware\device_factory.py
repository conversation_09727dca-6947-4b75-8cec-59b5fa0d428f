#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
设备工厂模块

提供创建和管理不同协议适配器的工厂类。
"""

import logging
from enum import Enum, auto
from typing import Any, Dict, List, Optional, Type, Union

from src.hardware.protocol_base import ProtocolBase

logger = logging.getLogger(__name__)

class DeviceType(Enum):
    """设备类型枚举"""
    SERIAL = auto()     # 串口设备
    MODBUS = auto()     # Modbus设备
    TCP = auto()        # TCP设备
    MQTT = auto()       # MQTT设备
    CAN = auto()        # CAN总线设备
    OPC_UA = auto()     # OPC UA设备
    I2C = auto()        # I2C设备
    SPI = auto()        # SPI设备
    

class DeviceFactory:
    """
    设备工厂类
    
    用于创建和管理不同类型的通信协议适配器实例。
    采用工厂方法设计模式，使客户端代码不需要直接依赖具体的协议类。
    """
    
    # 类变量，保存设备类型到协议类的映射
    _protocol_map: Dict[DeviceType, Type[ProtocolBase]] = {}
    
    # 设备实例缓存
    _device_instances: Dict[str, ProtocolBase] = {}
    
    @classmethod
    def register_protocol(cls, device_type: DeviceType, protocol_class: Type[ProtocolBase]) -> None:
        """
        注册协议类
        
        Args:
            device_type: 设备类型
            protocol_class: 协议类
        """
        cls._protocol_map[device_type] = protocol_class
        logger.debug(f"已注册协议类: {device_type.name} -> {protocol_class.__name__}")
    
    @classmethod
    def create_device(cls, device_type: DeviceType, device_id: str = None, 
                     config: Dict = None) -> Optional[ProtocolBase]:
        """
        创建设备实例
        
        Args:
            device_type: 设备类型
            device_id: 设备ID，用于唯一标识设备实例
            config: 设备配置
            
        Returns:
            设备实例，如果创建失败则返回None
        """
        # 生成设备ID（如果未提供）
        if device_id is None:
            device_id = f"{device_type.name}_{len(cls._device_instances)}"
            
        # 检查是否已存在同ID的设备
        if device_id in cls._device_instances:
            logger.warning(f"设备ID '{device_id}' 已存在，将返回现有实例")
            return cls._device_instances[device_id]
            
        # 检查设备类型是否已注册
        if device_type not in cls._protocol_map:
            logger.error(f"未注册的设备类型: {device_type.name}")
            return None
            
        try:
            # 获取协议类
            protocol_class = cls._protocol_map[device_type]
            
            # 创建协议实例
            if config is not None:
                if device_type == DeviceType.SERIAL and "port" in config:
                    # 串口设备需要特殊处理，将端口名作为构造参数
                    device = protocol_class(config["port"])
                    # 设置其他配置
                    config_copy = config.copy()
                    config_copy.pop("port", None)
                    if config_copy:
                        device.set_config(config_copy)
                else:
                    # 创建实例后设置配置
                    device = protocol_class()
                    device.set_config(config)
            else:
                # 使用默认配置创建实例
                device = protocol_class()
                
            # 缓存设备实例
            cls._device_instances[device_id] = device
            
            logger.info(f"已创建设备实例: {device_id} ({device_type.name})")
            return device
            
        except Exception as e:
            logger.error(f"创建设备实例失败: {str(e)}")
            return None
    
    @classmethod
    def get_device(cls, device_id: str) -> Optional[ProtocolBase]:
        """
        获取设备实例
        
        Args:
            device_id: 设备ID
            
        Returns:
            设备实例，如果不存在则返回None
        """
        return cls._device_instances.get(device_id)
    
    @classmethod
    def remove_device(cls, device_id: str) -> bool:
        """
        移除设备实例
        
        Args:
            device_id: 设备ID
            
        Returns:
            是否成功移除
        """
        if device_id not in cls._device_instances:
            logger.warning(f"设备ID '{device_id}' 不存在")
            return False
            
        try:
            # 获取设备实例
            device = cls._device_instances[device_id]
            
            # 断开连接
            if device.is_connected:
                device.disconnect()
                
            # 移除实例
            del cls._device_instances[device_id]
            
            logger.info(f"已移除设备实例: {device_id}")
            return True
            
        except Exception as e:
            logger.error(f"移除设备实例失败: {str(e)}")
            return False
    
    @classmethod
    def get_all_devices(cls) -> Dict[str, ProtocolBase]:
        """
        获取所有设备实例
        
        Returns:
            设备ID到设备实例的映射
        """
        return cls._device_instances.copy()
    
    @classmethod
    def connect_device(cls, device_id: str) -> bool:
        """
        连接设备
        
        Args:
            device_id: 设备ID
            
        Returns:
            是否成功连接
        """
        device = cls.get_device(device_id)
        if device is None:
            logger.error(f"设备ID '{device_id}' 不存在")
            return False
            
        try:
            return device.connect()
        except Exception as e:
            logger.error(f"连接设备失败: {str(e)}")
            return False
    
    @classmethod
    def disconnect_device(cls, device_id: str) -> bool:
        """
        断开设备连接
        
        Args:
            device_id: 设备ID
            
        Returns:
            是否成功断开连接
        """
        device = cls.get_device(device_id)
        if device is None:
            logger.error(f"设备ID '{device_id}' 不存在")
            return False
            
        try:
            return device.disconnect()
        except Exception as e:
            logger.error(f"断开设备连接失败: {str(e)}")
            return False
    
    @classmethod
    def clear_all_devices(cls) -> None:
        """清除所有设备实例"""
        # 获取所有设备ID的副本
        device_ids = list(cls._device_instances.keys())
        
        # 逐个移除设备
        for device_id in device_ids:
            cls.remove_device(device_id)
            
        logger.info("已清除所有设备实例") 