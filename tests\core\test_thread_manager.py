#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
线程管理器模块测试
"""

import os
import sys
import unittest
import time
import threading
from unittest.mock import patch, MagicMock

# 确保可以导入被测试模块
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..')))

from src.core.thread_manager import ThreadManager, ThreadStatus, PeriodicThread, WorkerThread
from src.core.event_bus import event_bus, AppEvents

class TestThreadManager(unittest.TestCase):
    """线程管理器测试类"""
    
    def setUp(self):
        """每个测试方法执行前的设置"""
        # 重置ThreadManager单例
        ThreadManager._instance = None
        self.thread_manager = ThreadManager()
        
    def tearDown(self):
        """每个测试方法执行后的清理"""
        # 关闭线程管理器
        self.thread_manager.shutdown(wait=True)
    
    def test_singleton(self):
        """测试ThreadManager类是否正确实现单例模式"""
        manager1 = ThreadManager()
        manager2 = ThreadManager()
        self.assertIs(manager1, manager2)
    
    def test_create_and_start_thread(self):
        """测试创建和启动工作线程"""
        # 创建模拟任务函数
        mock_task = MagicMock()
        
        # 创建线程但不自动启动
        thread_id = self.thread_manager.create_thread(
            target=mock_task,
            name="TestThread",
            auto_start=False
        )
        
        # 验证线程已创建但未启动
        thread_info = self.thread_manager.get_thread_info(thread_id)
        self.assertEqual(thread_info.name, "TestThread")
        self.assertEqual(thread_info.status, ThreadStatus.CREATED)
        self.assertFalse(self.thread_manager.is_alive(thread_id))
        
        # 启动线程
        self.thread_manager.start_thread(thread_id)
        
        # 等待线程结束
        self.thread_manager.wait_thread(thread_id)
        
        # 验证线程已执行
        mock_task.assert_called_once()
        
        # 验证线程状态已更新
        thread_status = self.thread_manager.get_thread_status(thread_id)
        self.assertEqual(thread_status, ThreadStatus.STOPPED)
    
    def test_create_auto_start_thread(self):
        """测试创建自动启动的工作线程"""
        # 创建等待事件，用于控制线程执行
        wait_event = threading.Event()
        
        def task():
            # 等待直到被通知继续
            wait_event.wait()
        
        # 创建线程并自动启动
        thread_id = self.thread_manager.create_thread(
            target=task,
            name="AutoStartThread",
            auto_start=True
        )
        
        # 验证线程已创建并启动
        self.assertTrue(self.thread_manager.is_alive(thread_id))
        self.assertEqual(self.thread_manager.get_thread_status(thread_id), ThreadStatus.RUNNING)
        
        # 允许线程继续执行
        wait_event.set()
        
        # 等待线程结束
        self.thread_manager.wait_thread(thread_id, timeout=1)
    
    def test_stop_thread(self):
        """测试停止线程"""
        # 创建等待事件，用于控制线程执行
        wait_event = threading.Event()
        stop_event = threading.Event()
        
        def task():
            # 设置等待事件，表示线程已启动
            wait_event.set()
            
            # 等待直到被停止或超时
            stop_event.wait(1)
        
        # 创建并启动线程
        thread_id = self.thread_manager.create_thread(
            target=task,
            name="StopThread"
        )
        
        # 等待线程启动
        wait_event.wait()
        
        # 验证线程正在运行
        self.assertTrue(self.thread_manager.is_alive(thread_id))
        
        # 停止线程
        self.thread_manager.stop_thread(thread_id)
        
        # 验证线程状态已更新
        self.assertEqual(self.thread_manager.get_thread_status(thread_id), ThreadStatus.STOPPING)
        
        # 允许线程停止
        stop_event.set()
        
        # 等待线程结束
        self.thread_manager.wait_thread(thread_id, timeout=1)
        
        # 清理已结束的线程
        self.thread_manager.cleanup_finished_threads()
        
        # 验证线程已不存在或已停止
        thread_info = self.thread_manager.get_thread_info(thread_id)
        self.assertIsNone(thread_info)
    
    def test_periodic_thread(self):
        """测试周期性任务线程"""
        # 创建模拟回调函数
        mock_callback = MagicMock()
        
        # 创建周期性任务线程
        thread_id = self.thread_manager.create_periodic_thread(
            callback=mock_callback,
            interval=0.1,  # 100ms间隔
            name="PeriodicThread"
        )
        
        # 等待足够时间，让回调函数执行多次
        time.sleep(0.5)  # 应该执行约5次
        
        # 停止线程
        self.thread_manager.stop_thread(thread_id)
        
        # 等待线程结束
        self.thread_manager.wait_thread(thread_id)
        
        # 验证回调函数被调用多次
        self.assertGreaterEqual(mock_callback.call_count, 3)  # 至少被调用3次
    
    def test_pause_resume_periodic_thread(self):
        """测试暂停和恢复周期性任务线程"""
        # 创建模拟回调函数
        mock_callback = MagicMock()
        
        # 创建周期性任务线程
        thread_id = self.thread_manager.create_periodic_thread(
            callback=mock_callback,
            interval=0.1,  # 100ms间隔
            name="PauseResumeThread"
        )
        
        # 等待足够时间，让回调函数执行几次
        time.sleep(0.3)  # 应该执行约3次
        
        # 记录当前调用次数
        call_count_before_pause = mock_callback.call_count
        self.assertGreaterEqual(call_count_before_pause, 2)  # 至少被调用2次
        
        # 暂停线程
        self.thread_manager.pause_thread(thread_id)
        
        # 验证线程状态已更新
        self.assertEqual(self.thread_manager.get_thread_status(thread_id), ThreadStatus.PAUSED)
        
        # 等待足够时间，验证回调函数不再被调用
        time.sleep(0.3)
        self.assertEqual(mock_callback.call_count, call_count_before_pause)
        
        # 恢复线程
        self.thread_manager.resume_thread(thread_id)
        
        # 验证线程状态已更新
        self.assertEqual(self.thread_manager.get_thread_status(thread_id), ThreadStatus.RUNNING)
        
        # 等待足够时间，验证回调函数继续被调用
        time.sleep(0.3)
        self.assertGreater(mock_callback.call_count, call_count_before_pause)
        
        # 停止线程
        self.thread_manager.stop_thread(thread_id)
    
    def test_thread_pool(self):
        """测试线程池功能"""
        # 创建测试任务函数
        def task(x):
            time.sleep(0.1)  # 模拟一些工作
            return x * 2
        
        # 提交多个任务到线程池
        futures = []
        for i in range(5):
            future = self.thread_manager.submit_task(task, i)
            futures.append((i, future))
        
        # 获取任务结果
        results = []
        for i, future in futures:
            result = future.result()
            results.append((i, result))
        
        # 验证结果
        expected_results = [(i, i * 2) for i in range(5)]
        self.assertEqual(sorted(results), expected_results)
    
    def test_exception_in_thread(self):
        """测试线程中的异常处理"""
        # 创建一个会抛出异常的模拟事件处理器
        mock_exception_handler = MagicMock()
        event_bus.register(AppEvents.EXCEPTION, mock_exception_handler)
        
        # 创建会抛出异常的测试任务函数
        def error_task():
            raise ValueError("Test thread error")
        
        # 创建并启动线程
        thread_id = self.thread_manager.create_thread(
            target=error_task,
            name="ErrorThread"
        )
        
        # 等待线程结束
        self.thread_manager.wait_thread(thread_id)
        
        # 验证线程状态为ERROR
        self.assertEqual(self.thread_manager.get_thread_status(thread_id), ThreadStatus.ERROR)
        
        # 验证异常事件被触发
        mock_exception_handler.assert_called()
        # 验证异常信息正确
        args, kwargs = mock_exception_handler.call_args
        self.assertEqual(str(args[0]), "Test thread error")
        self.assertEqual(kwargs.get('thread_id'), thread_id)
    
    def test_shutdown(self):
        """测试关闭线程管理器"""
        # 创建一些测试线程
        thread_ids = []
        for i in range(3):
            def task():
                time.sleep(0.5)  # 模拟长时间运行任务
                
            thread_id = self.thread_manager.create_thread(
                target=task,
                name=f"ShutdownTest{i}"
            )
            thread_ids.append(thread_id)
        
        # 验证线程正在运行
        for thread_id in thread_ids:
            self.assertTrue(self.thread_manager.is_alive(thread_id))
        
        # 关闭线程管理器（不等待）
        self.thread_manager.shutdown(wait=False)
        
        # 验证所有线程都已收到停止信号
        for thread_id in thread_ids:
            status = self.thread_manager.get_thread_status(thread_id)
            self.assertEqual(status, ThreadStatus.STOPPING)

if __name__ == "__main__":
    unittest.main() 