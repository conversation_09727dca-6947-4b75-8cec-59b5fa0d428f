2025-07-31 09:20:43,818 - src.utils.log_manager - INFO - 鏃ュ織绯荤粺鍒濆�嬪寲瀹屾垚
2025-07-31 09:20:43,819 - src.utils.log_manager - DEBUG - 璋冭瘯鏃ュ織宸插惎鐢�
2025-07-31 09:20:43,819 - src.core.app - INFO - 鏃ュ織绯荤粺鍒濆�嬪寲瀹屾垚锛屾棩蹇楃骇鍒�: DEBUG
2025-07-31 09:20:43,822 - src.utils.log_manager - INFO - 鎴愬姛杩炴帴logger妯″潡鐨勬棩蹇楃郴缁熷埌UI
2025-07-31 09:20:43,822 - src.core.app - DEBUG - 鎴愬姛杩炴帴logger妯″潡鐨勬棩蹇楃郴缁熷埌UI鏄剧ず
2025-07-31 09:20:43,823 - src.core.app - DEBUG - 杩欐槸涓€鏉′粠app.py鍙戝嚭鐨凞EBUG绾у埆娴嬭瘯鏃ュ織
2025-07-31 09:20:43,823 - data_show - DEBUG - 这是一条从logger模块发出的DEBUG级别测试日志
2025-07-31 09:20:44,535 - data_show - DEBUG - [TimeSeriesChart] 创建图表对象: 实时数据监测
2025-07-31 09:20:44,549 - data_show - DEBUG - [TimeSeriesChart] 图表初始化完成
2025-07-31 09:20:44,549 - data_show - INFO - [ChartInitializer] 时序图初始化完成
2025-07-31 09:20:44,550 - data_show - DEBUG - [LineChart] 创建图表对象: 数据分析
2025-07-31 09:20:44,554 - data_show - DEBUG - [LineChart] 图表初始化完成
2025-07-31 09:20:44,556 - data_show - INFO - [ChartInitializer] 折线图初始化完成
2025-07-31 09:20:44,566 - src.hardware.device_factory - DEBUG - 宸叉敞鍐屽崗璁�绫�: SERIAL -> SerialProtocol
2025-07-31 09:20:44,566 - src.hardware.device_manager - INFO - 璁惧�囩�＄悊鍣ㄥ凡鍒濆�嬪寲
2025-07-31 09:20:44,567 - data_show - INFO - [MainWindowController] 主窗口信号连接完成
2025-07-31 09:20:44,570 - data_show - DEBUG - [TimeSeriesChart] 创建图表对象: 实时数据监测
2025-07-31 09:20:44,577 - data_show - DEBUG - [TimeSeriesChart] 图表初始化完成
2025-07-31 09:20:44,577 - data_show - INFO - [ChartInitializer] 时序图初始化完成
2025-07-31 09:20:44,578 - data_show - INFO - [MainWindowController] 时间序列图初始化成功
2025-07-31 09:20:44,578 - data_show - INFO - [MainWindowController] 主窗口控制器初始化完成
2025-07-31 09:20:44,578 - src.core.app - INFO - 涓荤獥鍙ｆ帶鍒跺櫒鍒濆�嬪寲瀹屾垚
2025-07-31 09:20:44,631 - src.core.app - INFO - 涓荤獥鍙ｆ樉绀�
2025-07-31 09:20:44,631 - core.event_bus - DEBUG - 娌℃湁澶勭悊鍣ㄦ敞鍐屼簨浠�: app.startup
2025-07-31 09:33:59,684 - data_show - INFO - [MainWindowController] 打开选项设置
2025-07-31 09:33:59,696 - src.ui.config_panels.serial_config_panel - DEBUG - 鍒锋柊涓插彛鍒楄〃锛屾壘鍒� 1 涓�鍙�鐢ㄧ��鍙�
2025-07-31 09:33:59,697 - src.ui.protocol_config_dialog - DEBUG - 宸叉敞鍐屽崗璁�: 涓插彛鍗忚��
2025-07-31 09:33:59,698 - src.ui.protocol_config_dialog - DEBUG - 鍔犺浇鍗忚��閰嶇疆: 涓插彛鍗忚��
2025-07-31 09:33:59,716 - src.ui.protocol_config_dialog - DEBUG - 宸插垏鎹㈠埌鍗忚��: 涓插彛鍗忚��
2025-07-31 10:42:29,034 - src.core.app - CRITICAL - 鏈�鎹曡幏鐨勫紓甯�: 
Traceback (most recent call last):
  File "D:\code\data-show\.venv\lib\site-packages\pyqtgraph\widgets\GraphicsView.py", line 135, in paintEvent
    def paintEvent(self, ev):
KeyboardInterrupt

2025-07-31 11:05:51,363 - src.core.app - INFO - 搴旂敤绋嬪簭姝ｅ湪鍏抽棴...
2025-07-31 11:05:51,364 - core.thread_manager - INFO - 鍏抽棴绾跨▼绠＄悊鍣�...
2025-07-31 11:05:51,364 - core.thread_manager - INFO - 绾跨▼绠＄悊鍣ㄥ凡鍏抽棴
2025-07-31 11:05:51,365 - src.core.app - INFO - 搴旂敤绋嬪簭宸插叧闂�
2025-07-31 13:52:00,375 - src.utils.log_manager - INFO - 鏃ュ織绯荤粺鍒濆�嬪寲瀹屾垚
2025-07-31 13:52:00,378 - src.utils.log_manager - DEBUG - 璋冭瘯鏃ュ織宸插惎鐢�
2025-07-31 13:52:00,379 - src.core.app - INFO - 鏃ュ織绯荤粺鍒濆�嬪寲瀹屾垚锛屾棩蹇楃骇鍒�: DEBUG
2025-07-31 13:52:00,391 - src.utils.log_manager - INFO - 鎴愬姛杩炴帴logger妯″潡鐨勬棩蹇楃郴缁熷埌UI
2025-07-31 13:52:00,393 - src.core.app - DEBUG - 鎴愬姛杩炴帴logger妯″潡鐨勬棩蹇楃郴缁熷埌UI鏄剧ず
2025-07-31 13:52:00,395 - src.core.app - DEBUG - 杩欐槸涓€鏉′粠app.py鍙戝嚭鐨凞EBUG绾у埆娴嬭瘯鏃ュ織
2025-07-31 13:52:00,396 - data_show - DEBUG - 这是一条从logger模块发出的DEBUG级别测试日志
2025-07-31 13:52:00,887 - src.core.app - ERROR - 鍒濆�嬪寲鍥捐〃澶辫触: Error importing numpy: you should not try to import numpy from
        its source directory; please exit the numpy source tree, and relaunch
        your python interpreter from there.
2025-07-31 13:52:00,988 - src.core.app - DEBUG - Traceback (most recent call last):
  File "D:\code\data-show\.venv\lib\site-packages\numpy\_core\__init__.py", line 23, in <module>
    from . import multiarray
  File "D:\code\data-show\.venv\lib\site-packages\numpy\_core\multiarray.py", line 10, in <module>
    from . import overrides
  File "D:\code\data-show\.venv\lib\site-packages\numpy\_core\overrides.py", line 7, in <module>
    from numpy._core._multiarray_umath import (
ModuleNotFoundError: No module named 'numpy.exceptions'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\code\data-show\.venv\lib\site-packages\numpy\__init__.py", line 127, in <module>
    from numpy.__config__ import show_config
  File "D:\code\data-show\.venv\lib\site-packages\numpy\__config__.py", line 4, in <module>
    from numpy._core._multiarray_umath import (
  File "D:\code\data-show\.venv\lib\site-packages\numpy\_core\__init__.py", line 49, in <module>
    raise ImportError(msg)
ImportError: 

IMPORTANT: PLEASE READ THIS FOR ADVICE ON HOW TO SOLVE THIS ISSUE!

Importing the numpy C-extensions failed. This error can happen for
many reasons, often due to issues with your setup or how NumPy was
installed.

We have compiled some common reasons and troubleshooting tips at:

    https://numpy.org/devdocs/user/troubleshooting-importerror.html

Please note and check the following:

  * The Python version is: Python3.10 from "D:\code\data-show\.venv\Scripts\python.exe"
  * The NumPy version is: "2.2.6"

and make sure that they are the versions you expect.
Please carefully study the documentation linked above for further help.

Original error was: No module named 'numpy.exceptions'


The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\code\data-show\src\core\app.py", line 236, in show_main_window
    from src.visualization.chart_initializer import chart_initializer
  File "D:\code\data-show\src\visualization\__init__.py", line 11, in <module>
    from .base_chart import BaseChart, ChartType
  File "D:\code\data-show\src\visualization\base_chart.py", line 15, in <module>
    import numpy as np
  File "D:\code\data-show\.venv\lib\site-packages\numpy\__init__.py", line 132, in <module>
    raise ImportError(msg) from e
ImportError: Error importing numpy: you should not try to import numpy from
        its source directory; please exit the numpy source tree, and relaunch
        your python interpreter from there.

2025-07-31 13:52:01,007 - src.core.app - ERROR - 鍒濆�嬪寲涓荤獥鍙ｆ帶鍒跺櫒澶辫触: CPU dispatcher tracer already initlized
2025-07-31 13:52:01,009 - src.core.app - DEBUG - Traceback (most recent call last):
  File "D:\code\data-show\src\core\app.py", line 247, in show_main_window
    from src.ui.main_window_controller import initialize as init_controller
  File "D:\code\data-show\src\ui\main_window_controller.py", line 40, in <module>
    from ..visualization.chart_initializer import chart_initializer
  File "D:\code\data-show\src\visualization\__init__.py", line 11, in <module>
    from .base_chart import BaseChart, ChartType
  File "D:\code\data-show\src\visualization\base_chart.py", line 15, in <module>
    import numpy as np
  File "D:\code\data-show\.venv\lib\site-packages\numpy\__init__.py", line 127, in <module>
    from numpy.__config__ import show_config
  File "D:\code\data-show\.venv\lib\site-packages\numpy\__config__.py", line 4, in <module>
    from numpy._core._multiarray_umath import (
  File "D:\code\data-show\.venv\lib\site-packages\numpy\_core\__init__.py", line 23, in <module>
    from . import multiarray
  File "D:\code\data-show\.venv\lib\site-packages\numpy\_core\multiarray.py", line 10, in <module>
    from . import overrides
  File "D:\code\data-show\.venv\lib\site-packages\numpy\_core\overrides.py", line 7, in <module>
    from numpy._core._multiarray_umath import (
RuntimeError: CPU dispatcher tracer already initlized

2025-07-31 13:52:01,067 - src.core.app - INFO - 涓荤獥鍙ｆ樉绀�
2025-07-31 13:52:01,068 - core.event_bus - DEBUG - 娌℃湁澶勭悊鍣ㄦ敞鍐屼簨浠�: app.startup
2025-07-31 13:53:07,969 - src.core.app - INFO - 搴旂敤绋嬪簭姝ｅ湪鍏抽棴...
2025-07-31 13:53:07,975 - core.thread_manager - INFO - 鍏抽棴绾跨▼绠＄悊鍣�...
2025-07-31 13:53:07,976 - core.thread_manager - INFO - 绾跨▼绠＄悊鍣ㄥ凡鍏抽棴
2025-07-31 13:53:07,976 - src.core.app - INFO - 搴旂敤绋嬪簭宸插叧闂�
2025-07-31 13:53:45,209 - src.utils.log_manager - INFO - 鏃ュ織绯荤粺鍒濆�嬪寲瀹屾垚
2025-07-31 13:53:45,210 - src.utils.log_manager - DEBUG - 璋冭瘯鏃ュ織宸插惎鐢�
2025-07-31 13:53:45,210 - src.core.app - INFO - 鏃ュ織绯荤粺鍒濆�嬪寲瀹屾垚锛屾棩蹇楃骇鍒�: DEBUG
2025-07-31 13:53:45,214 - src.utils.log_manager - INFO - 鎴愬姛杩炴帴logger妯″潡鐨勬棩蹇楃郴缁熷埌UI
2025-07-31 13:53:45,215 - src.core.app - DEBUG - 鎴愬姛杩炴帴logger妯″潡鐨勬棩蹇楃郴缁熷埌UI鏄剧ず
2025-07-31 13:53:45,215 - src.core.app - DEBUG - 杩欐槸涓€鏉′粠app.py鍙戝嚭鐨凞EBUG绾у埆娴嬭瘯鏃ュ織
2025-07-31 13:53:45,215 - data_show - DEBUG - 这是一条从logger模块发出的DEBUG级别测试日志
2025-07-31 13:53:45,221 - src.core.app - ERROR - 鍒濆�嬪寲鍥捐〃澶辫触: Error importing numpy: you should not try to import numpy from
        its source directory; please exit the numpy source tree, and relaunch
        your python interpreter from there.
2025-07-31 13:53:45,222 - src.core.app - DEBUG - Traceback (most recent call last):
  File "D:\code\data-show\.venv\lib\site-packages\numpy\_core\__init__.py", line 23, in <module>
    from . import multiarray
  File "D:\code\data-show\.venv\lib\site-packages\numpy\_core\multiarray.py", line 10, in <module>
    from . import overrides
  File "D:\code\data-show\.venv\lib\site-packages\numpy\_core\overrides.py", line 7, in <module>
    from numpy._core._multiarray_umath import (
ModuleNotFoundError: No module named 'numpy.exceptions'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\code\data-show\.venv\lib\site-packages\numpy\__init__.py", line 127, in <module>
    from numpy.__config__ import show_config
  File "D:\code\data-show\.venv\lib\site-packages\numpy\__config__.py", line 4, in <module>
    from numpy._core._multiarray_umath import (
  File "D:\code\data-show\.venv\lib\site-packages\numpy\_core\__init__.py", line 49, in <module>
    raise ImportError(msg)
ImportError: 

IMPORTANT: PLEASE READ THIS FOR ADVICE ON HOW TO SOLVE THIS ISSUE!

Importing the numpy C-extensions failed. This error can happen for
many reasons, often due to issues with your setup or how NumPy was
installed.

We have compiled some common reasons and troubleshooting tips at:

    https://numpy.org/devdocs/user/troubleshooting-importerror.html

Please note and check the following:

  * The Python version is: Python3.10 from "D:\code\data-show\.venv\Scripts\python.exe"
  * The NumPy version is: "2.2.6"

and make sure that they are the versions you expect.
Please carefully study the documentation linked above for further help.

Original error was: No module named 'numpy.exceptions'


The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\code\data-show\src\core\app.py", line 236, in show_main_window
    from src.visualization.chart_initializer import chart_initializer
  File "D:\code\data-show\src\visualization\__init__.py", line 11, in <module>
    from .base_chart import BaseChart, ChartType
  File "D:\code\data-show\src\visualization\base_chart.py", line 15, in <module>
    import numpy as np
  File "D:\code\data-show\.venv\lib\site-packages\numpy\__init__.py", line 132, in <module>
    raise ImportError(msg) from e
ImportError: Error importing numpy: you should not try to import numpy from
        its source directory; please exit the numpy source tree, and relaunch
        your python interpreter from there.

2025-07-31 13:53:45,226 - src.core.app - ERROR - 鍒濆�嬪寲涓荤獥鍙ｆ帶鍒跺櫒澶辫触: CPU dispatcher tracer already initlized
2025-07-31 13:53:45,227 - src.core.app - DEBUG - Traceback (most recent call last):
  File "D:\code\data-show\src\core\app.py", line 247, in show_main_window
    from src.ui.main_window_controller import initialize as init_controller
  File "D:\code\data-show\src\ui\main_window_controller.py", line 40, in <module>
    from ..visualization.chart_initializer import chart_initializer
  File "D:\code\data-show\src\visualization\__init__.py", line 11, in <module>
    from .base_chart import BaseChart, ChartType
  File "D:\code\data-show\src\visualization\base_chart.py", line 15, in <module>
    import numpy as np
  File "D:\code\data-show\.venv\lib\site-packages\numpy\__init__.py", line 127, in <module>
    from numpy.__config__ import show_config
  File "D:\code\data-show\.venv\lib\site-packages\numpy\__config__.py", line 4, in <module>
    from numpy._core._multiarray_umath import (
  File "D:\code\data-show\.venv\lib\site-packages\numpy\_core\__init__.py", line 23, in <module>
    from . import multiarray
  File "D:\code\data-show\.venv\lib\site-packages\numpy\_core\multiarray.py", line 10, in <module>
    from . import overrides
  File "D:\code\data-show\.venv\lib\site-packages\numpy\_core\overrides.py", line 7, in <module>
    from numpy._core._multiarray_umath import (
RuntimeError: CPU dispatcher tracer already initlized

2025-07-31 13:53:45,267 - src.core.app - INFO - 涓荤獥鍙ｆ樉绀�
2025-07-31 13:53:45,268 - core.event_bus - DEBUG - 娌℃湁澶勭悊鍣ㄦ敞鍐屼簨浠�: app.startup
2025-07-31 13:54:17,037 - src.core.app - INFO - 搴旂敤绋嬪簭姝ｅ湪鍏抽棴...
2025-07-31 13:54:17,039 - core.thread_manager - INFO - 鍏抽棴绾跨▼绠＄悊鍣�...
2025-07-31 13:54:17,039 - core.thread_manager - INFO - 绾跨▼绠＄悊鍣ㄥ凡鍏抽棴
2025-07-31 13:54:17,039 - src.core.app - INFO - 搴旂敤绋嬪簭宸插叧闂�
