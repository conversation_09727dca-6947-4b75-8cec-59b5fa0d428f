#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
综合图表框架示例

展示所有已实现的图表类型及其基本用法。
包括折线图、散点图、时间序列图、柱状图、热力图、饼图、箱线图和雷达图。
支持标签页布局和堆叠布局两种显示方式。
"""

import os
import sys
import time
import numpy as np
import traceback
from typing import List, Tuple, Dict
from datetime import datetime, timedelta

# 确保可以导入其他模块
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..')))

# 尝试导入Qt库
try:
    from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QWidget,
                               QPushButton, QHBoxLayout, QMessageBox, QTabWidget,
                               QLabel, QComboBox, QSpinBox, QDoubleSpinBox, QGroupBox,
                               QScrollArea, QSizePolicy, QFrame)
    from PyQt5.QtCore import QTimer, Qt, QSize
except ImportError:
    try:
        from PySide6.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QWidget,
                                     QPushButton, QHBoxLayout, QMessageBox, QTabWidget,
                                     QLabel, QComboBox, QSpinBox, QDoubleSpinBox, QGroupBox,
                                     QScrollArea, QSizePolicy, QFrame)
        from PySide6.QtCore import QTimer, Qt, QSize
    except ImportError:
        print("错误：找不到 PyQt5 或 PySide6 库。请安装其中一个。")
        sys.exit(1)

# 导入图表相关模块
from src.visualization.chart_manager import ChartManager, LayoutType
from src.visualization.charts.line_chart import LineChart
from src.visualization.charts.scatter import ScatterChart
from src.visualization.charts.time_series import TimeSeriesChart
from src.visualization.charts.histogram import HistogramChart
from src.visualization.charts.heatmap import HeatmapChart
from src.visualization.charts.pie_chart import PieChart
from src.visualization.charts.box_plot import BoxPlotChart
from src.visualization.charts.radar import RadarChart
from src.utils.logger import logger, configure


class AllChartsExampleWindow(QMainWindow):
    """
    综合图表示例窗口，展示所有图表类型
    支持标签页布局和堆叠布局两种显示方式
    """

    def __init__(self):
        super().__init__()

        # 配置日志
        configure({
            'level': 'debug',
            'console_enabled': True,
            'file_enabled': True
        })

        # 设置窗口属性
        self.setWindowTitle("综合图表框架示例")
        self.setGeometry(100, 50, 1280, 800)

        # 创建中央部件和布局
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        self.main_layout = QVBoxLayout(central_widget)

        # 创建布局选择控件
        layout_control = QWidget()
        layout_control_layout = QHBoxLayout(layout_control)
        layout_control_layout.setContentsMargins(5, 5, 5, 5)

        layout_label = QLabel("布局方式:")
        self.layout_combo = QComboBox()
        self.layout_combo.addItems(["标签页布局", "堆叠布局"])
        self.layout_combo.currentIndexChanged.connect(self.change_layout_mode)

        layout_control_layout.addWidget(layout_label)
        layout_control_layout.addWidget(self.layout_combo)
        layout_control_layout.addStretch()

        self.main_layout.addWidget(layout_control)

        # 创建内容区域
        self.content_widget = QWidget()
        self.content_layout = QVBoxLayout(self.content_widget)
        self.content_layout.setContentsMargins(0, 0, 0, 0)

        self.main_layout.addWidget(self.content_widget, 1)

        # 创建标签页控件
        self.tab_widget = QTabWidget()

        # 创建堆叠布局的滚动区域
        self.scroll_area = QScrollArea()
        self.scroll_area.setWidgetResizable(True)
        self.scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        self.scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)

        self.stacked_widget = QWidget()
        self.stacked_layout = QVBoxLayout(self.stacked_widget)
        self.scroll_area.setWidget(self.stacked_widget)

        # 初始化为标签页模式
        self.current_layout_mode = "tabs"
        self.content_layout.addWidget(self.tab_widget)

        # 初始化所有图表标签页
        self.init_line_chart_tab()
        self.init_scatter_chart_tab()
        self.init_time_series_tab()
        self.init_histogram_tab()
        self.init_heatmap_tab()
        self.init_pie_chart_tab()
        self.init_box_plot_tab()
        self.init_radar_chart_tab()

        # 初始化堆叠布局的所有图表
        self.init_stacked_charts()

        # 设置定时器，用于模拟实时数据更新
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_realtime_data)
        self.timer.start(1000)  # 每秒更新一次

        # 应用异常处理
        sys.excepthook = self.exception_hook

        logger.info("综合图表示例窗口初始化完成")

    def change_layout_mode(self, index):
        """切换布局模式"""
        if index == 0 and self.current_layout_mode != "tabs":
            # 切换到标签页模式
            if self.scroll_area.parent():
                self.content_layout.removeWidget(self.scroll_area)
            self.content_layout.addWidget(self.tab_widget)
            self.current_layout_mode = "tabs"
            logger.info("切换到标签页布局模式")
        elif index == 1 and self.current_layout_mode != "stacked":
            # 切换到堆叠模式
            if self.tab_widget.parent():
                self.content_layout.removeWidget(self.tab_widget)
            self.content_layout.addWidget(self.scroll_area)
            self.current_layout_mode = "stacked"
            logger.info("切换到堆叠布局模式")

    def exception_hook(self, exc_type, exc_value, exc_traceback):
        """全局异常处理函数"""
        # 打印异常信息到控制台
        print("".join(traceback.format_exception(exc_type, exc_value, exc_traceback)))
        # 记录到日志
        logger.exception(f"未捕获的异常: {exc_value}")
        # 显示错误对话框
        QMessageBox.critical(self, "错误", f"发生了一个错误:\n{exc_value}")

    def init_line_chart_tab(self):
        """初始化折线图标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # 创建图表管理器
        chart_manager = ChartManager(layout_type=LayoutType.GRID)
        layout.addWidget(chart_manager)

        # 创建折线图
        line_chart = LineChart(title="折线图示例", x_label="X轴", y_label="Y轴")
        chart_id = chart_manager.add_chart(line_chart)

        # 添加初始数据
        x_data = np.linspace(0, 10, 100)
        y_data = np.sin(x_data)
        line_chart.add_data(
            data_id="线条1",
            data=(x_data, y_data),
            name="正弦波",
            symbol="o",
            symbol_size=5
        )

        y_data2 = np.cos(x_data)
        line_chart.add_data(
            data_id="线条2",
            data=(x_data, y_data2),
            name="余弦波",
            symbol="t",
            symbol_size=5
        )

        # 创建控制面板
        control_panel = QWidget()
        control_layout = QHBoxLayout(control_panel)

        update_btn = QPushButton("更新数据")
        update_btn.clicked.connect(lambda: self.update_line_chart_data(line_chart))

        clear_btn = QPushButton("清除数据")
        clear_btn.clicked.connect(lambda: self.clear_line_chart_data(line_chart))

        control_layout.addWidget(update_btn)
        control_layout.addWidget(clear_btn)
        control_layout.addStretch()

        layout.addWidget(control_panel)

        # 保存对象以便后续使用
        self.line_chart = line_chart

        # 添加到标签页
        self.tab_widget.addTab(tab, "折线图")

    def update_line_chart_data(self, chart):
        """更新折线图数据"""
        try:
            x_data = np.linspace(0, 10, 100)
            y_data = np.sin(x_data + np.random.random() * 5)
            y_data2 = np.cos(x_data + np.random.random() * 5)

            chart.update_data("线条1", (x_data, y_data))
            chart.update_data("线条2", (x_data, y_data2))

            logger.info("已更新折线图数据")
        except Exception as e:
            logger.exception(f"更新折线图数据失败: {str(e)}")

    def clear_line_chart_data(self, chart):
        """清除折线图数据并强制更新视图"""
        chart.clear_data()
        chart.initialize()  # 重新初始化图表以强制刷新
        logger.info("已清除折线图数据")

    def init_scatter_chart_tab(self):
        """初始化散点图标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # 创建图表管理器
        chart_manager = ChartManager(layout_type=LayoutType.GRID)
        layout.addWidget(chart_manager)

        # 创建散点图
        scatter_chart = ScatterChart(title="散点图示例", x_label="X轴", y_label="Y轴")
        chart_id = chart_manager.add_chart(scatter_chart)

        # 添加初始数据 - 随机点
        x_data = np.random.normal(0, 1, 100)
        y_data = np.random.normal(0, 1, 100)
        scatter_chart.add_data(
            data_id="点集1",
            data=(x_data, y_data),
            name="随机点",
            symbol="o",
            symbol_size=8
        )

        # 添加第二组数据 - 聚类效果
        cluster1_x = np.random.normal(-2, 0.5, 50)
        cluster1_y = np.random.normal(2, 0.5, 50)
        scatter_chart.add_data(
            data_id="点集2",
            data=(cluster1_x, cluster1_y),
            name="簇1",
            symbol="t",
            symbol_size=8,
            color=(255, 0, 0)
        )

        # 添加第三组数据 - 另一个聚类
        cluster2_x = np.random.normal(2, 0.5, 50)
        cluster2_y = np.random.normal(-2, 0.5, 50)
        scatter_chart.add_data(
            data_id="点集3",
            data=(cluster2_x, cluster2_y),
            name="簇2",
            symbol="s",
            symbol_size=8,
            color=(0, 255, 0)
        )

        # 创建控制面板
        control_panel = QWidget()
        control_layout = QHBoxLayout(control_panel)

        update_btn = QPushButton("更新数据")
        update_btn.clicked.connect(lambda: self.update_scatter_chart_data(scatter_chart))

        clear_btn = QPushButton("清除数据")
        clear_btn.clicked.connect(lambda: self.clear_scatter_chart_data(scatter_chart))

        control_layout.addWidget(update_btn)
        control_layout.addWidget(clear_btn)
        control_layout.addStretch()

        layout.addWidget(control_panel)

        # 保存对象以便后续使用
        self.scatter_chart = scatter_chart

        # 添加到标签页
        self.tab_widget.addTab(tab, "散点图")

    def update_scatter_chart_data(self, chart):
        """更新散点图数据"""
        try:
            # 更新随机点
            x_data = np.random.normal(0, 1, 100)
            y_data = np.random.normal(0, 1, 100)
            chart.update_data("点集1", (x_data, y_data))

            # 更新聚类1
            offset_x = np.random.random() * 4 - 2
            offset_y = np.random.random() * 4 - 2
            cluster1_x = np.random.normal(offset_x, 0.5, 50)
            cluster1_y = np.random.normal(offset_y, 0.5, 50)
            chart.update_data("点集2", (cluster1_x, cluster1_y))

            # 更新聚类2
            offset_x = np.random.random() * 4 - 2
            offset_y = np.random.random() * 4 - 2
            cluster2_x = np.random.normal(offset_x, 0.5, 50)
            cluster2_y = np.random.normal(offset_y, 0.5, 50)
            chart.update_data("点集3", (cluster2_x, cluster2_y))

            logger.info("已更新散点图数据")
        except Exception as e:
            logger.exception(f"更新散点图数据失败: {str(e)}")

    def clear_scatter_chart_data(self, chart):
        """清除散点图数据并强制更新视图"""
        chart.clear_data()
        chart.initialize()  # 重新初始化图表以强制刷新
        logger.info("已清除散点图数据")

    def init_time_series_tab(self):
        """初始化时间序列图标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # 创建图表管理器
        chart_manager = ChartManager(layout_type=LayoutType.GRID)
        layout.addWidget(chart_manager)

        # 创建时间序列图
        time_series_chart = TimeSeriesChart(title="时间序列图示例", x_label="时间", y_label="数值")
        chart_id = chart_manager.add_chart(time_series_chart)

        # 添加初始数据 - 过去30天的数据
        end_date = datetime.now()
        start_date = end_date - timedelta(days=30)
        dates = [start_date + timedelta(days=i) for i in range(31)]

        # 模拟股票价格
        base_value = 100
        values = [base_value]
        for i in range(1, 31):
            change = np.random.normal(0, 1) * 2  # 每天随机变动
            values.append(values[-1] + change)

        time_series_chart.add_data(
            data_id="股票价格",
            data=(dates, values),
            name="股票价格",
            color=(0, 0, 255),
            line_width=2
        )

        # 添加第二组数据 - 成交量
        volume = np.random.randint(1000, 5000, 31)
        time_series_chart.add_data(
            data_id="成交量",
            data=(dates, volume),
            name="成交量",
            color=(255, 0, 0),
            axis="right",
            line_width=1.5,
            symbol="t",
            symbol_size=6
        )

        # 创建控制面板
        control_panel = QWidget()
        control_layout = QHBoxLayout(control_panel)

        add_point_btn = QPushButton("添加数据点")
        add_point_btn.clicked.connect(lambda: self.add_time_series_point(time_series_chart))

        clear_btn = QPushButton("清除数据")
        clear_btn.clicked.connect(lambda: self.clear_time_series_chart_data(time_series_chart))

        control_layout.addWidget(add_point_btn)
        control_layout.addWidget(clear_btn)
        control_layout.addStretch()

        layout.addWidget(control_panel)

        # 保存对象以便后续使用
        self.time_series_chart = time_series_chart
        self.ts_dates = dates
        self.ts_values = values
        self.ts_volume = volume.tolist()

        # 添加到标签页
        self.tab_widget.addTab(tab, "时间序列图")

    def add_time_series_point(self, chart):
        """为时间序列图添加新数据点"""
        try:
            # 添加一天的数据
            last_date = self.ts_dates[-1]
            new_date = last_date + timedelta(days=1)
            self.ts_dates.append(new_date)

            # 更新股票价格
            last_value = self.ts_values[-1]
            change = np.random.normal(0, 1) * 2
            new_value = last_value + change
            self.ts_values.append(new_value)

            # 更新成交量
            new_volume = np.random.randint(1000, 5000)
            self.ts_volume.append(new_volume)

            # 更新图表数据
            chart.update_data("股票价格", (self.ts_dates, self.ts_values))
            chart.update_data("成交量", (self.ts_dates, self.ts_volume))

            logger.info(f"已添加时间序列数据点: {new_date.strftime('%Y-%m-%d')}")
        except Exception as e:
            logger.exception(f"添加时间序列数据点失败: {str(e)}")

    def clear_time_series_chart_data(self, chart):
        """清除时间序列图数据并强制更新视图"""
        chart.clear_data()
        chart.initialize()  # 重新初始化图表以强制刷新
        logger.info("已清除时间序列图数据")

    def init_histogram_tab(self):
        """初始化柱状图标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # 创建图表管理器
        chart_manager = ChartManager(layout_type=LayoutType.GRID)
        layout.addWidget(chart_manager)

        # 创建柱状图
        histogram_chart = HistogramChart(title="柱状图示例", x_label="类别", y_label="数值")
        chart_id = chart_manager.add_chart(histogram_chart)

        # 设置类别
        categories = ["类别A", "类别B", "类别C", "类别D", "类别E"]
        histogram_chart.set_categories(categories)

        # 添加数据
        values = np.random.randint(10, 100, len(categories))
        histogram_chart.add_data(
            data_id="数据集1",
            data=values,
            name="数据集1",
            offset=-0.2,  # 为第一组数据添加负偏移量
            bar_width=0.4  # 设置合适的宽度
        )

        # 添加第二组数据（用于分组对比）
        values2 = np.random.randint(10, 100, len(categories))
        histogram_chart.add_data(
            data_id="数据集2",
            data=values2,
            name="数据集2",
            offset=0.2,  # 保持正偏移量
            bar_width=0.4,  # 设置相同的宽度
            color=(0, 0, 255)
        )

        # 创建控制面板
        control_panel = QWidget()
        control_layout = QHBoxLayout(control_panel)

        update_btn = QPushButton("更新数据")
        update_btn.clicked.connect(lambda: self.update_histogram_data(histogram_chart, categories))

        group_btn = QPushButton("创建分组柱状图")
        group_btn.clicked.connect(lambda: self.create_grouped_bars(histogram_chart, categories))

        stack_btn = QPushButton("创建堆叠柱状图")
        stack_btn.clicked.connect(lambda: self.create_stacked_bars(histogram_chart, categories))

        clear_btn = QPushButton("清除数据")
        clear_btn.clicked.connect(lambda: self.clear_histogram_chart_data(histogram_chart))

        control_layout.addWidget(update_btn)
        control_layout.addWidget(group_btn)
        control_layout.addWidget(stack_btn)
        control_layout.addWidget(clear_btn)
        control_layout.addStretch()

        layout.addWidget(control_panel)

        # 保存对象以便后续使用
        self.histogram_chart = histogram_chart

        # 添加到标签页
        self.tab_widget.addTab(tab, "柱状图")

    def update_histogram_data(self, chart, categories):
        """更新柱状图数据"""
        try:
            values = np.random.randint(10, 100, len(categories))
            chart.update_data("数据集1", values)

            values2 = np.random.randint(10, 100, len(categories))
            chart.update_data("数据集2", values2)

            logger.info("已更新柱状图数据")
        except Exception as e:
            logger.exception(f"更新柱状图数据失败: {str(e)}")

    def create_grouped_bars(self, chart, categories):
        """创建分组柱状图"""
        try:
            # 创建三组数据
            data_dict = {}
            for i in range(3):
                data_dict[f"数据集{i+1}"] = np.random.randint(10, 100, len(categories))

            # 使用分组柱状图方法
            chart.create_grouped_bars(
                data_dict=data_dict,
                categories=categories,
                bar_width=0.2,
                group_spacing=0.2
            )

            logger.info("已创建分组柱状图")
        except Exception as e:
            logger.exception(f"创建分组柱状图失败: {str(e)}")

    def create_stacked_bars(self, chart, categories):
        """创建堆叠柱状图"""
        try:
            # 创建三组数据
            data_dict = {}
            for i in range(3):
                data_dict[f"数据集{i+1}"] = np.random.randint(10, 50, len(categories))

            # 使用堆叠柱状图方法
            chart.create_stacked_bars(
                data_dict=data_dict,
                categories=categories,
                bar_width=0.6
            )

            logger.info("已创建堆叠柱状图")
        except Exception as e:
            logger.exception(f"创建堆叠柱状图失败: {str(e)}")

    def clear_histogram_chart_data(self, chart):
        """清除柱状图数据并强制更新视图"""
        chart.clear_data()
        chart.initialize()  # 重新初始化图表以强制刷新
        logger.info("已清除柱状图数据")

    def init_heatmap_tab(self):
        """初始化热力图标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # 创建图表管理器
        chart_manager = ChartManager(layout_type=LayoutType.GRID)
        layout.addWidget(chart_manager)

        # 创建热力图
        heatmap_chart = HeatmapChart(title="热力图示例", x_label="X轴", y_label="Y轴")
        chart_id = chart_manager.add_chart(heatmap_chart)

        # 创建示例数据 - 20x20的热力图
        data = np.zeros((20, 20))
        # 添加一些模式
        for i in range(20):
            for j in range(20):
                # 创建一些有趣的图案
                data[i, j] = np.sin(i/5.0) * np.cos(j/5.0) * 0.5 + 0.5

        # X和Y轴标签
        x_labels = [f"X{i}" for i in range(20)]
        y_labels = [f"Y{i}" for i in range(20)]

        # 添加热力图数据
        heatmap_chart.add_data(
            data_id="热力图数据",
            data=data,
            x_labels=x_labels,
            y_labels=y_labels
        )

        # 创建控制面板
        control_panel = QWidget()
        control_layout = QHBoxLayout(control_panel)

        update_btn = QPushButton("更新数据")
        update_btn.clicked.connect(lambda: self.update_heatmap_data(heatmap_chart, x_labels, y_labels))

        color_map_combo = QComboBox()
        color_map_combo.addItems(["热力图", "彩虹", "寒冷", "黑白"])
        color_map_combo.currentIndexChanged.connect(
            lambda idx: self.change_heatmap_colormap(heatmap_chart, idx)
        )

        clear_btn = QPushButton("清除数据")
        clear_btn.clicked.connect(lambda: self.clear_heatmap_chart_data(heatmap_chart))

        control_layout.addWidget(update_btn)
        control_layout.addWidget(QLabel("颜色映射:"))
        control_layout.addWidget(color_map_combo)
        control_layout.addWidget(clear_btn)
        control_layout.addStretch()

        layout.addWidget(control_panel)

        # 保存对象以便后续使用
        self.heatmap_chart = heatmap_chart

        # 添加到标签页
        self.tab_widget.addTab(tab, "热力图")

    def update_heatmap_data(self, chart, x_labels, y_labels):
        """更新热力图数据"""
        try:
            # 创建新的随机数据
            data = np.random.rand(20, 20)

            # 可以添加一些模式
            for i in range(20):
                for j in range(20):
                    # 添加高斯模式
                    dx = (i - 10) / 5.0
                    dy = (j - 10) / 5.0
                    data[i, j] += np.exp(-(dx*dx + dy*dy)) * 0.5

            # 更新热力图数据
            chart.update_data(
                data_id="热力图数据",
                data=data,
                x_labels=x_labels,
                y_labels=y_labels
            )

            logger.info("已更新热力图数据")
        except Exception as e:
            logger.exception(f"更新热力图数据失败: {str(e)}")

    def change_heatmap_colormap(self, chart, index):
        """更改热力图的颜色映射"""
        try:
            colormaps = ["viridis", "rainbow", "cool", "gray"]
            colormap = colormaps[index]

            chart.set_colormap(colormap)

            logger.info(f"已更改热力图颜色映射为: {colormap}")
        except Exception as e:
            logger.exception(f"更改热力图颜色映射失败: {str(e)}")

    def clear_heatmap_chart_data(self, chart):
        """清除热力图数据并强制更新视图"""
        chart.clear_data()
        chart.initialize()  # 重新初始化图表以强制刷新
        logger.info("已清除热力图数据")

    def init_pie_chart_tab(self):
        """初始化饼图标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # 创建图表管理器
        chart_manager = ChartManager(layout_type=LayoutType.GRID)
        layout.addWidget(chart_manager)

        # 创建饼图
        pie_chart = PieChart(title="饼图示例")
        chart_id = chart_manager.add_chart(pie_chart)

        # 添加数据
        labels = ["类别A", "类别B", "类别C", "类别D", "类别E"]
        values = np.random.randint(10, 100, len(labels))

        pie_chart.add_data(
            data_id="饼图数据",
            data=dict(zip(labels, values)),
            explode=[0, 0, 0.1, 0, 0],  # 突出显示第三个扇区
            start_angle=90
        )

        # 创建控制面板
        control_panel = QWidget()
        control_layout = QHBoxLayout(control_panel)

        update_btn = QPushButton("更新数据")
        update_btn.clicked.connect(lambda: self.update_pie_chart_data(pie_chart, labels))

        donut_btn = QPushButton("甜甜圈图")
        donut_btn.clicked.connect(lambda: pie_chart.set_hole_size(0.4))

        pie_btn = QPushButton("饼图")
        pie_btn.clicked.connect(lambda: pie_chart.set_hole_size(0))

        clear_btn = QPushButton("清除数据")
        clear_btn.clicked.connect(lambda: self.clear_pie_chart_data(pie_chart))

        control_layout.addWidget(update_btn)
        control_layout.addWidget(donut_btn)
        control_layout.addWidget(pie_btn)
        control_layout.addWidget(clear_btn)
        control_layout.addStretch()

        layout.addWidget(control_panel)

        # 保存对象以便后续使用
        self.pie_chart = pie_chart

        # 添加到标签页
        self.tab_widget.addTab(tab, "饼图")

    def update_pie_chart_data(self, chart, labels):
        """更新饼图数据"""
        try:
            values = np.random.randint(10, 100, len(labels))

            # 创建新的突出显示配置
            explode = [0] * len(labels)
            explode[np.random.randint(0, len(labels))] = 0.1

            chart.update_data(
                data_id="饼图数据",
                data=dict(zip(labels, values)),
                explode=explode,
                start_angle=np.random.randint(0, 360)
            )

            logger.info("已更新饼图数据")
        except Exception as e:
            logger.exception(f"更新饼图数据失败: {str(e)}")

    def clear_pie_chart_data(self, chart):
        """清除饼图数据并强制更新视图"""
        chart.clear_data()
        chart.initialize()  # 重新初始化图表以强制刷新
        logger.info("已清除饼图数据")

    def init_box_plot_tab(self):
        """初始化箱线图标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # 创建图表管理器
        chart_manager = ChartManager(layout_type=LayoutType.GRID)
        layout.addWidget(chart_manager)

        # 创建箱线图
        box_plot = BoxPlotChart(title="箱线图示例", x_label="组别", y_label="数值")
        chart_id = chart_manager.add_chart(box_plot)

        # 生成示例数据
        groups = ["组A", "组B", "组C", "组D", "组E"]
        data_dict = {}

        for group in groups:
            # 为每个组生成随机数据
            mean = np.random.uniform(50, 150)
            std = np.random.uniform(5, 15)
            data = np.random.normal(mean, std, 100)
            # 添加一些异常值
            if np.random.random() > 0.5:
                data = np.append(data, [mean + std * 3.5, mean - std * 3.5])
            data_dict[group] = data

        # 添加数据到箱线图
        box_plot.add_data(
            data_id="箱线图数据",
            data=data_dict,
            name="示例数据",
            method="tukey",  # 使用Tukey方法检测异常值
            show_outliers=True
        )

        # 创建控制面板
        control_panel = QWidget()
        control_layout = QHBoxLayout(control_panel)

        update_btn = QPushButton("更新数据")
        update_btn.clicked.connect(lambda: self.update_box_plot_data(box_plot, groups))

        outlier_cb = QComboBox()
        outlier_cb.addItems(["显示异常值", "隐藏异常值"])
        outlier_cb.currentIndexChanged.connect(
            lambda idx: box_plot.set_show_outliers(idx == 0)
        )

        method_cb = QComboBox()
        method_cb.addItems(["Tukey方法", "百分位数方法"])
        method_cb.currentIndexChanged.connect(
            lambda idx: self.change_box_plot_method(box_plot, groups,
                                               "tukey" if idx == 0 else "percentile")
        )

        clear_btn = QPushButton("清除数据")
        clear_btn.clicked.connect(lambda: self.clear_box_plot_chart_data(box_plot))

        control_layout.addWidget(update_btn)
        control_layout.addWidget(QLabel("异常值:"))
        control_layout.addWidget(outlier_cb)
        control_layout.addWidget(QLabel("检测方法:"))
        control_layout.addWidget(method_cb)
        control_layout.addWidget(clear_btn)
        control_layout.addStretch()

        layout.addWidget(control_panel)

        # 保存对象以便后续使用
        self.box_plot = box_plot

        # 添加到标签页
        self.tab_widget.addTab(tab, "箱线图")

    def update_box_plot_data(self, chart, groups):
        """更新箱线图数据"""
        try:
            data_dict = {}

            for group in groups:
                # 为每个组生成新的随机数据
                mean = np.random.uniform(50, 150)
                std = np.random.uniform(5, 15)
                data = np.random.normal(mean, std, 100)
                # 添加一些异常值
                if np.random.random() > 0.5:
                    data = np.append(data, [mean + std * 3.5, mean - std * 3.5])
                data_dict[group] = data

            # 更新箱线图数据
            chart.update_data(
                data_id="箱线图数据",
                data=data_dict
            )

            logger.info("已更新箱线图数据")
        except Exception as e:
            logger.exception(f"更新箱线图数据失败: {str(e)}")

    def change_box_plot_method(self, chart, groups, method):
        """更改箱线图异常值检测方法"""
        try:
            # 获取现有数据
            data_dict = {}

            for group in groups:
                # 为每个组生成新的随机数据
                mean = np.random.uniform(50, 150)
                std = np.random.uniform(5, 15)
                data = np.random.normal(mean, std, 100)
                # 添加一些异常值
                if np.random.random() > 0.5:
                    data = np.append(data, [mean + std * 3.5, mean - std * 3.5])
                data_dict[group] = data

            # 更新箱线图数据，使用新的方法
            chart.update_data(
                data_id="箱线图数据",
                data=data_dict,
                method=method
            )

            logger.info(f"已更改箱线图检测方法为: {method}")
        except Exception as e:
            logger.exception(f"更改箱线图检测方法失败: {str(e)}")

    def clear_box_plot_chart_data(self, chart):
        """清除箱线图数据并强制更新视图"""
        chart.clear_data()
        chart.initialize()  # 重新初始化图表以强制刷新
        logger.info("已清除箱线图数据")

    def init_radar_chart_tab(self):
        """初始化雷达图标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # 创建图表管理器
        chart_manager = ChartManager(layout_type=LayoutType.GRID)
        layout.addWidget(chart_manager)

        # 创建雷达图
        radar_chart = RadarChart(title="雷达图示例")
        chart_id = chart_manager.add_chart(radar_chart)

        # 设置雷达图类别
        categories = ["强度", "耐力", "敏捷", "智力", "魅力", "幸运"]
        radar_chart.set_categories(categories)

        # 添加数据
        # 第一个角色
        values1 = [80, 65, 90, 75, 60, 70]
        radar_chart.add_data(
            data_id="角色1",
            data=dict(zip(categories, values1)),
            name="战士",
            color=(255, 0, 0),
            fill_color=(255, 0, 0, 100)
        )

        # 第二个角色
        values2 = [50, 60, 70, 95, 80, 60]
        radar_chart.add_data(
            data_id="角色2",
            data=dict(zip(categories, values2)),
            name="法师",
            color=(0, 0, 255),
            fill_color=(0, 0, 255, 100)
        )

        # 创建控制面板
        control_panel = QWidget()
        control_layout = QHBoxLayout(control_panel)

        update_btn = QPushButton("更新数据")
        update_btn.clicked.connect(lambda: self.update_radar_chart_data(radar_chart, categories))

        fill_cb = QComboBox()
        fill_cb.addItems(["有填充", "无填充"])
        fill_cb.currentIndexChanged.connect(
            lambda idx: self.toggle_radar_fill(radar_chart, categories, idx == 0)
        )

        clear_btn = QPushButton("清除数据")
        clear_btn.clicked.connect(lambda: self.clear_radar_chart_data(radar_chart))

        control_layout.addWidget(update_btn)
        control_layout.addWidget(QLabel("填充:"))
        control_layout.addWidget(fill_cb)
        control_layout.addWidget(clear_btn)
        control_layout.addStretch()

        layout.addWidget(control_panel)

        # 保存对象以便后续使用
        self.radar_chart = radar_chart

        # 添加到标签页
        self.tab_widget.addTab(tab, "雷达图")

    def update_radar_chart_data(self, chart, categories):
        """更新雷达图数据"""
        try:
            # 生成新的随机数据
            values1 = np.random.randint(50, 100, len(categories))
            values2 = np.random.randint(50, 100, len(categories))

            # 更新数据
            chart.update_data("角色1", dict(zip(categories, values1)))
            chart.update_data("角色2", dict(zip(categories, values2)))

            logger.info("已更新雷达图数据")
        except Exception as e:
            logger.exception(f"更新雷达图数据失败: {str(e)}")

    def toggle_radar_fill(self, chart, categories, show_fill):
        """切换雷达图填充"""
        try:
            if show_fill:
                # 添加填充色
                chart.update_data(
                    data_id="角色1",
                    fill_color=(255, 0, 0, 100)
                )
                chart.update_data(
                    data_id="角色2",
                    fill_color=(0, 0, 255, 100)
                )
            else:
                # 移除填充色
                chart.update_data(
                    data_id="角色1",
                    fill_color=None
                )
                chart.update_data(
                    data_id="角色2",
                    fill_color=None
                )

            logger.info(f"已{'启用' if show_fill else '禁用'}雷达图填充")
        except Exception as e:
            logger.exception(f"切换雷达图填充失败: {str(e)}")

    def clear_radar_chart_data(self, chart):
        """清除雷达图数据并强制更新视图"""
        chart.clear_data()
        chart.initialize()  # 重新初始化图表以强制刷新
        logger.info("已清除雷达图数据")

    def update_realtime_data(self):
        """更新实时数据，由定时器触发"""
        # 这里可以添加实时数据更新的逻辑
        pass

    def init_stacked_charts(self):
        """初始化堆叠布局的所有图表"""
        # 清空现有内容
        for i in reversed(range(self.stacked_layout.count())):
            widget = self.stacked_layout.itemAt(i).widget()
            if widget:
                widget.setParent(None)

        # 创建所有图表类型的容器
        chart_types = [
            ("折线图", self.create_line_chart),
            ("散点图", self.create_scatter_chart),
            ("时间序列图", self.create_time_series_chart),
            ("柱状图", self.create_histogram_chart),
            ("热力图", self.create_heatmap_chart),
            ("饼图", self.create_pie_chart),
            ("箱线图", self.create_box_plot_chart),
            ("雷达图", self.create_radar_chart)
        ]

        # 为每种图表类型创建一个容器
        for chart_name, create_func in chart_types:
            # 创建图表容器
            chart_container = QWidget()
            chart_container.setMinimumHeight(400)
            chart_container.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)

            # 创建布局
            container_layout = QVBoxLayout(chart_container)

            # 添加标题
            title_label = QLabel(f"<h2>{chart_name}</h2>")
            title_label.setAlignment(Qt.AlignCenter)
            container_layout.addWidget(title_label)

            # 创建图表
            chart_widget = create_func()
            container_layout.addWidget(chart_widget)

            # 添加到堆叠布局
            self.stacked_layout.addWidget(chart_container)

        # 添加一些间距
        self.stacked_layout.addStretch()

        logger.info("堆叠布局的所有图表初始化完成")

    def create_line_chart(self):
        """创建折线图控件"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(0, 0, 0, 0)

        # 创建图表管理器
        chart_manager = ChartManager(layout_type=LayoutType.GRID)
        layout.addWidget(chart_manager)

        # 创建折线图
        line_chart = LineChart(title="折线图示例", x_label="X轴", y_label="Y轴")
        chart_manager.add_chart(line_chart)

        # 添加初始数据
        x_data = np.linspace(0, 10, 100)
        y_data = np.sin(x_data)
        line_chart.add_data(
            data_id="线条1",
            data=(x_data, y_data),
            name="正弦波",
            symbol="o",
            symbol_size=5
        )

        y_data2 = np.cos(x_data)
        line_chart.add_data(
            data_id="线条2",
            data=(x_data, y_data2),
            name="余弦波",
            symbol="t",
            symbol_size=5
        )

        # 创建控制面板
        control_panel = QWidget()
        control_layout = QHBoxLayout(control_panel)

        update_btn = QPushButton("更新数据")
        update_btn.clicked.connect(lambda: self.update_line_chart_data(line_chart))

        clear_btn = QPushButton("清除数据")
        clear_btn.clicked.connect(lambda: self.clear_line_chart_data(line_chart))

        control_layout.addWidget(update_btn)
        control_layout.addWidget(clear_btn)
        control_layout.addStretch()

        layout.addWidget(control_panel)

        return widget

    def create_scatter_chart(self):
        """创建散点图控件"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(0, 0, 0, 0)

        # 创建图表管理器
        chart_manager = ChartManager(layout_type=LayoutType.GRID)
        layout.addWidget(chart_manager)

        # 创建散点图
        scatter_chart = ScatterChart(title="散点图示例", x_label="X轴", y_label="Y轴")
        chart_manager.add_chart(scatter_chart)

        # 添加初始数据 - 随机点
        x_data = np.random.normal(0, 1, 100)
        y_data = np.random.normal(0, 1, 100)
        scatter_chart.add_data(
            data_id="点集1",
            data=(x_data, y_data),
            name="随机点",
            symbol="o",
            symbol_size=8
        )

        # 添加第二组数据 - 聚类效果
        cluster1_x = np.random.normal(-2, 0.5, 50)
        cluster1_y = np.random.normal(2, 0.5, 50)
        scatter_chart.add_data(
            data_id="点集2",
            data=(cluster1_x, cluster1_y),
            name="簇1",
            symbol="t",
            symbol_size=8,
            color=(255, 0, 0)
        )

        # 添加第三组数据 - 另一个聚类
        cluster2_x = np.random.normal(2, 0.5, 50)
        cluster2_y = np.random.normal(-2, 0.5, 50)
        scatter_chart.add_data(
            data_id="点集3",
            data=(cluster2_x, cluster2_y),
            name="簇2",
            symbol="s",
            symbol_size=8,
            color=(0, 255, 0)
        )

        # 创建控制面板
        control_panel = QWidget()
        control_layout = QHBoxLayout(control_panel)

        update_btn = QPushButton("更新数据")
        update_btn.clicked.connect(lambda: self.update_scatter_chart_data(scatter_chart))

        clear_btn = QPushButton("清除数据")
        clear_btn.clicked.connect(lambda: self.clear_scatter_chart_data(scatter_chart))

        control_layout.addWidget(update_btn)
        control_layout.addWidget(clear_btn)
        control_layout.addStretch()

        layout.addWidget(control_panel)

        return widget

    def create_time_series_chart(self):
        """创建时间序列图控件"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(0, 0, 0, 0)

        # 创建图表管理器
        chart_manager = ChartManager(layout_type=LayoutType.GRID)
        layout.addWidget(chart_manager)

        # 创建时间序列图
        time_series_chart = TimeSeriesChart(title="时间序列图示例", x_label="时间", y_label="数值")
        chart_manager.add_chart(time_series_chart)

        # 添加初始数据 - 过去30天的数据
        end_date = datetime.now()
        start_date = end_date - timedelta(days=30)
        dates = [start_date + timedelta(days=i) for i in range(31)]

        # 模拟股票价格
        base_value = 100
        values = [base_value]
        for i in range(1, 31):
            change = np.random.normal(0, 1) * 2  # 每天随机变动
            values.append(values[-1] + change)

        time_series_chart.add_data(
            data_id="股票价格",
            data=(dates, values),
            name="股票价格",
            color=(0, 0, 255),
            line_width=2
        )

        # 添加第二组数据 - 成交量
        volume = np.random.randint(1000, 5000, 31)
        time_series_chart.add_data(
            data_id="成交量",
            data=(dates, volume),
            name="成交量",
            color=(255, 0, 0),
            axis="right",
            line_width=1.5,
            symbol="t",
            symbol_size=6
        )

        # 创建控制面板
        control_panel = QWidget()
        control_layout = QHBoxLayout(control_panel)

        add_point_btn = QPushButton("添加数据点")
        add_point_btn.clicked.connect(lambda: self.add_time_series_point(time_series_chart))

        clear_btn = QPushButton("清除数据")
        clear_btn.clicked.connect(lambda: self.clear_time_series_chart_data(time_series_chart))

        control_layout.addWidget(add_point_btn)
        control_layout.addWidget(clear_btn)
        control_layout.addStretch()

        layout.addWidget(control_panel)

        return widget

    def create_histogram_chart(self):
        """创建柱状图控件"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(0, 0, 0, 0)

        # 创建图表管理器
        chart_manager = ChartManager(layout_type=LayoutType.GRID)
        layout.addWidget(chart_manager)

        # 创建柱状图
        histogram_chart = HistogramChart(title="柱状图示例", x_label="类别", y_label="数值")
        chart_manager.add_chart(histogram_chart)

        # 设置类别
        categories = ["类别A", "类别B", "类别C", "类别D", "类别E"]
        histogram_chart.set_categories(categories)

        # 添加数据
        values = np.random.randint(10, 100, len(categories))
        histogram_chart.add_data(
            data_id="数据集1",
            data=values,
            name="数据集1",
            offset=-0.2,  # 为第一组数据添加负偏移量
            bar_width=0.4  # 设置合适的宽度
        )

        # 添加第二组数据（用于分组对比）
        values2 = np.random.randint(10, 100, len(categories))
        histogram_chart.add_data(
            data_id="数据集2",
            data=values2,
            name="数据集2",
            offset=0.2,  # 保持正偏移量
            bar_width=0.4,  # 设置相同的宽度
            color=(0, 0, 255)
        )

        # 创建控制面板
        control_panel = QWidget()
        control_layout = QHBoxLayout(control_panel)

        update_btn = QPushButton("更新数据")
        update_btn.clicked.connect(lambda: self.update_histogram_data(histogram_chart, categories))

        group_btn = QPushButton("创建分组柱状图")
        group_btn.clicked.connect(lambda: self.create_grouped_bars(histogram_chart, categories))

        stack_btn = QPushButton("创建堆叠柱状图")
        stack_btn.clicked.connect(lambda: self.create_stacked_bars(histogram_chart, categories))

        clear_btn = QPushButton("清除数据")
        clear_btn.clicked.connect(lambda: self.clear_histogram_chart_data(histogram_chart))

        control_layout.addWidget(update_btn)
        control_layout.addWidget(group_btn)
        control_layout.addWidget(stack_btn)
        control_layout.addWidget(clear_btn)
        control_layout.addStretch()

        layout.addWidget(control_panel)

        return widget

    def create_heatmap_chart(self):
        """创建热力图控件"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(0, 0, 0, 0)

        # 创建图表管理器
        chart_manager = ChartManager(layout_type=LayoutType.GRID)
        layout.addWidget(chart_manager)

        # 创建热力图
        heatmap_chart = HeatmapChart(title="热力图示例", x_label="X轴", y_label="Y轴")
        chart_manager.add_chart(heatmap_chart)

        # 创建示例数据 - 20x20的热力图
        data = np.zeros((20, 20))
        # 添加一些模式
        for i in range(20):
            for j in range(20):
                # 创建一些有趣的图案
                data[i, j] = np.sin(i/5.0) * np.cos(j/5.0) * 0.5 + 0.5

        # X和Y轴标签
        x_labels = [f"X{i}" for i in range(20)]
        y_labels = [f"Y{i}" for i in range(20)]

        # 添加热力图数据
        heatmap_chart.add_data(
            data_id="热力图数据",
            data=data,
            x_labels=x_labels,
            y_labels=y_labels
        )

        # 创建控制面板
        control_panel = QWidget()
        control_layout = QHBoxLayout(control_panel)

        update_btn = QPushButton("更新数据")
        update_btn.clicked.connect(lambda: self.update_heatmap_data(heatmap_chart, x_labels, y_labels))

        color_map_combo = QComboBox()
        color_map_combo.addItems(["热力图", "彩虹", "寒冷", "黑白"])
        color_map_combo.currentIndexChanged.connect(
            lambda idx: self.change_heatmap_colormap(heatmap_chart, idx)
        )

        clear_btn = QPushButton("清除数据")
        clear_btn.clicked.connect(lambda: self.clear_heatmap_chart_data(heatmap_chart))

        control_layout.addWidget(update_btn)
        control_layout.addWidget(QLabel("颜色映射:"))
        control_layout.addWidget(color_map_combo)
        control_layout.addWidget(clear_btn)
        control_layout.addStretch()

        layout.addWidget(control_panel)

        return widget

    def create_pie_chart(self):
        """创建饼图控件"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(0, 0, 0, 0)

        # 创建图表管理器
        chart_manager = ChartManager(layout_type=LayoutType.GRID)
        layout.addWidget(chart_manager)

        # 创建饼图
        pie_chart = PieChart(title="饼图示例")
        chart_manager.add_chart(pie_chart)

        # 添加数据
        labels = ["类别A", "类别B", "类别C", "类别D", "类别E"]
        values = np.random.randint(10, 100, len(labels))

        pie_chart.add_data(
            data_id="饼图数据",
            data=dict(zip(labels, values)),
            explode=[0, 0, 0.1, 0, 0],  # 突出显示第三个扇区
            start_angle=90
        )

        # 创建控制面板
        control_panel = QWidget()
        control_layout = QHBoxLayout(control_panel)

        update_btn = QPushButton("更新数据")
        update_btn.clicked.connect(lambda: self.update_pie_chart_data(pie_chart, labels))

        donut_btn = QPushButton("甜甜圈图")
        donut_btn.clicked.connect(lambda: pie_chart.set_hole_size(0.4))

        pie_btn = QPushButton("饼图")
        pie_btn.clicked.connect(lambda: pie_chart.set_hole_size(0))

        clear_btn = QPushButton("清除数据")
        clear_btn.clicked.connect(lambda: self.clear_pie_chart_data(pie_chart))

        control_layout.addWidget(update_btn)
        control_layout.addWidget(donut_btn)
        control_layout.addWidget(pie_btn)
        control_layout.addWidget(clear_btn)
        control_layout.addStretch()

        layout.addWidget(control_panel)

        return widget

    def create_box_plot_chart(self):
        """创建箱线图控件"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(0, 0, 0, 0)

        # 创建图表管理器
        chart_manager = ChartManager(layout_type=LayoutType.GRID)
        layout.addWidget(chart_manager)

        # 创建箱线图
        box_plot = BoxPlotChart(title="箱线图示例", x_label="组别", y_label="数值")
        chart_manager.add_chart(box_plot)

        # 生成示例数据
        groups = ["组A", "组B", "组C", "组D", "组E"]
        data_dict = {}

        for group in groups:
            # 为每个组生成随机数据
            mean = np.random.uniform(50, 150)
            std = np.random.uniform(5, 15)
            data = np.random.normal(mean, std, 100)
            # 添加一些异常值
            if np.random.random() > 0.5:
                data = np.append(data, [mean + std * 3.5, mean - std * 3.5])
            data_dict[group] = data

        # 添加数据到箱线图
        box_plot.add_data(
            data_id="箱线图数据",
            data=data_dict,
            name="示例数据",
            method="tukey",  # 使用Tukey方法检测异常值
            show_outliers=True
        )

        # 创建控制面板
        control_panel = QWidget()
        control_layout = QHBoxLayout(control_panel)

        update_btn = QPushButton("更新数据")
        update_btn.clicked.connect(lambda: self.update_box_plot_data(box_plot, groups))

        outlier_cb = QComboBox()
        outlier_cb.addItems(["显示异常值", "隐藏异常值"])
        outlier_cb.currentIndexChanged.connect(
            lambda idx: box_plot.set_show_outliers(idx == 0)
        )

        method_cb = QComboBox()
        method_cb.addItems(["Tukey方法", "百分位数方法"])
        method_cb.currentIndexChanged.connect(
            lambda idx: self.change_box_plot_method(box_plot, groups,
                                               "tukey" if idx == 0 else "percentile")
        )

        clear_btn = QPushButton("清除数据")
        clear_btn.clicked.connect(lambda: self.clear_box_plot_chart_data(box_plot))

        control_layout.addWidget(update_btn)
        control_layout.addWidget(QLabel("异常值:"))
        control_layout.addWidget(outlier_cb)
        control_layout.addWidget(QLabel("检测方法:"))
        control_layout.addWidget(method_cb)
        control_layout.addWidget(clear_btn)
        control_layout.addStretch()

        layout.addWidget(control_panel)

        return widget

    def create_radar_chart(self):
        """创建雷达图控件"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(0, 0, 0, 0)

        # 创建图表管理器
        chart_manager = ChartManager(layout_type=LayoutType.GRID)
        layout.addWidget(chart_manager)

        # 创建雷达图
        radar_chart = RadarChart(title="雷达图示例")
        chart_manager.add_chart(radar_chart)

        # 设置雷达图类别
        categories = ["强度", "耐力", "敏捷", "智力", "魅力", "幸运"]
        radar_chart.set_categories(categories)

        # 添加数据
        # 第一个角色
        values1 = [80, 65, 90, 75, 60, 70]
        radar_chart.add_data(
            data_id="角色1",
            data=dict(zip(categories, values1)),
            name="战士",
            color=(255, 0, 0),
            fill_color=(255, 0, 0, 100)
        )

        # 第二个角色
        values2 = [50, 60, 70, 95, 80, 60]
        radar_chart.add_data(
            data_id="角色2",
            data=dict(zip(categories, values2)),
            name="法师",
            color=(0, 0, 255),
            fill_color=(0, 0, 255, 100)
        )

        # 创建控制面板
        control_panel = QWidget()
        control_layout = QHBoxLayout(control_panel)

        update_btn = QPushButton("更新数据")
        update_btn.clicked.connect(lambda: self.update_radar_chart_data(radar_chart, categories))

        fill_cb = QComboBox()
        fill_cb.addItems(["有填充", "无填充"])
        fill_cb.currentIndexChanged.connect(
            lambda idx: self.toggle_radar_fill(radar_chart, categories, idx == 0)
        )

        clear_btn = QPushButton("清除数据")
        clear_btn.clicked.connect(lambda: self.clear_radar_chart_data(radar_chart))

        control_layout.addWidget(update_btn)
        control_layout.addWidget(QLabel("填充:"))
        control_layout.addWidget(fill_cb)
        control_layout.addWidget(clear_btn)
        control_layout.addStretch()

        layout.addWidget(control_panel)

        return widget


if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = AllChartsExampleWindow()
    window.show()
    sys.exit(app.exec_())