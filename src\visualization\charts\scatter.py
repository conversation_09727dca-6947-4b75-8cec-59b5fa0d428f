#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
散点图模块

实现基于PyQtGraph的散点图，支持多组数据、自定义点样式和颜色等功能。
"""

import numpy as np
from typing import Dict, List, Optional, Tuple, Union, Any

try:
    import pyqtgraph as pg
    from pyqtgraph.Qt import QtCore, QtGui
except ImportError:
    # 如果导入失败，可能会在运行时另行处理
    pass

from ..base_chart import BaseChart, ChartType
from ...utils.logger import logger


class ScatterChart(BaseChart):
    """
    散点图类
    
    实现散点图的创建、数据管理和样式设置等功能。
    """
    
    def __init__(
        self, 
        title: str = "散点图", 
        x_label: str = "X轴", 
        y_label: str = "Y轴"
    ):
        """
        初始化散点图
        
        Args:
            title: 图表标题
            x_label: X轴标签
            y_label: Y轴标签
        """
        super().__init__(title, x_label, y_label, ChartType.SCATTER)
        
        # 散点图特有的属性
        self._symbol_sizes = {}  # 存储每组散点的大小
        self._symbol_pens = {}  # 存储每组散点的边框样式
        self._symbol_brushes = {}  # 存储每组散点的填充样式
    
    def _initialize_specific(self):
        """实现图表特定的初始化"""
        # 散点图特有的初始化操作（如果有）
        pass
    
    def add_data(
        self, 
        data_id: str, 
        data: Union[Tuple[List, List], np.ndarray], 
        **kwargs
    ):
        """
        添加数据到散点图
        
        Args:
            data_id: 数据标识符
            data: 数据内容，可以是 (x, y) 元组或二维数组
            **kwargs: 额外配置参数，包括：
                - name: 散点名称（显示在图例中）
                - color: 散点颜色，格式为 (R,G,B) 或 '#RRGGBB'
                - symbol: 点标记样式，如 'o', 't', '+', 'd', 's' 等
                - symbol_size: 点标记大小
                - symbol_brush: 点标记填充颜色
                - symbol_pen: 点标记边框样式，如 (color, width)
        """
        if not self._initialized:
            self.initialize()
        
        try:
            # 解析数据
            x_data, y_data = self._parse_data(data)
            
            # 解析样式参数
            name = kwargs.get('name', data_id)
            color = kwargs.get('color', None)
            symbol = kwargs.get('symbol', 'o')
            symbol_size = kwargs.get('symbol_size', 10)
            symbol_brush = kwargs.get('symbol_brush', color)
            symbol_pen = kwargs.get('symbol_pen', None)
            
            # 如果没有指定颜色，使用自动生成的颜色
            if color is None:
                # 使用预定义的颜色列表
                colors = [
                    (255, 0, 0),      # 红色
                    (0, 0, 255),      # 蓝色
                    (0, 255, 0),      # 绿色
                    (255, 165, 0),    # 橙色
                    (128, 0, 128),    # 紫色
                    (255, 192, 203),  # 粉红色
                    (165, 42, 42),    # 棕色
                    (0, 255, 255),    # 青色
                ]
                color = colors[len(self._data_items) % len(colors)]
            
            # 设置符号边框
            if symbol_pen is None:
                if isinstance(color, tuple) and len(color) == 3:
                    # 使用稍深的颜色作为边框
                    border_color = tuple(max(0, c - 50) for c in color)
                    symbol_pen = pg.mkPen(color=border_color, width=1)
                else:
                    symbol_pen = pg.mkPen(color=color, width=1)
            
            # 设置符号填充
            if symbol_brush is None:
                symbol_brush = pg.mkBrush(color)
            elif isinstance(symbol_brush, tuple) and len(symbol_brush) == 3:
                symbol_brush = pg.mkBrush(symbol_brush)
            
            # 创建散点图数据项
            scatter_item = pg.ScatterPlotItem(
                x=x_data,
                y=y_data,
                symbol=symbol,
                size=symbol_size,
                pen=symbol_pen,
                brush=symbol_brush,
                name=name
            )
            
            # 添加到绘图项
            self._plot_item.addItem(scatter_item)
            
            # 存储数据项和样式信息
            self._data_items[data_id] = scatter_item
            self._symbol_sizes[data_id] = symbol_size
            self._symbol_pens[data_id] = symbol_pen
            self._symbol_brushes[data_id] = symbol_brush
            
            logger.debug(f"{self._log_prefix} 添加散点: {data_id}, 数据点数: {len(x_data)}")
            
        except Exception as e:
            logger.error(f"{self._log_prefix} 添加数据失败: {str(e)}")
            raise
    
    def update_data(
        self, 
        data_id: str, 
        data: Union[Tuple[List, List], np.ndarray], 
        **kwargs
    ):
        """
        更新散点图中的数据
        
        Args:
            data_id: 数据标识符
            data: 新的数据内容
            **kwargs: 额外的样式参数，与add_data相同
        """
        if not self._initialized or data_id not in self._data_items:
            if data_id not in self._data_items:
                # 如果数据不存在，则添加新数据
                self.add_data(data_id, data, **kwargs)
            return
        
        try:
            # 解析数据
            x_data, y_data = self._parse_data(data)
            
            # 获取散点项
            scatter_item = self._data_items[data_id]
            
            # 检查是否更新样式
            symbol = kwargs.get('symbol', scatter_item.opts.get('symbol', 'o'))
            symbol_size = kwargs.get('symbol_size', self._symbol_sizes.get(data_id, 10))
            symbol_pen = kwargs.get('symbol_pen', self._symbol_pens.get(data_id))
            symbol_brush = kwargs.get('symbol_brush', self._symbol_brushes.get(data_id))
            
            # 更新点样式和数据
            scatter_item.setData(
                x=x_data,
                y=y_data,
                symbol=symbol,
                size=symbol_size,
                pen=symbol_pen,
                brush=symbol_brush
            )
            
            # 更新样式信息
            self._symbol_sizes[data_id] = symbol_size
            self._symbol_pens[data_id] = symbol_pen
            self._symbol_brushes[data_id] = symbol_brush
            
            logger.debug(f"{self._log_prefix} 更新散点: {data_id}, 数据点数: {len(x_data)}")
            
        except Exception as e:
            logger.error(f"{self._log_prefix} 更新数据失败: {str(e)}")
            raise
    
    def _parse_data(self, data) -> Tuple[List, List]:
        """
        解析数据格式
        
        Args:
            data: 输入数据，可以是 (x, y) 元组或二维数组
            
        Returns:
            Tuple[List, List]: x和y数据列表
        """
        if isinstance(data, tuple) and len(data) == 2:
            # 如果是 (x, y) 元组
            x_data, y_data = data
            
            # 确保是列表类型
            if isinstance(x_data, np.ndarray):
                x_data = x_data.tolist()
            if isinstance(y_data, np.ndarray):
                y_data = y_data.tolist()
                
            return x_data, y_data
            
        elif isinstance(data, np.ndarray):
            # 如果是二维数组
            if data.ndim == 2 and data.shape[1] == 2:
                # 如果是 [[x1, y1], [x2, y2], ...] 格式
                return data[:, 0].tolist(), data[:, 1].tolist()
            elif data.ndim == 1:
                # 如果是一维数组，使用索引作为X轴
                return list(range(len(data))), data.tolist()
        
        # 其他情况，尝试转换为列表
        if hasattr(data, '__iter__'):
            return list(range(len(data))), list(data)
        
        raise ValueError(f"不支持的数据格式: {type(data)}")
    
    def set_point_size(self, data_id: str, size: float):
        """
        设置指定散点组的点大小
        
        Args:
            data_id: 数据标识符
            size: 点大小
        """
        if not self._initialized or data_id not in self._data_items:
            return
        
        scatter_item = self._data_items[data_id]
        scatter_item.setSize(size)
        self._symbol_sizes[data_id] = size
    
    def set_point_symbol(self, data_id: str, symbol: str):
        """
        设置指定散点组的符号类型
        
        Args:
            data_id: 数据标识符
            symbol: 符号类型，如 'o', 't', '+', 's', 'd' 等
        """
        if not self._initialized or data_id not in self._data_items:
            return
        
        scatter_item = self._data_items[data_id]
        scatter_item.setSymbol(symbol)
    
    def set_point_color(self, data_id: str, color):
        """
        设置指定散点组的颜色
        
        Args:
            data_id: 数据标识符
            color: 颜色，格式为 (R,G,B) 或 '#RRGGBB'
        """
        if not self._initialized or data_id not in self._data_items:
            return
        
        scatter_item = self._data_items[data_id]
        brush = pg.mkBrush(color)
        scatter_item.setBrush(brush)
        self._symbol_brushes[data_id] = brush
        
        # 更新边框颜色
        if isinstance(color, tuple) and len(color) == 3:
            border_color = tuple(max(0, c - 50) for c in color)
            pen = pg.mkPen(color=border_color, width=1)
            scatter_item.setPen(pen)
            self._symbol_pens[data_id] = pen
