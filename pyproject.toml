[project]
name = "data-show"
version = "0.1.0"
description = "基于Python(Qt)的数据可视化软件，支持多协议硬件接口和丰富图表类型"
readme = "README.md"
requires-python = ">=3.8"
license = {text = "MIT"}
authors = [
    {name = "buaalyz", email = "<EMAIL>"}
]
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Science/Research",
    "Operating System :: Microsoft :: Windows",
    "Programming Language :: Python :: 3.8"
]
dependencies = [
    # 核心框架
    "PyQt5>=5.15.9",
    
    # 数据可视化
    "pyqtgraph>=0.13.3",
    "matplotlib>=3.7.0",
    
    # 硬件协议
    "pymodbus>=3.5.4",
    "pyserial>=3.5",
    "python-can>=4.3.1",
    "paho-mqtt>=2.0.0",
    "opcua>=0.98.13",
    
    # 数据处理
    "numpy>=1.24.0",
    "pandas>=2.0.0",
    "scipy>=1.10.0",
    
    # 辅助工具
    "python-dotenv>=1.0.0",
]

[project.urls]
Homepage = "https://github.com/liuyazui/data-show"
Documentation = "https://github.com/liuyazui/data-show/docs"

[build-system]
requires = ["setuptools>=42"]
build-backend = "setuptools.build_meta"

[project.optional-dependencies]
dev = [
    "pytest>=7.0",
    "black>=23.0",
    "mypy>=1.0",
    "pylint>=2.15"
]
qt6 = ["PyQt6>=6.5.0", "PySide6>=6.5.0"]
full = [
    "PyQt6>=6.5.0",
    "PySide6>=6.5.0",
    "opcua-client>=0.98.0"
]

[project.scripts]
data-show = "src.core.app:main"
