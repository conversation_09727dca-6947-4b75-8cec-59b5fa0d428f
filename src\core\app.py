#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
应用程序入口模块

该模块作为整个应用的启动点，负责初始化各个组件和启动应用程序。
"""

import datetime
import logging
import os
import sys
import traceback
from pathlib import Path
from typing import Optional

# 确保可以导入其他模块
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..')))

try:
    from PyQt5.QtCore import QObject, pyqtSignal, QTimer, QSettings
    from PyQt5.QtWidgets import QApplication, QMainWindow, QMessageBox, QSplashScreen
    from PyQt5.QtGui import QPixmap, QIcon
    from PyQt5 import uic
    qt_lib = "PyQt5"
except ImportError:
    try:
        from PySide6.QtCore import QObject, Signal as pyqtSignal, QTimer, QSettings
        from PySide6.QtWidgets import QApplication, QMainWindow, QMessageBox, QSplashScreen
        from PySide6.QtGui import QPixmap, QIcon
        from PySide6 import QtUiTools, QtCore
        qt_lib = "PySide6"
    except ImportError:
        print("错误：找不到 PyQt5 或 PySide6 库。请安装其中一个。")
        sys.exit(1)

from core.config import Config
from core.event_bus import event_bus, AppEvents
from core.thread_manager import thread_manager

# 全局变量
config = Config()
logger = logging.getLogger(__name__)
app_instance = None

class AppSignals(QObject):
    """应用程序信号类，用于在线程间传递信号"""
    error_occurred = pyqtSignal(str, str)  # 错误信息, 详细信息
    status_message = pyqtSignal(str)       # 状态栏消息

class Application:
    """应用程序类，管理应用生命周期"""
    
    def __init__(self):
        """初始化应用程序"""
        global app_instance
        app_instance = self
        
        self.app = QApplication(sys.argv)
        self.app.setApplicationName(config.get("app.name", "Data-Show"))
        self.app.setApplicationVersion(config.get("app.version", "0.1.0"))
        
        self.signals = AppSignals()
        self.main_window = None
        self.splash = None
        
        # 连接信号
        self.signals.error_occurred.connect(self.show_error_dialog)
        
        # 设置异常钩子
        sys.excepthook = self.exception_hook
        
        # 注册应用事件
        event_bus.register(AppEvents.EXCEPTION, self.handle_exception)
        
    def setup_logging(self, force_debug=False):
        """设置日志系统"""
        # 确保main_window已创建，以便获取logDisplay控件
        if not self.main_window or not hasattr(self.main_window, 'logDisplay'):
            # 如果UI尚未加载，则仅设置基本的控制台日志
            log_level = getattr(logging, config.get("app.log_level", "INFO"))
            if force_debug:
                log_level = logging.DEBUG
            logging.basicConfig(
                level=log_level,
                format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                handlers=[logging.StreamHandler()]
            )
            logger.info(f"基本日志系统初始化完成，日志级别: {logging.getLevelName(log_level)}")
            return
            
        try:
            # 导入日志管理器
            from src.utils.log_manager import log_manager
            
            # 获取配置的日志级别
            log_level_name = config.get("app.log_level", "INFO")
            if force_debug:
                log_level = logging.DEBUG
            else:
                log_level = getattr(logging, log_level_name)
            
            # 获取日志目录
            log_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'logs')
            
            # 初始化日志管理器
            success = log_manager.initialize(
                log_widget=self.main_window.logDisplay,
                log_level=log_level,
                log_dir=log_dir,
                enable_file_logging=True
            )
            
            if success:
                logger.info(f"日志系统初始化完成，日志级别: {logging.getLevelName(log_level)}")
                
                # 连接logger.py中的日志系统到UI
                connect_success = log_manager.connect_logger()
                if connect_success:
                    logger.debug("成功连接logger模块的日志系统到UI显示")
                else:
                    logger.warning("无法连接logger模块的日志系统到UI显示")
            else:
                # 初始化失败时使用基本日志
                logging.basicConfig(
                    level=log_level,
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                    handlers=[logging.StreamHandler()]
                )
                logger.warning("日志管理器初始化失败，使用基本日志系统")
                
        except Exception as e:
            # 出现异常时使用基本日志
            log_level = getattr(logging, config.get("app.log_level", "INFO"))
            if force_debug:
                log_level = logging.DEBUG
            logging.basicConfig(
                level=log_level,
                format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                handlers=[logging.StreamHandler()]
            )
            logger.error(f"初始化日志系统失败: {str(e)}")
            logger.debug(traceback.format_exc())
        
    def show_splash_screen(self):
        """显示启动画面"""
        try:
            splash_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 
                                      'resources', 'images', 'splash.png')
            
            if os.path.exists(splash_path):
                self.splash = QSplashScreen(QPixmap(splash_path))
                self.splash.show()
                QApplication.processEvents()
        except Exception as e:
            logger.warning(f"无法显示启动画面: {str(e)}")
            
    def create_main_window(self):
        """创建主窗口并加载UI文件"""
        ui_file_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))),
                                   'src', 'ui', 'main_window.ui')

        if not os.path.exists(ui_file_path):
            logger.error(f"UI文件未找到: {ui_file_path}")
            # 可以选择创建一个默认的空窗口或退出
            self.main_window = QMainWindow()
            self.main_window.setWindowTitle("错误 - UI文件丢失")
            QMessageBox.critical(None, "错误", f"UI文件丢失: {ui_file_path}") # 添加错误提示
            return

        try:
            if qt_lib == "PyQt5":
                # 使用 uic 加载 UI 文件 (PyQt5)
                self.main_window = uic.loadUi(ui_file_path)
            elif qt_lib == "PySide6":
                 # 使用 QUiLoader 加载 UI 文件 (PySide6)
                 loader = QtUiTools.QUiLoader()
                 file = QtCore.QFile(ui_file_path)
                 file.open(QtCore.QFile.ReadOnly)
                 self.main_window = loader.load(file, None) # parent=None
                 file.close()


            if not self.main_window: # 检查加载是否成功
                 raise RuntimeError("加载UI文件返回了 None")


            logger.info("主窗口UI加载完成")

            # 连接一些基础信号和槽
            if hasattr(self.main_window, 'actionExit'):
                self.main_window.actionExit.triggered.connect(self.app.quit)

            # 切换工具栏和状态栏的可见性
            if hasattr(self.main_window, 'actionToggleToolbar') and hasattr(self.main_window, 'toolBar'):
                self.main_window.actionToggleToolbar.triggered.connect(self.main_window.toolBar.setVisible)
                self.main_window.toolBar.setVisible(self.main_window.actionToggleToolbar.isChecked()) # 初始化可见性
            if hasattr(self.main_window, 'actionToggleStatusbar') and hasattr(self.main_window, 'statusbar'):
                self.main_window.actionToggleStatusbar.triggered.connect(self.main_window.statusbar.setVisible)
                self.main_window.statusbar.setVisible(self.main_window.actionToggleStatusbar.isChecked()) # 初始化可见性

        except Exception as e:
            logger.critical(f"加载UI文件失败: {str(e)}")
            logger.debug(traceback.format_exc())
            # 加载失败时创建一个空窗口
            self.main_window = QMainWindow()
            self.main_window.setWindowTitle(f"错误 - UI加载失败")
            QMessageBox.critical(None, "UI加载错误", f"加载UI文件失败: {e}\n{traceback.format_exc()}") # 添加错误提示


        # 设置窗口标题 (如果 .ui 文件没有设置，虽然我们设置了)
        if not self.main_window.windowTitle():
             self.main_window.setWindowTitle(config.get("app.name", "Data-Show"))

        # 加载图标
        try:
            icon_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))),
                                    'resources', 'images', 'icon.png')
            if os.path.exists(icon_path):
                self.main_window.setWindowIcon(QIcon(icon_path))
            else:
                logger.warning(f"应用图标文件未找到: {icon_path}")
        except Exception as e:
            logger.warning(f"无法加载应用图标: {str(e)}")

        logger.info("主窗口创建完成")
        
    def show_main_window(self):
        """显示主窗口"""
        if self.splash:
            self.splash.finish(self.main_window)
        
        # 初始化默认图表
        try:
            from src.visualization.chart_initializer import chart_initializer
            success = chart_initializer.initialize_default_charts(self.main_window)
            if not success:
                logger.warning("部分图表初始化失败")
        except Exception as e:
            logger.error(f"初始化图表失败: {str(e)}")
            import traceback
            logger.debug(traceback.format_exc())
        
        # 初始化主窗口控制器
        try:
            from src.ui.main_window_controller import initialize as init_controller
            self.controller = init_controller(self.main_window)
            logger.info("主窗口控制器初始化完成")
        except Exception as e:
            logger.error(f"初始化主窗口控制器失败: {str(e)}")
            import traceback
            logger.debug(traceback.format_exc())
            
        self.main_window.show()
        logger.info("主窗口显示")
        
        # 触发窗口创建事件
        event_bus.publish(AppEvents.STARTUP)
        
    def exception_hook(self, exc_type, exc_value, exc_traceback):
        """全局异常处理钩子"""
        tb = "".join(traceback.format_exception(exc_type, exc_value, exc_traceback))
        logger.critical(f"未捕获的异常: {str(exc_value)}\n{tb}")
        
        # 在UI线程中显示错误对话框
        self.signals.error_occurred.emit(str(exc_value), tb)
        
    def handle_exception(self, exception, **kwargs):
        """处理从事件总线接收到的异常"""
        thread_id = kwargs.get('thread_id', 'unknown')
        logger.error(f"从线程 {thread_id} 接收到异常: {str(exception)}")
        
        # 在UI线程中显示错误对话框
        self.signals.error_occurred.emit(str(exception), traceback.format_exc())
        
    def show_error_dialog(self, message, details):
        """显示错误对话框"""
        msg_box = QMessageBox()
        msg_box.setIcon(QMessageBox.Critical)
        msg_box.setWindowTitle("错误")
        msg_box.setText(message)
        msg_box.setDetailedText(details)
        msg_box.setStandardButtons(QMessageBox.Ok)
        msg_box.exec_()
        
    def shutdown(self):
        """关闭应用程序"""
        logger.info("应用程序正在关闭...")
        
        # 触发应用程序关闭事件
        event_bus.publish(AppEvents.SHUTDOWN)
        
        # 保存配置
        config.save()
        
        # 清理资源
        thread_manager.shutdown()
        
        logger.info("应用程序已关闭")
        
    def run(self):
        """运行应用程序"""
        try:
            # 设置最基本的控制台日志（UI日志稍后初始化）
            # 使用DEBUG级别以捕获所有日志
            logging.basicConfig(
                level=logging.DEBUG,  # 使用DEBUG级别捕获所有级别的日志
                format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                handlers=[logging.StreamHandler()]
            )
            
            logger.debug("应用程序启动，使用DEBUG日志级别")
            
            # 显示启动画面
            self.show_splash_screen()
            
            # 创建主窗口
            self.create_main_window()
            
            # 立即初始化UI日志系统，确保捕获后续所有日志
            self.setup_logging(force_debug=True)  # 强制使用DEBUG级别
            
            # 添加测试日志，验证两套日志系统
            logger.debug("这是一条从app.py发出的DEBUG级别测试日志")
            
            # 测试logger模块的日志
            from src.utils.logger import debug as logger_debug
            logger_debug("这是一条从logger模块发出的DEBUG级别测试日志")
            
            # 显示主窗口
            self.show_main_window()
            
            # 设置应用程序关闭事件
            self.app.aboutToQuit.connect(self.shutdown)
            
            # 进入主事件循环
            return self.app.exec_()
            
        except Exception as e:
            logger.critical(f"启动失败: {str(e)}")
            logger.debug(traceback.format_exc())
            return 1

def main():
    """程序入口函数"""
    app = Application()
    sys.exit(app.run())

if __name__ == "__main__":
    main()
