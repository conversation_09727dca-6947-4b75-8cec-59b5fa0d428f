<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1280</width>
    <height>800</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Data-Show</string>
  </property>
  <widget class="QWidget" name="centralwidget">
   <layout class="QVBoxLayout" name="verticalLayout">
    <item>
     <widget class="QSplitter" name="verticalSplitter">
      <property name="orientation">
       <enum>Qt::Vertical</enum>
      </property>
      <widget class="QSplitter" name="mainSplitter">
       <property name="sizePolicy">
        <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
         <horstretch>0</horstretch>
         <verstretch>4</verstretch>
        </sizepolicy>
       </property>
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <widget class="QFrame" name="visualizationFrame">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
          <horstretch>3</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="frameShape">
         <enum>QFrame::StyledPanel</enum>
        </property>
        <property name="frameShadow">
         <enum>QFrame::Raised</enum>
        </property>
        <layout class="QVBoxLayout" name="verticalLayout_2">
         <item>
          <widget class="QLabel" name="labelVisualizationPlaceholder">
           <property name="text">
            <string>Visualization Area Placeholder</string>
           </property>
           <property name="alignment">
            <set>Qt::AlignCenter</set>
           </property>
          </widget>
         </item>
        </layout>
       </widget>
       <widget class="QFrame" name="analysisFrame">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
          <horstretch>1</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="frameShape">
         <enum>QFrame::StyledPanel</enum>
        </property>
        <property name="frameShadow">
         <enum>QFrame::Raised</enum>
        </property>
        <layout class="QVBoxLayout" name="analysisLayout">
         <property name="leftMargin">
          <number>9</number>
         </property>
         <property name="topMargin">
          <number>9</number>
         </property>
         <property name="rightMargin">
          <number>9</number>
         </property>
         <property name="bottomMargin">
          <number>9</number>
         </property>
         <item>
          <layout class="QHBoxLayout" name="buttonBarLayout">
           <item>
            <widget class="QLabel" name="analysisLabel">
             <property name="font">
              <font>
               <pointsize>10</pointsize>
               <weight>75</weight>
               <bold>true</bold>
              </font>
             </property>
             <property name="text">
              <string>数据分析</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignCenter</set>
             </property>
            </widget>
           </item>
           <item>
            <spacer name="horizontalSpacer_2">
             <property name="orientation">
              <enum>Qt::Horizontal</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>40</width>
               <height>20</height>
              </size>
             </property>
            </spacer>
           </item>
           <item>
            <widget class="QPushButton" name="addButton">
             <property name="text">
              <string>添加</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QPushButton" name="deleteButton">
             <property name="text">
              <string>删除</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QPushButton" name="exportButton">
             <property name="text">
              <string>导出</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QPushButton" name="settingsButton">
             <property name="text">
              <string>设置</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QPushButton" name="resetViewButton">
             <property name="text">
              <string>重置视图</string>
             </property>
            </widget>
           </item>
          </layout>
         </item>
         <item>
          <widget class="QStackedWidget" name="chartStackWidget">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <widget class="QWidget" name="defaultChartPage">
            <layout class="QVBoxLayout" name="verticalLayout_chart">
             <item>
              <widget class="QLabel" name="chartAreaTitleLabel">
               <property name="text">
                <string>图表绘制</string>
               </property>
               <property name="alignment">
                <set>Qt::AlignCenter</set>
               </property>
               <property name="font">
                <font>
                 <pointsize>10</pointsize>
                 <weight>75</weight>
                 <bold>true</bold>
                </font>
               </property>
              </widget>
             </item>
             <item>
              <layout class="QHBoxLayout" name="toolbarLayout">
               <item>
                <widget class="QLabel" name="chartTypeLabel">
                 <property name="text">
                  <string>图表类型:</string>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QComboBox" name="chartTypeComboBox">
                 <item>
                  <property name="text">
                   <string>折线图</string>
                  </property>
                 </item>
                 <item>
                  <property name="text">
                   <string>散点图</string>
                  </property>
                 </item>
                 <item>
                  <property name="text">
                   <string>柱状图</string>
                  </property>
                 </item>
                 <item>
                  <property name="text">
                   <string>饼图</string>
                  </property>
                 </item>
                 <item>
                  <property name="text">
                   <string>热力图</string>
                  </property>
                 </item>
                 <item>
                  <property name="text">
                   <string>箱线图</string>
                  </property>
                 </item>
                 <item>
                  <property name="text">
                   <string>雷达图</string>
                  </property>
                 </item>
                </widget>
               </item>
               <item>
                <widget class="QPushButton" name="addChartButton">
                 <property name="text">
                  <string>新增图表</string>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QPushButton" name="removeChartButton">
                 <property name="text">
                  <string>删除图表</string>
                 </property>
                </widget>
               </item>
              </layout>
             </item>
             <item>
              <widget class="QTabBar" name="chartTabBar">
               <property name="tabsClosable">
                <bool>true</bool>
               </property>
               <property name="movable">
                <bool>true</bool>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QLabel" name="placeholderLabel">
               <property name="text">
                <string>请添加图表</string>
               </property>
              </widget>
             </item>
            </layout>
           </widget>
          </widget>
         </item>
        </layout>
       </widget>
      </widget>
      <widget class="QWidget" name="logWidget">
       <property name="sizePolicy">
        <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
         <horstretch>0</horstretch>
         <verstretch>1</verstretch>
        </sizepolicy>
       </property>
       <layout class="QVBoxLayout" name="logLayout">
        <property name="spacing">
         <number>4</number>
        </property>
        <property name="leftMargin">
         <number>0</number>
        </property>
        <property name="topMargin">
         <number>0</number>
        </property>
        <property name="rightMargin">
         <number>0</number>
        </property>
        <property name="bottomMargin">
         <number>0</number>
        </property>
        <item>
         <layout class="QHBoxLayout" name="logToolbarLayout">
          <item>
           <widget class="QLabel" name="logTitleLabel">
            <property name="text">
             <string>系统日志</string>
            </property>
            <property name="font">
             <font>
              <weight>75</weight>
              <bold>true</bold>
             </font>
            </property>
           </widget>
          </item>
          <item>
           <spacer name="horizontalSpacer">
            <property name="orientation">
             <enum>Qt::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
          <item>
           <widget class="QPushButton" name="saveLogButton">
            <property name="text">
             <string>保存日志</string>
            </property>
           </widget>
          </item>
         </layout>
        </item>
        <item>
         <widget class="QPlainTextEdit" name="logDisplay">
          <property name="minimumHeight">
           <number>100</number>
          </property>
         </widget>
        </item>
       </layout>
      </widget>
     </widget>
    </item>
   </layout>
  </widget>
  <widget class="QMenuBar" name="menubar">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>0</y>
     <width>1280</width>
     <height>26</height>
    </rect>
   </property>
   <widget class="QMenu" name="menuFile">
    <property name="title">
     <string>File</string>
    </property>
    <addaction name="actionNewConfig"/>
    <addaction name="actionOpenConfig"/>
    <addaction name="actionSaveConfig"/>
    <addaction name="separator"/>
    <addaction name="actionExportData"/>
    <addaction name="separator"/>
    <addaction name="actionExit"/>
   </widget>
   <widget class="QMenu" name="menuView">
    <property name="title">
     <string>View</string>
    </property>
    <addaction name="actionToggleToolbar"/>
    <addaction name="actionToggleStatusbar"/>
    <addaction name="separator"/>
    <addaction name="actionFullScreen"/>
   </widget>
   <widget class="QMenu" name="menuConnect">
    <property name="title">
     <string>Connect</string>
    </property>
    <addaction name="actionConnectionSettings"/>
    <addaction name="separator"/>
    <addaction name="actionConnect"/>
    <addaction name="actionDisconnect"/>
   </widget>
   <widget class="QMenu" name="menuTools">
    <property name="title">
     <string>Tools</string>
    </property>
    <addaction name="actionOptions"/>
   </widget>
   <widget class="QMenu" name="menuHelp">
    <property name="title">
     <string>Help</string>
    </property>
    <addaction name="actionAbout"/>
    <addaction name="actionDocumentation"/>
   </widget>
   <addaction name="menuFile"/>
   <addaction name="menuView"/>
   <addaction name="menuConnect"/>
   <addaction name="menuTools"/>
   <addaction name="menuHelp"/>
  </widget>
  <widget class="QStatusBar" name="statusbar"/>
  <widget class="QToolBar" name="toolBar">
   <property name="windowTitle">
    <string>toolBar</string>
   </property>
   <attribute name="toolBarArea">
    <enum>TopToolBarArea</enum>
   </attribute>
   <attribute name="toolBarBreak">
    <bool>false</bool>
   </attribute>
   <addaction name="actionConnect"/>
   <addaction name="actionDisconnect"/>
   <addaction name="separator"/>
   <addaction name="actionStart"/>
   <addaction name="actionStop"/>
   <addaction name="separator"/>
   <addaction name="actionOptions"/>
   <addaction name="actionExportData"/>
  </widget>
  <action name="actionNewConfig">
   <property name="text">
    <string>New Configuration</string>
   </property>
  </action>
  <action name="actionOpenConfig">
   <property name="text">
    <string>Open Configuration</string>
   </property>
  </action>
  <action name="actionSaveConfig">
   <property name="text">
    <string>Save Configuration</string>
   </property>
  </action>
  <action name="actionExportData">
   <property name="text">
    <string>Export Data</string>
   </property>
   <property name="toolTip">
    <string>Export current data</string>
   </property>
  </action>
  <action name="actionExit">
   <property name="text">
    <string>Exit</string>
   </property>
  </action>
  <action name="actionToggleToolbar">
   <property name="checkable">
    <bool>true</bool>
   </property>
   <property name="checked">
    <bool>true</bool>
   </property>
   <property name="text">
    <string>Show Toolbar</string>
   </property>
  </action>
  <action name="actionToggleStatusbar">
   <property name="checkable">
    <bool>true</bool>
   </property>
   <property name="checked">
    <bool>true</bool>
   </property>
   <property name="text">
    <string>Show Statusbar</string>
   </property>
  </action>
  <action name="actionFullScreen">
   <property name="checkable">
    <bool>true</bool>
   </property>
   <property name="text">
    <string>Full Screen</string>
   </property>
  </action>
  <action name="actionConnectionSettings">
   <property name="text">
    <string>Connection Settings</string>
   </property>
  </action>
  <action name="actionConnect">
   <property name="text">
    <string>Connect</string>
   </property>
   <property name="toolTip">
    <string>Establish connection</string>
   </property>
  </action>
  <action name="actionDisconnect">
   <property name="text">
    <string>Disconnect</string>
   </property>
   <property name="toolTip">
    <string>Close current connection</string>
   </property>
  </action>
  <action name="actionOptions">
   <property name="text">
    <string>Options</string>
   </property>
   <property name="toolTip">
    <string>Open application settings</string>
   </property>
  </action>
  <action name="actionAbout">
   <property name="text">
    <string>About</string>
   </property>
  </action>
  <action name="actionDocumentation">
   <property name="text">
    <string>Documentation</string>
   </property>
  </action>
  <action name="actionStart">
   <property name="text">
    <string>Start</string>
   </property>
   <property name="toolTip">
    <string>Start data acquisition</string>
   </property>
  </action>
  <action name="actionStop">
   <property name="text">
    <string>Stop</string>
   </property>
   <property name="toolTip">
    <string>Stop data acquisition</string>
   </property>
  </action>
 </widget>
 <resources/>
 <connections/>
</ui> 