#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
事件总线模块测试
"""

import os
import sys
import unittest
import asyncio
import threading
import time
from unittest.mock import patch, MagicMock, call

# 确保可以导入被测试模块
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..')))

# 直接导入全局实例和相关函数
from src.core.event_bus import EventBus, event_bus, on_event, AppEvents

class TestEventBus(unittest.TestCase):
    """事件总线测试类"""
    
    def setUp(self):
        """每个测试方法执行前的设置"""
        # 不重置单例，而是清空所有事件处理器
        event_bus.unregister_all()
    
    def tearDown(self):
        """每个测试方法执行后的清理"""
        # 确保清空所有事件处理器
        event_bus.unregister_all()
    
    def test_singleton(self):
        """测试EventBus类是否正确实现单例模式"""
        bus1 = EventBus()
        bus2 = EventBus()
        self.assertIs(bus1, bus2)
        
        # 验证全局实例也是同一个单例
        self.assertIs(event_bus, bus1)
    
    def test_register_and_publish(self):
        """测试注册事件处理器并发布事件"""
        # 创建模拟处理函数
        mock_handler = MagicMock()
        
        # 注册事件处理器
        event_name = "test.event"
        handler_id = event_bus.register(event_name, mock_handler)
        
        # 验证注册成功
        self.assertTrue(event_bus.has_subscribers(event_name))
        self.assertIn(event_name, event_bus.list_events())
        
        # 发布事件
        test_data = "test_data"
        event_bus.publish(event_name, test_data)
        
        # 验证处理函数被调用
        mock_handler.assert_called_once_with(test_data)
    
    def test_unregister(self):
        """测试注销事件处理器"""
        # 创建模拟处理函数
        mock_handler = MagicMock()
        
        # 注册事件处理器
        event_name = "test.event"
        handler_id = event_bus.register(event_name, mock_handler)
        
        # 验证注册成功
        self.assertTrue(event_bus.has_subscribers(event_name))
        
        # 注销事件处理器
        result = event_bus.unregister(event_name, handler_id)
        
        # 验证注销成功
        self.assertTrue(result)
        self.assertFalse(event_bus.has_subscribers(event_name))
        self.assertNotIn(event_name, event_bus.list_events())
        
        # 发布事件，验证处理函数不再被调用
        event_bus.publish(event_name, "test_data")
        mock_handler.assert_not_called()
    
    def test_unregister_all(self):
        """测试注销所有事件处理器"""
        # 创建多个模拟处理函数
        mock_handler1 = MagicMock()
        mock_handler2 = MagicMock()
        
        # 注册多个事件处理器
        event_name1 = "test.event1"
        event_name2 = "test.event2"
        event_bus.register(event_name1, mock_handler1)
        event_bus.register(event_name2, mock_handler2)
        
        # 验证注册成功
        self.assertTrue(event_bus.has_subscribers(event_name1))
        self.assertTrue(event_bus.has_subscribers(event_name2))
        
        # 注销所有事件处理器
        event_bus.unregister_all()
        
        # 验证所有事件都已注销
        self.assertFalse(event_bus.has_subscribers(event_name1))
        self.assertFalse(event_bus.has_subscribers(event_name2))
        self.assertEqual(event_bus.list_events(), [])
        
        # 发布事件，验证处理函数不再被调用
        event_bus.publish(event_name1, "test_data")
        event_bus.publish(event_name2, "test_data")
        mock_handler1.assert_not_called()
        mock_handler2.assert_not_called()
    
    def test_multiple_handlers(self):
        """测试一个事件有多个处理器"""
        # 创建多个模拟处理函数
        mock_handler1 = MagicMock()
        mock_handler2 = MagicMock()
        
        # 注册多个事件处理器到同一事件
        event_name = "test.event"
        event_bus.register(event_name, mock_handler1)
        event_bus.register(event_name, mock_handler2)
        
        # 发布事件
        test_data = "test_data"
        event_bus.publish(event_name, test_data)
        
        # 验证所有处理函数都被调用
        mock_handler1.assert_called_once_with(test_data)
        mock_handler2.assert_called_once_with(test_data)
    
    def test_decorator(self):
        """测试事件订阅装饰器"""
        # 使用装饰器创建事件处理函数
        test_result = []
        event_name = "test.event"
        
        @on_event(event_name)
        def handler(data):
            test_result.append(data)
        
        # 验证注册成功
        self.assertTrue(event_bus.has_subscribers(event_name))
        
        # 发布事件
        test_data = "test_data"
        event_bus.publish(event_name, test_data)
        
        # 验证处理函数被调用
        self.assertIn(test_data, test_result)
    
    def test_async_handler(self):
        """测试异步事件处理器"""
        # 创建异步处理函数
        test_result = []
        event_name = "test.async.event"
        
        async def async_handler(data):
            await asyncio.sleep(0.1)  # 模拟异步操作
            test_result.append(data)
        
        # 注册异步事件处理器
        event_bus.register(event_name, async_handler, is_async=True)
        
        # 发布事件
        test_data = "async_test_data"
        event_bus.publish(event_name, test_data)
        
        # 运行事件循环以处理异步任务
        loop = asyncio.get_event_loop()
        loop.run_until_complete(asyncio.sleep(0.2))
        
        # 验证处理函数被调用
        self.assertIn(test_data, test_result)
    
    def test_publish_async(self):
        """测试异步发布事件"""
        # 创建处理函数
        mock_handler = MagicMock()
        
        # 注册事件处理器
        event_name = "test.event"
        event_bus.register(event_name, mock_handler)
        
        # 异步发布事件
        test_data = "test_data"
        
        async def async_test():
            await event_bus.publish_async(event_name, test_data)
        
        # 运行异步测试
        asyncio.run(async_test())
        
        # 验证处理函数被调用
        mock_handler.assert_called_once_with(test_data)
    
    def test_error_handling(self):
        """测试事件处理器中的错误处理"""
        # 创建会抛出异常的处理函数
        def error_handler(data):
            raise ValueError("Test error")
        
        # 注册事件处理器
        event_name = "test.error.event"
        event_bus.register(event_name, error_handler)
        
        # 发布事件（不应该抛出异常）
        event_bus.publish(event_name, "test_data")
        
        # 如果没有异常抛出到测试中，认为测试通过

if __name__ == "__main__":
    unittest.main() 