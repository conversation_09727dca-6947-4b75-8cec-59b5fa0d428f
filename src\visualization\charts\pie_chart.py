#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
饼图模块

实现基于PyQtGraph的饼图，支持扇区高亮、标签显示等功能。
"""

import numpy as np
import math
from typing import Dict, List, Optional, Tuple, Union, Any

try:
    import pyqtgraph as pg
    from pyqtgraph.Qt import QtCore, QtGui, QtWidgets
except ImportError:
    # 如果导入失败，可能会在运行时另行处理
    pass

from ..base_chart import BaseChart, ChartType
from ...utils.logger import logger


class PieSlice(pg.GraphicsObject):
    """饼图扇区项目"""
    
    def __init__(
        self, 
        start_angle: float, 
        span_angle: float, 
        radius: float, 
        color: Tuple[int, int, int], 
        label: str = "",
        value: float = 0,
        percentage: float = 0,
        offset: float = 0,
        pen=None
    ):
        """
        初始化饼图扇区
        
        Args:
            start_angle: 起始角度（度）
            span_angle: 扇区跨度（度）
            radius: 扇区半径
            color: 扇区颜色
            label: 扇区标签
            value: 扇区数值
            percentage: 扇区百分比
            offset: 扇区偏移（用于突出显示）
            pen: 扇区边框画笔
        """
        super().__init__()
        
        self.start_angle = start_angle
        self.span_angle = span_angle
        self.radius = radius
        self.color = color
        self.label = label
        self.value = value
        self.percentage = percentage
        self.offset = offset
        self.pen = pen or pg.mkPen(color=(0, 0, 0, 100), width=1)
        
        self.highlighted = False
        self.setAcceptHoverEvents(True)
        
        # 创建扇区路径
        self.path = self._create_path()
    
    def _create_path(self):
        """创建扇区路径"""
        path = QtGui.QPainterPath()

        # 计算偏移后的中心点
        if self.offset > 0:
            # 使用扇区中点角度计算偏移方向 (使用内部CW+角度)
            mid_angle_cw = self.start_angle + self.span_angle / 2
            mid_angle_rad_cw = math.radians(mid_angle_cw)
            # 使用标准数学函数计算xy偏移 (cos @ 0=right, sin @ 90=up)
            # No angle conversion needed here as math functions follow standard convention
            offset_x = self.offset * math.cos(mid_angle_rad_cw)
            offset_y = self.offset * math.sin(mid_angle_rad_cw)
            center = QtCore.QPointF(offset_x, offset_y)
        else:
            center = QtCore.QPointF(0, 0)

        # 定义绘制弧线的矩形
        rect = QtCore.QRectF(
            center.x() - self.radius,
            center.y() - self.radius,
            self.radius * 2,
            self.radius * 2
        )

        # 转换角度到Qt系统 (degrees, 0 at 3 o'clock, CCW positive)
        # Our internal angles are 0 at 3 o'clock, CW positive.
        qt_start_angle = self.start_angle # Start angle reference point is the same
        qt_span_angle = -self.span_angle  # Span direction needs to be reversed for CCW

        # 创建路径: 移动到中心点, 使用arcTo绘制扇形弧线, 关闭路径连接回中心点
        path.moveTo(center)
        path.arcTo(rect, qt_start_angle, qt_span_angle)
        path.closeSubpath()

        return path
    
    def paint(self, painter, option, widget=None):
        """绘制扇区"""
        painter.setPen(self.pen)
        
        # 如果高亮，使用稍亮的颜色
        if self.highlighted:
            r, g, b = self.color
            brush = QtGui.QBrush(QtGui.QColor(
                min(r + 30, 255), 
                min(g + 30, 255), 
                min(b + 30, 255)
            ))
        else:
            brush = QtGui.QBrush(QtGui.QColor(*self.color))
        
        painter.setBrush(brush)
        painter.drawPath(self.path)
    
    def boundingRect(self):
        """获取包围盒"""
        return self.path.boundingRect()
    
    def contains(self, point):
        """检查点是否在扇区内"""
        return self.path.contains(point)
    
    def hoverEnterEvent(self, event):
        """鼠标进入事件"""
        self.highlighted = True
        self.update()
        event.accept()
    
    def hoverLeaveEvent(self, event):
        """鼠标离开事件"""
        self.highlighted = False
        self.update()
        event.accept()
    
    def setOffset(self, offset):
        """设置扇区偏移"""
        if self.offset != offset:
            self.offset = offset
            self.path = self._create_path()
            self.update()


class PieChart(BaseChart):
    """
    饼图类
    
    实现饼图的创建、数据管理和样式设置等功能。
    """
    
    def __init__(
        self, 
        title: str = "饼图", 
        x_label: str = "", 
        y_label: str = ""
    ):
        """
        初始化饼图
        
        Args:
            title: 图表标题
            x_label: X轴标签（饼图通常不显示坐标轴）
            y_label: Y轴标签（饼图通常不显示坐标轴）
        """
        super().__init__(title, x_label, y_label, ChartType.PIE)
        
        # 饼图特有的属性
        self._slices = {}  # 存储扇区对象
        self._radius = 0.8  # 饼图半径（相对于绘图区域）
        self._start_angle = 0  # 开始角度（度）
        self._show_labels = True  # 是否显示标签
        self._label_items = {}  # 存储标签文本项
        self._selected_slice = None  # 当前选中的扇区
        self._donut_hole = 0  # 甜甜圈图中心空洞的半径比例
        
        # 预设颜色列表
        self._colors = [
            (255, 0, 0),      # 红色
            (0, 0, 255),      # 蓝色
            (0, 255, 0),      # 绿色
            (255, 165, 0),    # 橙色
            (128, 0, 128),    # 紫色
            (255, 192, 203),  # 粉红色
            (165, 42, 42),    # 棕色
            (0, 255, 255),    # 青色
            (255, 215, 0),    # 金色
            (0, 128, 128),    # 蓝绿色
            (255, 0, 255),    # 品红色
            (128, 128, 0),    # 橄榄色
            (0, 0, 128),      # 深蓝色
        ]
    
    def _initialize_specific(self):
        """实现图表特定的初始化"""
        # 隐藏坐标轴
        self._plot_item.hideAxis('left')
        self._plot_item.hideAxis('bottom')
        
        # 设置等轴比例以获得正圆形
        self._plot_item.setAspectLocked(True)
        
        # 设置合适的视图范围
        self._plot_item.setRange(xRange=(-1, 1), yRange=(-1, 1), padding=0.1)
    
    def add_data(
        self, 
        data_id: str, 
        data: Union[Dict[str, float], List[Tuple[str, float]]], 
        **kwargs
    ):
        """
        添加数据到饼图
        
        Args:
            data_id: 数据标识符（饼图通常只有一个数据系列）
            data: 数据内容，可以是字典 {'类别1': 值1, ...} 或列表 [('类别1', 值1), ...]
            **kwargs: 额外配置参数，包括：
                - colors: 颜色列表或字典，用于不同类别
                - start_angle: 起始角度（度）
                - radius: 饼图半径（相对于绘图区域）
                - show_labels: 是否显示标签
                - label_format: 标签格式化字符串，如 '{label}: {value:.1f}'
                - explode: 突出显示的扇区索引或类别名称
                - donut_hole: 中心空洞的半径比例（0表示普通饼图）
        """
        if not self._initialized:
            self.initialize()
        
        try:
            # 清除现有数据
            self.clear_data()
            
            # 解析数据
            labels = []
            values = []
            
            if isinstance(data, dict):
                for label, value in data.items():
                    labels.append(label)
                    values.append(value)
            elif isinstance(data, list):
                for item in data:
                    if isinstance(item, tuple) and len(item) == 2:
                        label, value = item
                        labels.append(label)
                        values.append(value)
                    else:
                        raise ValueError(f"列表项必须是 (label, value) 元组，但获得了 {item}")
            else:
                raise ValueError(f"不支持的数据格式: {type(data)}")
            
            # 确保所有值都为正
            if any(v <= 0 for v in values):
                logger.warning(f"{self._log_prefix} 饼图数据包含零或负值，这些值将被忽略")
                filtered_data = [(l, v) for l, v in zip(labels, values) if v > 0]
                labels = [item[0] for item in filtered_data]
                values = [item[1] for item in filtered_data]
            
            if not values:
                logger.warning(f"{self._log_prefix} 没有有效的正值数据")
                return
            
            # 解析参数
            colors = kwargs.get('colors', None)
            start_angle = kwargs.get('start_angle', self._start_angle)
            radius = kwargs.get('radius', self._radius)
            show_labels = kwargs.get('show_labels', self._show_labels)
            label_format = kwargs.get('label_format', '{label}: {value:.1f} ({percentage:.1f}%)')
            explode = kwargs.get('explode', None)
            donut_hole = kwargs.get('donut_hole', self._donut_hole)
            
            # 更新设置
            self._start_angle = start_angle
            self._radius = radius
            self._show_labels = show_labels
            self._donut_hole = donut_hole
            
            # 计算总和和百分比
            total = sum(values)
            percentages = [v / total * 100 for v in values]
            
            # 计算角度
            angles = [v / total * 360 for v in values]
            
            # 创建扇区
            current_angle = start_angle
            for i, (label, value, percentage, angle) in enumerate(zip(labels, values, percentages, angles)):
                # 确定颜色
                if colors is not None:
                    if isinstance(colors, dict) and label in colors:
                        color = colors[label]
                    elif isinstance(colors, list) and i < len(colors):
                        color = colors[i]
                    else:
                        color = self._colors[i % len(self._colors)]
                else:
                    color = self._colors[i % len(self._colors)]
                
                # 确定是否突出显示
                offset = 0
                if explode is not None:
                    if isinstance(explode, int) and i == explode:
                        offset = radius * 0.1
                    elif isinstance(explode, str) and label == explode:
                        offset = radius * 0.1
                    elif isinstance(explode, (list, tuple)) and (i in explode or label in explode):
                        offset = radius * 0.1
                
                # 创建扇区
                slice_id = f"{data_id}_{i}"
                slice_item = PieSlice(
                    start_angle=current_angle,
                    span_angle=angle,
                    radius=radius,
                    color=color,
                    label=label,
                    value=value,
                    percentage=percentage,
                    offset=offset
                )
                
                # 添加到绘图项
                self._plot_item.addItem(slice_item)
                
                # 存储扇区
                self._slices[slice_id] = slice_item
                self._data_items[slice_id] = slice_item
                
                # 添加标签
                if show_labels:
                    self._add_label(
                        slice_id, 
                        label, 
                        value, 
                        percentage, 
                        current_angle + angle / 2, 
                        radius, 
                        label_format
                    )
                
                # 更新当前角度
                current_angle += angle
            
            # 添加中心空洞（如果需要）
            if donut_hole > 0:
                self._create_donut_hole(radius * donut_hole)
            
            # 将数据项关联到数据ID
            self._data_items[data_id] = list(self._slices.values())
            
            logger.debug(f"{self._log_prefix} 添加饼图数据: {data_id}, 扇区数: {len(labels)}")
            
        except Exception as e:
            logger.error(f"{self._log_prefix} 添加数据失败: {str(e)}")
            raise
    
    def _add_label(
        self, 
        slice_id: str, 
        label: str, 
        value: float, 
        percentage: float, 
        angle: float, 
        radius: float, 
        label_format: str
    ):
        """
        添加扇区标签
        
        Args:
            slice_id: 扇区ID
            label: 标签文本
            value: 扇区值
            percentage: 百分比
            angle: 扇区中心角度（度）
            radius: 饼图半径
            label_format: 标签格式化字符串
        """
        # 转换为弧度
        angle_rad = math.radians(angle)
        
        # 计算标签位置（稍微超出扇区边缘）
        label_radius = radius * 1.1
        x = label_radius * math.cos(angle_rad)
        y = label_radius * math.sin(angle_rad)
        
        # 根据角度确定对齐方式
        if -90 <= angle <= 90:
            # 右侧标签，左对齐
            anchor = (0, 0.5)
        else:
            # 左侧标签，右对齐
            anchor = (1, 0.5)
        
        # 格式化标签文本
        text = label_format.format(
            label=label,
            value=value,
            percentage=percentage
        )
        
        # 创建文本项
        text_item = pg.TextItem(
            text=text,
            color=(0, 0, 0),
            anchor=anchor
        )
        
        # 设置位置
        text_item.setPos(x, y)
        
        # 添加到绘图项
        self._plot_item.addItem(text_item)
        
        # 存储标签项
        self._label_items[slice_id] = text_item
    
    def _create_donut_hole(self, hole_radius: float):
        """
        创建甜甜圈中心空洞
        
        Args:
            hole_radius: 中心空洞半径
        """
        # 创建一个圆形路径
        hole = QtWidgets.QGraphicsEllipseItem(-hole_radius, -hole_radius, hole_radius * 2, hole_radius * 2)
        
        # 设置白色填充
        hole.setBrush(QtGui.QBrush(QtGui.QColor(255, 255, 255)))
        
        # 无边框
        hole.setPen(pg.mkPen(None))
        
        # 添加到绘图项
        self._plot_item.addItem(hole)
    
    def update_data(
        self, 
        data_id: str, 
        data: Union[Dict[str, float], List[Tuple[str, float]]], 
        **kwargs
    ):
        """
        更新饼图数据
        
        Args:
            data_id: 数据标识符
            data: 新的数据内容
            **kwargs: 额外配置参数，与add_data相同
        """
        # 饼图更新直接重新生成所有扇区
        self.add_data(data_id, data, **kwargs)
    
    def set_explode(self, slice_index: Union[int, str, List], explode_offset: float = 0.1):
        """
        设置突出显示的扇区
        
        Args:
            slice_index: 扇区索引、标签或索引/标签列表
            explode_offset: 偏移量（相对于半径）
        """
        if not self._initialized:
            return
        
        # 将所有扇区恢复到原位
        for slice_item in self._slices.values():
            slice_item.setOffset(0)
        
        # 如果没有提供扇区索引，则取消所有突出显示
        if slice_index is None:
            return
        
        # 对指定扇区设置偏移
        for slice_id, slice_item in self._slices.items():
            index = int(slice_id.split('_')[-1])
            
            # 检查当前扇区是否需要突出显示
            should_explode = False
            if isinstance(slice_index, int) and index == slice_index:
                should_explode = True
            elif isinstance(slice_index, str) and slice_item.label == slice_index:
                should_explode = True
            elif isinstance(slice_index, (list, tuple)) and (index in slice_index or slice_item.label in slice_index):
                should_explode = True
            
            if should_explode:
                offset = self._radius * explode_offset
                slice_item.setOffset(offset)
    
    def set_donut(self, hole_radius_ratio: float = 0.5):
        """
        将饼图设置为甜甜圈图

        Args:
            hole_radius_ratio: 中心空洞半径（相对于饼图半径）
        """
        if not self._initialized:
            return
        
        # 更新设置
        self._donut_hole = max(0, min(0.9, hole_radius_ratio))
        
        # 重新生成饼图
        if any(self._data_items):
            data_id = next(iter(self._data_items.keys()))
            self.update_data(data_id, self._get_current_data(), donut_hole=self._donut_hole)
            
    def set_hole_size(self, hole_radius_ratio: float = 0.5):
        """
        将饼图设置为甜甜圈图（中间有空洞）
        
        这是set_donut方法的别名，提供兼容性
        
        Args:
            hole_radius_ratio (float): 空洞半径与饼图半径的比例，0表示没有空洞（普通饼图）
        """
        return self.set_donut(hole_radius_ratio)
    
    def _get_current_data(self):
        """获取当前数据，用于重新生成饼图"""
        data = {}
        for slice_id, slice_item in self._slices.items():
            if hasattr(slice_item, 'label') and hasattr(slice_item, 'value'):
                data[slice_item.label] = slice_item.value
        return data
    
    def set_show_labels(self, show: bool):
        """
        设置是否显示标签
        
        Args:
            show: 是否显示
        """
        if self._show_labels == show:
            return
        
        self._show_labels = show
        
        # 更新所有标签的可见性
        for label_item in self._label_items.values():
            label_item.setVisible(show)
    
    def set_start_angle(self, angle: float):
        """
        设置起始角度
        
        Args:
            angle: 起始角度（度）
        """
        if self._start_angle == angle:
            return
        
        self._start_angle = angle
        
        # 重新生成饼图
        if any(self._data_items):
            data_id = next(iter(self._data_items.keys()))
            self.update_data(data_id, self._get_current_data(), start_angle=angle)
    
    def clear_data(self, data_id: Optional[str] = None):
        """
        清除图表数据
        
        Args:
            data_id: 要清除的数据标识符，如果为None则清除所有数据
        """
        # 调用父类方法清除绘图项
        super().clear_data(data_id)
        
        # 清除标签项
        for label_id, label_item in list(self._label_items.items()):
            if data_id is None or label_id.startswith(data_id):
                self._plot_item.removeItem(label_item)
                del self._label_items[label_id]
        
        # 清除扇区项
        for slice_id in list(self._slices.keys()):
            if data_id is None or slice_id.startswith(data_id):
                del self._slices[slice_id]
        
        # 重置选中扇区
        self._selected_slice = None
