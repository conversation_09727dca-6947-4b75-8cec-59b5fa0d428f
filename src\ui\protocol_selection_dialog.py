#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
协议选择对话框

提供界面用于选择配置好的硬件协议进行连接。
"""

import logging
from typing import Dict, Optional

try:
    from PyQt5.QtWidgets import (
        QDialog, QVBoxLayout, QHBoxLayout, QListWidget, QListWidgetItem,
        QLabel, QPushButton, QDialogButtonBox, QFrame, QSplitter,
        QTextBrowser, QMessageBox
    )
    from PyQt5.QtCore import Qt, pyqtSlot as Slot
except ImportError:
    try:
        from PySide6.QtWidgets import (
            QDialog, QVBoxLayout, QHBoxLayout, QListWidget, QListWidgetItem,
            QLabel, QPushButton, QDialogButtonBox, QFrame, QSplitter,
            QTextBrowser, QMessageBox
        )
        from PySide6.QtCore import Qt, Slot
    except ImportError:
        raise ImportError("未安装PyQt5或PySide6，请安装其中之一")

from src.core.config import Config

# 获取logger
logger = logging.getLogger(__name__)

class ProtocolSelectionDialog(QDialog):
    """
    协议选择对话框
    
    提供界面用于选择配置好的硬件协议进行连接。
    """
    
    def __init__(self, parent=None):
        """初始化对话框"""
        super().__init__(parent)
        
        # 设置窗口属性
        self.setWindowTitle("选择连接协议")
        self.setMinimumSize(600, 400)
        
        # 选中的协议
        self.selected_protocol = None
        self.selected_config = None
        
        # 初始化UI
        self._init_ui()
        
        # 加载协议配置
        self._load_protocols()
    
    def _init_ui(self) -> None:
        """初始化UI"""
        # 创建主布局
        main_layout = QVBoxLayout()
        self.setLayout(main_layout)
        
        # 说明标签
        info_label = QLabel("请选择要连接的协议:")
        main_layout.addWidget(info_label)
        
        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)
        
        # 协议列表
        self.protocol_list = QListWidget()
        self.protocol_list.setMinimumWidth(150)
        
        # 协议详情
        self.detail_frame = QFrame()
        detail_layout = QVBoxLayout()
        self.detail_frame.setLayout(detail_layout)
        
        # 协议详情标题
        self.detail_title = QLabel("请选择一个协议")
        self.detail_title.setStyleSheet("font-weight: bold; font-size: 14px;")
        detail_layout.addWidget(self.detail_title)
        
        # 协议配置详情
        self.detail_browser = QTextBrowser()
        detail_layout.addWidget(self.detail_browser)
        
        # 添加到分割器
        splitter.addWidget(self.protocol_list)
        splitter.addWidget(self.detail_frame)
        
        # 设置分割器初始比例
        splitter.setSizes([200, 400])
        
        # 添加到主布局
        main_layout.addWidget(splitter)
        
        # 按钮
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        main_layout.addWidget(button_box)
        
        # 连接信号
        self.protocol_list.currentItemChanged.connect(self._on_protocol_selected)
    
    def _load_protocols(self) -> None:
        """加载协议配置"""
        try:
            # 获取配置
            config = Config.get_instance()
            protocols = config.get("hardware.protocols", {})
            
            if not protocols:
                self.protocol_list.addItem("没有可用的协议配置")
                logger.warning("没有找到可用的协议配置")
                return
            
            # 添加到列表
            for protocol_name in protocols.keys():
                item = QListWidgetItem(protocol_name)
                item.setData(Qt.UserRole, protocol_name)
                self.protocol_list.addItem(item)
            
            logger.debug(f"已加载 {len(protocols)} 个协议配置")
            
        except Exception as e:
            logger.error(f"加载协议配置失败: {str(e)}")
            self.protocol_list.addItem("加载协议配置失败")
    
    def _on_protocol_selected(self, current: QListWidgetItem, previous: QListWidgetItem) -> None:
        """
        协议选择事件处理函数
        
        Args:
            current: 当前选中的项
            previous: 之前选中的项
        """
        if current is None:
            self.detail_title.setText("请选择一个协议")
            self.detail_browser.clear()
            self.selected_protocol = None
            self.selected_config = None
            return
        
        # 获取协议名称
        protocol_name = current.data(Qt.UserRole)
        if protocol_name in ("没有可用的协议配置", "加载协议配置失败"):
            self.detail_title.setText("错误")
            self.detail_browser.setPlainText("无法加载协议配置，请先在选项中配置协议。")
            self.selected_protocol = None
            self.selected_config = None
            return
        
        # 获取协议配置
        config = Config.get_instance()
        protocol_config = config.get(f"hardware.protocols.{protocol_name}", {})
        
        # 更新详情
        self.detail_title.setText(f"协议: {protocol_name}")
        self.selected_protocol = protocol_name
        self.selected_config = protocol_config
        
        # 显示配置详情
        detail_text = self._format_config_details(protocol_name, protocol_config)
        self.detail_browser.setPlainText(detail_text)
    
    def _format_config_details(self, protocol_name: str, config: Dict) -> str:
        """
        格式化配置详情
        
        Args:
            protocol_name: 协议名称
            config: 协议配置
            
        Returns:
            格式化后的配置详情文本
        """
        if not config:
            return f"没有找到 {protocol_name} 的配置信息"
        
        # 协议特定的格式化
        if protocol_name == "串口协议":
            port = config.get("port", "未指定")
            baudrate = config.get("baudrate", "9600")
            bytesize = config.get("bytesize", "8")
            parity = config.get("parity", "N")
            stopbits = config.get("stopbits", "1")
            
            # 将校验位代码转换为文本
            parity_text = {
                "N": "无校验",
                "E": "偶校验",
                "O": "奇校验",
                "M": "标记校验",
                "S": "空格校验"
            }.get(parity, parity)
            
            return (
                f"串口: {port}\n"
                f"波特率: {baudrate}\n"
                f"数据位: {bytesize}\n"
                f"校验位: {parity_text}\n"
                f"停止位: {stopbits}\n"
            )
        
        # 默认格式化
        lines = []
        for key, value in config.items():
            if isinstance(value, str) and value.startswith("__bytes__"):
                continue  # 跳过二进制数据
            lines.append(f"{key}: {value}")
        
        return "\n".join(lines)
    
    def get_selected_protocol(self) -> Optional[str]:
        """
        获取选中的协议名称
        
        Returns:
            选中的协议名称，如果未选择则返回None
        """
        return self.selected_protocol
    
    def get_selected_config(self) -> Optional[Dict]:
        """
        获取选中的协议配置
        
        Returns:
            选中的协议配置，如果未选择则返回None
        """
        return self.selected_config
    
    def accept(self) -> None:
        """确认选择"""
        if not self.selected_protocol:
            QMessageBox.warning(
                self,
                "未选择协议",
                "请选择一个要连接的协议"
            )
            return
        
        super().accept() 