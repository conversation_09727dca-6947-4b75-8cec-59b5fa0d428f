# 角色定义

你是一位数据可视化软件开发专家，专注于使用 Python（Qt）开发高效、用户友好的数据可视化工具。

# 背景分析

用户需要一款能够处理和可视化测量采集数据的软件，支持多种数据类型和常用硬件接口协议的解析，以满足不同应用场景的需求。

# 目标设定

1. 提供多种类型的数据可视化，包括但不限于：

   - 折线图
   - 散点图
   - 时间序列图
   - 柱状图
   - 热力图
   - 饼图
   - 箱线图
   - 雷达图
2. 支持解析常用的硬件接口协议，包括但不限于：

   - 自由串口
   - Modbus
   - TCP/IP
   - CAN总线
   - MQTT
   - OPC UA
   - I2C
   - SPI

# 工作流程

1. 分析用户需求，明确数据类型和协议的具体要求。
2. 设计软件架构，确保可扩展性和模块化。
3. 实现数据可视化功能，使用合适的图形库（如 Matplotlib、PyQtGraph）。
4. 实现协议解析模块，确保与硬件的兼容性。
5. 进行测试和优化，确保软件的稳定性和性能。

# 技能

- 精通 Python 和 Qt 框架
- 熟悉数据可视化库（如 Matplotlib、Seaborn、Plotly）
- 了解常用硬件接口协议的工作原理
- 具备良好的软件设计和架构能力

# 硬件测试软件

### 1. **串口通信模拟**

- **Virtual Serial Port Driver (VSPD)**用于创建虚拟串口对，模拟串口通信。
- **COM0COM**
  开源的虚拟串口工具，适合测试串口协议。

### 2. **Modbus协议模拟**

- **Modbus Slave (Modbus从站模拟器)**支持Modbus RTU/TCP协议的从站模拟，适合测试Modbus主站功能。
- **QModMaster**
  开源的Modbus主站工具，也可用于测试Modbus通信。

### 3. **TCP/IP通信模拟**

- **Packet Sender**轻量级工具，支持TCP/UDP数据包的发送和接收。
- **Wireshark**
  网络协议分析工具，可用于抓包和调试TCP/IP通信。

### 4. **CAN总线模拟**

- **CANoe (Vector)**专业的CAN总线开发和测试工具，支持仿真和数据分析。
- **PCAN-View**
  免费的CAN总线监控工具，适合简单的CAN通信测试。

### 5. **MQTT协议模拟**

- **MQTT.fx**支持MQTT协议的客户端工具，可用于模拟发布和订阅消息。
- **Mosquitto**
  开源的MQTT代理，支持本地搭建MQTT服务器进行测试。

### 6. **OPC UA模拟**

- **Prosys OPC UA Simulation Server**提供OPC UA服务器的模拟功能，支持数据点生成和通信测试。
- **UA Expert**
  OPC UA客户端工具，可用于测试OPC UA服务器的数据交互。

### 7. **I2C/SPI模拟**

- **Bus Pirate**开源硬件工具，支持I2C、SPI等多种总线的调试和模拟。
- **Saleae Logic Analyzer**
  逻辑分析仪软件，配合硬件可捕获和分析I2C/SPI信号。

### 8. **综合测试工具**

- **LabVIEW**支持多种协议的仿真和测试，适合复杂场景。
- **Python脚本**
  使用 `pyserial`、`python-can`等库编写自定义模拟脚本。

# 项目架构设计

### **1. 核心框架层**

- **语言与框架**：
  - Python 3.8+
  - PyQt5/PySide6（UI框架）
  - Qt Designer（界面设计工具）
- **职责**：
  - 提供基础运行环境。
  - 管理主窗口、事件循环和跨平台兼容性。

### **2. 用户界面层（UI Layer）**

- **组件**：
  - **主窗口**：集成图表、控件和菜单。
  - **动态面板**：支持多图表布局和交互。
  - **协议配置面板**：硬件接口参数设置。
- **技术**：
  - Qt Designer 生成的 `.ui` 文件。
  - 动态加载和自定义控件。

### **3. 数据可视化层**

- **核心库**：
  - PyQtGraph（实时数据渲染）。
  - Matplotlib（复杂图表导出）。
  - QtCharts（基础图表）。
- **功能模块**：
  - **图表管理器**：统一管理图表类型（折线图、散点图等）。
  - **数据渲染引擎**：优化大数据量绘制性能。

### **4. 硬件协议解析层**

- **协议支持**：
  - **Modbus**：`pymodbus` 库。
  - **串口**：`pyserial`。
  - **CAN总线**：`python-can`。
  - **MQTT**：`paho-mqtt`。
- **模块设计**：
  - **协议适配器**：统一接口（如 `read_data()`、`send_command()`）。
  - **多线程通信**：避免阻塞UI线程。

### **5. 数据处理层**

- **库支持**：
  - NumPy（实时数据计算）。
  - Pandas（历史数据分析）。
- **功能**：
  - **数据缓存**：环形缓冲区管理实时数据。
  - **数据清洗**：滤波和异常值处理。

### **6. 辅助工具层**

- **功能**：
  - **图表导出**：支持 PNG/CSV 格式（`pyqtgraph.exporters`）。
  - **多媒体支持**：音频/视频反馈（`QtMultimedia`）。
  - **日志系统**：记录协议通信和用户操作。

### **7. 测试与模拟层**

- **工具集成**：
  - **硬件模拟**：VSPD（串口）、Modbus Slave（协议测试）。
  - **性能测试**：Python 脚本生成模拟数据。

---

### **架构图**

```plaintext
┌─────────────────────────────────────────────────┐
│                  用户界面层 (UI)                 │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────┐  │
│  │  主窗口     │  │  图表面板   │  │ 配置面板│  │
│  └─────────────┘  └─────────────┘  └─────────┘  │
└───────────────┬──────────────────────┬──────────┘
                │                      │
┌───────────────▼──────┐  ┌────────────▼───────┐
│    数据可视化层      │  │  硬件协议解析层    │
│  ┌────────────────┐  │  │  ┌───────────────┐ │
│  │  图表管理器    │  │  │  │  Modbus适配器 │ │
│  └────────────────┘  │  │  └───────────────┘ │
│  ┌────────────────┐  │  │  ┌───────────────┐ │
│  │ 数据渲染引擎   │  │  │  │  串口适配器   │ │
│  └────────────────┘  │  │  └───────────────┘ │
└───────────────┬──────┘  └──────────┬────────┘
                │                     │
┌───────────────▼────────────────────▼───────┐
│              数据处理层                    │
│  ┌────────────────┐  ┌──────────────────┐  │
│  │  实时数据缓存  │  │  历史数据分析    │  │
│  └────────────────┘  └──────────────────┘  │
└───────────────┬────────────────────┬───────┘
                │                    │
┌───────────────▼──────┐  ┌──────────▼───────┐
│    辅助工具层        │  │  测试与模拟层    │
│  ┌────────────────┐  │  │  ┌─────────────┐ │
│  │  图表导出      │  │  │  │  硬件模拟   │ │
│  └────────────────┘  │  │  └─────────────┘ │
│  ┌────────────────┐  │  │  ┌─────────────┐ │
│  │  日志系统      │  │  │  │  性能测试   │ │
│  └────────────────┘  │  │  └─────────────┘ │
└──────────────────────┘  └──────────────────┘
```

---

### **关键设计说明**

1. **模块化**：每层职责明确，支持独立开发和测试。
2. **多线程**：协议解析和数据处理使用独立线程，避免UI卡顿。
3. **扩展性**：新增协议或图表类型时，只需扩展对应模块。
4. **测试覆盖**：结合硬件模拟工具和单元测试（如 `pytest`）。

# 项目目录架构

## 基础目录结构

```plaintext
data-show/
├── .venv/                     # UV虚拟环境目录
├── .uv/                       # UV配置目录
│   ├── config.toml            # UV工具配置文件
│   └── lock.json              # 依赖锁文件
├── docs/                      # 项目文档
│   ├── requirements.md        # 依赖说明
│   └── architecture.md        # 架构设计文档
├── config/                    # 【新增】配置文件目录
│   ├── default_settings.json  # 【新增】默认配置
│   └── user_settings.json     # 【新增】用户配置
├── src/                       # 主代码目录
│   ├── core/                  # 核心框架层
│   │   ├── app.py             # 主程序入口
│   │   ├── config.py          # 全局配置
│   │   ├── thread_manager.py  # 【新增】线程管理
│   │   └── event_bus.py       # 【新增】事件总线（线程间通信）
│   ├── ui/                    # 用户界面层
│   ├── visualization/         # 数据可视化层
│   │   ├── chart_manager.py   # 总体管理
│   │   ├── charts/            # 【新增】图表类型目录
│   │   │   ├── line_chart.py  # 【新增】折线图
│   │   │   ├── scatter.py     # 【新增】散点图
│   │   │   ├── time_series.py # 【新增】时间序列图
│   │   │   ├── histogram.py   # 【新增】柱状图
│   │   │   ├── heatmap.py     # 【新增】热力图
│   │   │   ├── pie_chart.py   # 【新增】饼图
│   │   │   ├── box_plot.py    # 【新增】箱线图
│   │   │   └── radar.py       # 【新增】雷达图
│   ├── protocol/              # 硬件协议解析层
│   │   ├── adapters/
│   │   │   ├── modbus.py      # Modbus协议
│   │   │   ├── serial.py      # 串口通信
│   │   │   ├── mqtt.py        # MQTT协议
│   │   │   ├── can_bus.py     # 【新增】CAN总线支持
│   │   │   ├── tcp_ip.py      # 【新增】TCP/IP通信
│   │   │   ├── opc_ua.py      # 【新增】OPC UA协议
│   │   │   ├── i2c.py         # 【新增】I2C协议
│   │   │   └── spi.py         # 【新增】SPI协议
│   ├── data/                  # 数据处理层
|   |   ├── storage/           # 新增数据存储模块
│   │   │   ├── db_manager.py  # 数据库管理
│   │   │   ├── file_io.py     # 文件读写
│   │   │   └── export.py      # 数据导出
│   └── utils/                 # 辅助工具层
├── scripts/                   # 辅助脚本
├── pyproject.toml             # 项目配置
├── README.md                  # 项目说明
└── .gitignore                 # Git忽略规则
```

# 开发日志

## 阶段一：项目基础架构搭建（2周）

### 步骤1.1：环境配置与依赖安装（3天）

- **开发内容**：
  - 创建项目基础目录结构
  - 配置Python虚拟环境
  - 安装基础依赖（PyQt5/PySide6、NumPy、Pandas等）
  - 编写项目配置文件
- **测试方法**：
  - 编写简单的启动脚本，确认环境配置正确
  - 验证各个依赖库能够正常导入和使用

### 步骤1.2：核心框架层开发（5天）

- **开发内容**：
  - 开发应用程序入口（app.py）
  - 实现配置管理模块（config.py）
  - 开发线程管理器（thread_manager.py）
  - 开发事件总线系统（event_bus.py）
- **测试方法**：
  - 编写单元测试，验证配置项的读写功能
  - 创建模拟线程，测试线程管理器的创建、暂停和终止功能
  - 编写事件触发测试，确认事件总线可以正常工作

### 步骤1.3：基础UI框架搭建（6天）

- **开发内容**：
  - 使用Qt Designer设计主窗口和基础面板
  - 开发UI加载器，动态加载*.ui文件
  - 设计主菜单和工具栏
  - 实现基础状态栏和日志显示区
- **测试方法**：
  - 不要编写单元测试，让用户运行程序，观察UI交互是否存在问题
  - 运行应用，测试UI布局是否正确显示
  - 验证菜单项和工具栏按钮是否有正确响应
  - 测试窗口调整大小和最大化/最小化功能

## 阶段二：数据可视化模块开发（3周）

### 步骤2.1：图表底层框架开发（1周）

- **开发内容**：
  - 开发图表管理器（chart_manager.py）
  - 设计图表基类，定义通用接口
  - 实现图表区域的动态布局系统
  - 用户可以添加或者删除图表
  - 开发数据绑定接口
- **测试方法**：
  - 构造虚拟数据对象，测试数据绑定机制
  - 验证图表区域能够正确地添加和移除图表（用户启动程序后，通过交互进行测试）
  - 测试布局系统在不同窗口大小下的适应性（用户进行测试反馈）

### 步骤2.2：基础图表类型实现（1周）

- **开发内容**：
  - 实现折线图（line_chart.py）
  - 实现散点图（scatter.py）
  - 实现柱状图（histogram.py）
  - 实现时间序列图（time_series.py）
- **测试虚拟数据**：
  ```python
  # 折线图虚拟数据
  import numpy as np
  x = np.linspace(0, 10, 100)
  y = np.sin(x)

  # 散点图虚拟数据
  points_x = np.random.normal(0, 1, 100)
  points_y = np.random.normal(0, 1, 100)

  # 柱状图虚拟数据
  categories = ['类别A', '类别B', '类别C', '类别D', '类别E']
  values = np.random.randint(10, 100, len(categories))

  # 时间序列虚拟数据
  from datetime import datetime, timedelta
  dates = [datetime.now() + timedelta(days=i) for i in range(30)]
  values = np.cumsum(np.random.normal(0, 1, 30))
  ```
- **测试方法**：
  - 加载虚拟数据，验证每种图表类型的正确渲染
  - 测试图表的缩放、平移和重置功能
  - 测试图表的标题、坐标轴和图例设置

### 步骤2.3：高级图表类型实现（1周）

- **开发内容**：
  - 实现热力图（heatmap.py）
  - 实现饼图（pie_chart.py）
  - 实现箱线图（box_plot.py）
  - 实现雷达图（radar.py）
- **测试虚拟数据**：
  ```python
  # 热力图虚拟数据
  heatmap_data = np.random.rand(20, 20)

  # 饼图虚拟数据
  labels = ['类别A', '类别B', '类别C', '类别D']
  sizes = [15, 30, 45, 10]

  # 箱线图虚拟数据
  box_data = [np.random.normal(0, std, 100) for std in range(1, 4)]

  # 雷达图虚拟数据
  categories = ['速度', '力量', '耐力', '敏捷', '智力', '魅力']
  values = [80, 70, 60, 90, 75, 85]
  ```
- **测试方法**：
  - 加载虚拟数据，验证每种高级图表类型的正确渲染
  - 测试图表的交互功能（如饼图的扇区选择）
  - 测试图表的配色方案和样式定制

## 阶段三：协议解析模块开发（4周）

### 步骤3.1：协议适配器基类设计（1周）

- **开发内容**：
  - 设计协议适配器基类，定义统一接口
  - 实现协议监听和数据回调机制
  - 开发多线程协议处理框架
  - 设计协议错误处理机制
- **测试方法**：
  - 编写模拟协议的测试适配器
  - 验证线程安全性和异常处理机制
  - 测试数据回调机制的稳定性

### 步骤3.2：串口和Modbus协议实现（1周）

- **开发内容**：
  - 实现串口通信适配器（serial.py）
  - 实现Modbus协议适配器（modbus.py）
  - 开发协议配置界面
- **硬件测试软件**：
  - **安装Virtual Serial Port Driver (VSPD)**：
    1. 下载VSPD安装包
    2. 运行安装程序，按提示完成安装
    3. 创建一对虚拟串口（如COM3和COM4）
  - **安装Modbus Slave**：
    1. 下载Modbus Slave软件
    2. 配置模拟从站，设置寄存器值
- **测试方法**：
  - 使用VSPD创建虚拟串口对，测试串口收发功能
  - 使用Modbus Slave模拟从站，测试Modbus读写功能
  - 验证串口和Modbus数据能正确传输到图表显示

### 步骤3.3：网络协议实现（1周）

- **开发内容**：
  - 实现TCP/IP通信适配器（tcp_ip.py）
  - 实现MQTT协议适配器（mqtt.py）
  - 开发网络设置界面
- **硬件测试软件**：
  - **安装Packet Sender**：
    1. 下载Packet Sender安装包
    2. 安装并配置发送的数据包
  - **安装Mosquitto**：
    1. 下载并安装Mosquitto MQTT代理
    2. 配置本地MQTT服务器
    3. 安装MQTT.fx客户端工具进行测试
- **测试方法**：
  - 使用Packet Sender发送TCP/UDP测试数据
  - 配置Mosquitto建立本地MQTT服务器
  - 使用MQTT.fx发布测试消息，验证订阅功能

### 步骤3.4：工业总线协议实现（1周）

- **开发内容**：
  - 实现CAN总线适配器（can_bus.py）
  - 实现OPC UA协议适配器（opc_ua.py）
  - 实现I2C/SPI协议适配器（i2c.py、spi.py）
- **硬件测试软件**：
  - **安装PCAN-View**：
    1. 下载并安装PCAN-View软件
    2. 如有CAN分析仪硬件，连接设备
  - **安装Prosys OPC UA Simulation Server**：
    1. 下载并安装OPC UA模拟服务器
    2. 配置数据点和变量
- **测试方法**：
  - 使用PCAN-View模拟CAN总线通信（如无硬件，可使用软件模拟）
  - 配置Prosys OPC UA模拟服务器，测试OPC UA客户端功能
  - 对于I2C/SPI，可使用虚拟设备或编写模拟脚本

## 阶段四：数据处理与存储模块开发（2周）

### 步骤4.1：实时数据处理（1周）

- **开发内容**：
  - 实现环形缓冲区数据存储
  - 开发数据滤波和异常处理算法
  - 实现数据统计分析功能（最大/最小/平均值等）
- **测试方法**：
  - 构造含噪声的虚拟数据流，测试滤波效果
  - 模拟高频数据，测试缓冲区性能
  - 验证统计分析结果的准确性

### 步骤4.2：数据存储与导出（1周）

- **开发内容**：
  - 实现数据库存储功能（db_manager.py）
  - 开发文件读写功能（file_io.py）
  - 实现数据导出功能（export.py）
- **测试方法**：
  - 测试数据库的创建、写入和查询功能
  - 验证CSV、Excel等格式的数据导出功能
  - 测试大数据量存储和读取的性能

## 阶段五：系统集成与优化（3周）

### 步骤5.1：模块集成（1周）

- **开发内容**：
  - 整合UI、可视化、协议和数据处理模块
  - 实现跨模块通信和数据流
  - 开发配置保存和加载功能
- **测试方法**：
  - 运行完整应用，验证各模块协同工作
  - 测试配置的保存和恢复功能
  - 验证数据从采集到显示的完整流程

### 步骤5.2：性能优化（1周）

- **开发内容**：
  - 优化大数据量渲染性能
  - 减少内存占用和CPU使用率
  - 优化多线程协调效率
- **测试方法**：
  - 使用性能分析工具（如cProfile）定位瓶颈
  - 测试高频数据更新下的UI响应性
  - 长时间运行测试，验证内存泄漏问题

### 步骤5.3：综合测试与bug修复（1周）

- **开发内容**：
  - 进行全面的功能测试
  - 修复发现的bug
  - 优化用户体验细节
- **测试方法**：
  - 编写自动化测试脚本
  - 模拟各种异常情况，测试系统稳定性
  - 收集用户反馈，改进界面和交互

## 阶段六：文档编写与发布准备（1周）

### 步骤6.1：文档编写（3天）

- **开发内容**：
  - 编写用户手册
  - 完善开发文档
  - 编写API文档
- **测试方法**：
  - 根据文档尝试使用软件，验证文档的准确性和完整性
  - 检查API文档与实际代码的一致性

### 步骤6.2：打包与发布准备（4天）

- **开发内容**：
  - 创建安装包（如使用PyInstaller）
  - 编写安装向导
  - 准备发布说明
- **测试方法**：
  - 在不同环境中测试安装包
  - 验证安装和卸载过程
  - 检查依赖项是否完整

# UI交互逻辑

1. **启动程序**
   - **入口文件**：通过 `main.py` 启动程序，实例化 `Application` 类并调用其 `run()` 方法。
   - **初始化**：
     - 加载配置文件，设置应用名称和版本。
     - 初始化日志系统，记录应用运行状态。
     - 显示启动画面，提供用户反馈。

2. **创建主窗口**
   - **UI加载**：
     - 使用 Qt Designer 设计的 `.ui` 文件加载主窗口界面。
     - 连接主窗口的基本信号和槽，例如退出、工具栏和状态栏的可见性切换。
   - **图表管理器**：
     - 在主窗口中实例化 `ChartManager`，负责管理所有图表的添加、删除和布局。

3. **创建图表**
   - **用户交互**：
     - 用户通过菜单或工具栏选择图表类型（如折线图、柱状图等）。
     - 触发相应的信号，调用 `ChartManager` 的 `add_chart()` 方法，传入所选图表类型和数据。
   - **数据绑定**：
     - 在 `ChartManager` 中，创建图表实例并将其添加到布局中。
     - 通过数据绑定接口，将数据与图表进行关联，确保图表能够正确显示数据。

4. **硬件协议连接**
   - **协议选择**：
     - 用户在界面中选择要使用的硬件协议（如串口、Modbus、TCP/IP等）。
     - 根据选择，实例化相应的协议适配器（如 `SerialAdapter`、`ModbusAdapter`）。
   - **连接硬件**：
     - 调用适配器的连接方法，建立与硬件的连接。
     - 在连接成功后，更新UI状态，提示用户连接状态。

5. **实时数据处理**
   - **数据接收**：
     - 在协议适配器中实现数据接收逻辑，使用多线程避免阻塞UI线程。
     - 接收到的数据通过信号发送到主线程，更新图表数据。
   - **数据展示**：
     - 在图表中实时更新数据，使用 `ChartManager` 的数据渲染引擎进行图表的动态更新。
     - 确保图表能够处理高频数据更新，保持流畅的用户体验。

6. **数据存储**
   - **实时数据存储**：
     - 使用环形缓冲区或数据库（如 SQLite）存储实时数据，确保数据的持久性。
     - 定期将数据写入数据库，支持后续的数据分析和可视化。
   - **数据清洗**：
     - 在存储过程中，进行数据清洗和异常值处理，确保数据质量。

7. **数据导入与可视化**
   - **导入数据**：
     - 用户通过界面选择导入数据的文件（如 CSV、Excel）。
     - 使用 Pandas 等库读取数据文件，并进行格式转换。
   - **数据可视化**：
     - 将导入的数据传递给 `ChartManager`，创建相应的图表实例。
     - 通过数据绑定接口，将导入的数据与图表进行关联，确保图表能够正确显示。

8. **用户反馈与错误处理**
   - **错误处理**：
     - 在应用中实现全局异常处理，捕获未处理的异常并通过信号显示错误对话框。
     - 记录错误日志，便于后续的调试和问题解决。
   - **用户反馈**：
     - 提供状态栏消息，实时反馈应用状态和操作结果。
     - 在图表操作（如添加、删除）后，更新用户界面，确保用户能够清晰地看到操作结果。
