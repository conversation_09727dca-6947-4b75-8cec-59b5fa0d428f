#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
设备管理器模块

提供管理和操作协议设备的高级接口，协调多设备操作。
"""

import logging
import threading
import time
from enum import Enum
from typing import Any, Callable, Dict, List, Optional, Set, Tuple, Union

try:
    from PyQt5.QtCore import QObject, pyqtSignal
except ImportError:
    try:
        from PySide6.QtCore import QObject, Signal as pyqtSignal
    except ImportError:
        # 如果没有安装Qt库，则创建一个简单的替代类
        class QObject:
            pass
        
        class PyQtSignalMock:
            def __init__(self, *args):
                pass
                
            def emit(self, *args):
                pass
                
            def connect(self, func):
                pass
                
        # 将模拟类赋值给pyqtSignal
        pyqtSignal = PyQtSignalMock

from src.hardware.device_factory import DeviceFactory, DeviceType
from src.hardware.protocol_base import ProtocolBase, ProtocolEventType

# 获取logger
logger = logging.getLogger(__name__)

class DeviceManagerSignals(QObject):
    """设备管理器信号类"""
    # 设备状态信号
    device_connected = pyqtSignal(str)          # 设备ID
    device_disconnected = pyqtSignal(str)       # 设备ID
    device_error = pyqtSignal(str, object)      # 设备ID, 错误对象
    device_data_received = pyqtSignal(str, object)  # 设备ID, 数据对象
    
    # 管理器状态信号
    manager_initialized = pyqtSignal()          # 初始化完成
    manager_shutdown = pyqtSignal()             # 关闭完成


class DeviceManager:
    """
    设备管理器类
    
    提供管理和操作协议设备的高级接口，协调多设备操作。
    采用单例模式，确保全局只有一个设备管理器实例。
    """
    
    # 单例实例
    _instance = None
    _initialized = False
    
    def __new__(cls):
        """创建单例实例"""
        if cls._instance is None:
            cls._instance = super(DeviceManager, cls).__new__(cls)
        return cls._instance
    
    def __init__(self):
        """初始化设备管理器"""
        # 避免重复初始化
        if self._initialized:
            return
            
        # 创建信号对象
        self._signals = DeviceManagerSignals()
        
        # 设备事件回调映射
        self._event_callbacks = {}
        
        # 管理器状态
        self._running = False
        self._lock = threading.RLock()
        
        # 注册协议类
        self._register_protocols()
        
        # 标记为已初始化
        self._initialized = True
        logger.info("设备管理器已初始化")
        
        # 发送初始化完成信号
        self._signals.manager_initialized.emit()
    
    def _register_protocols(self) -> None:
        """注册协议类"""
        # 注册串口协议
        try:
            from src.hardware.protocols.serial_protocol import SerialProtocol
            DeviceFactory.register_protocol(DeviceType.SERIAL, SerialProtocol)
        except ImportError:
            logger.warning("无法导入串口协议类")
        
        # 未来可以在这里注册其他协议
        # 例如：
        # try:
        #     from src.hardware.protocols.modbus_protocol import ModbusProtocol
        #     DeviceFactory.register_protocol(DeviceType.MODBUS, ModbusProtocol)
        # except ImportError:
        #     logger.warning("无法导入Modbus协议类")
    
    @property
    def signals(self) -> DeviceManagerSignals:
        """获取信号对象"""
        return self._signals
    
    def create_device(self, device_type: DeviceType, config: Dict = None, 
                     device_id: str = None) -> Optional[str]:
        """
        创建设备
        
        Args:
            device_type: 设备类型
            config: 设备配置
            device_id: 设备ID（可选）
            
        Returns:
            设备ID，如果创建失败则返回None
        """
        with self._lock:
            device = DeviceFactory.create_device(device_type, device_id, config)
            if device is None:
                return None
                
            # 设备ID（由工厂生成或使用传入的ID）
            if device_id is None:
                device_id = next(
                    (id for id, d in DeviceFactory.get_all_devices().items() if d == device),
                    None
                )
            
            # 注册事件回调
            self._register_device_callbacks(device_id, device)
            
            logger.info(f"已创建设备: {device_id} ({device_type.name})")
            return device_id
    
    def _register_device_callbacks(self, device_id: str, device: ProtocolBase) -> None:
        """
        注册设备事件回调
        
        Args:
            device_id: 设备ID
            device: 设备实例
        """
        # 创建设备的回调字典
        if device_id not in self._event_callbacks:
            self._event_callbacks[device_id] = {}
            
        # 创建事件回调
        event_callbacks = {
            ProtocolEventType.CONNECTED: lambda: self._on_device_connected(device_id),
            ProtocolEventType.DISCONNECTED: lambda: self._on_device_disconnected(device_id),
            ProtocolEventType.ERROR: lambda error: self._on_device_error(device_id, error),
            ProtocolEventType.DATA_RECEIVED: lambda data: self._on_device_data_received(device_id, data)
        }
        
        # 注册回调
        for event_type, callback in event_callbacks.items():
            # 存储回调
            self._event_callbacks[device_id][event_type] = callback
            # 注册到设备
            device.register_event_callback(event_type, callback)
    
    def _unregister_device_callbacks(self, device_id: str, device: ProtocolBase) -> None:
        """
        取消注册设备事件回调
        
        Args:
            device_id: 设备ID
            device: 设备实例
        """
        # 检查设备是否有回调
        if device_id not in self._event_callbacks:
            return
            
        # 取消注册回调
        for event_type, callback in self._event_callbacks[device_id].items():
            device.unregister_event_callback(event_type, callback)
            
        # 清除回调字典
        del self._event_callbacks[device_id]
    
    def _on_device_connected(self, device_id: str) -> None:
        """
        设备连接回调
        
        Args:
            device_id: 设备ID
        """
        logger.info(f"设备已连接: {device_id}")
        self._signals.device_connected.emit(device_id)
    
    def _on_device_disconnected(self, device_id: str) -> None:
        """
        设备断开连接回调
        
        Args:
            device_id: 设备ID
        """
        logger.info(f"设备已断开连接: {device_id}")
        self._signals.device_disconnected.emit(device_id)
    
    def _on_device_error(self, device_id: str, error: Any) -> None:
        """
        设备错误回调
        
        Args:
            device_id: 设备ID
            error: 错误对象
        """
        logger.error(f"设备错误: {device_id} - {error}")
        self._signals.device_error.emit(device_id, error)
    
    def _on_device_data_received(self, device_id: str, data: Any) -> None:
        """
        设备数据接收回调
        
        Args:
            device_id: 设备ID
            data: 接收到的数据
        """
        # 日志级别降低为DEBUG，避免大量日志
        logger.debug(f"设备接收数据: {device_id}")
        self._signals.device_data_received.emit(device_id, data)
    
    def connect_device(self, device_id: str) -> bool:
        """
        连接设备
        
        Args:
            device_id: 设备ID
            
        Returns:
            是否成功连接
        """
        with self._lock:
            return DeviceFactory.connect_device(device_id)
    
    def disconnect_device(self, device_id: str) -> bool:
        """
        断开设备连接
        
        Args:
            device_id: 设备ID
            
        Returns:
            是否成功断开连接
        """
        with self._lock:
            return DeviceFactory.disconnect_device(device_id)
    
    def remove_device(self, device_id: str) -> bool:
        """
        移除设备
        
        Args:
            device_id: 设备ID
            
        Returns:
            是否成功移除
        """
        with self._lock:
            # 获取设备
            device = DeviceFactory.get_device(device_id)
            if device is None:
                return False
                
            # 取消注册回调
            self._unregister_device_callbacks(device_id, device)
            
            # 移除设备
            return DeviceFactory.remove_device(device_id)
    
    def get_device(self, device_id: str) -> Optional[ProtocolBase]:
        """
        获取设备实例
        
        Args:
            device_id: 设备ID
            
        Returns:
            设备实例，如果不存在则返回None
        """
        return DeviceFactory.get_device(device_id)
    
    def get_all_devices(self) -> Dict[str, ProtocolBase]:
        """
        获取所有设备实例
        
        Returns:
            设备ID到设备实例的映射
        """
        return DeviceFactory.get_all_devices()
    
    def send_command(self, device_id: str, command: Any, wait_response: bool = False, 
                    timeout: float = 1.0) -> Optional[Any]:
        """
        向设备发送命令
        
        Args:
            device_id: 设备ID
            command: 要发送的命令
            wait_response: 是否等待响应
            timeout: 等待响应的超时时间（秒）
            
        Returns:
            如果wait_response为True，返回响应数据；否则返回None
            
        Raises:
            Exception: 发送命令失败时抛出
        """
        # 获取设备
        device = self.get_device(device_id)
        if device is None:
            raise ValueError(f"设备不存在: {device_id}")
            
        # 发送命令
        return device.send_command(command, wait_response, timeout)
    
    def shutdown(self) -> None:
        """关闭设备管理器，断开所有设备连接"""
        if not self._running:
            return
            
        with self._lock:
            # 断开所有设备连接
            for device_id in DeviceFactory.get_all_devices().keys():
                try:
                    DeviceFactory.disconnect_device(device_id)
                except Exception as e:
                    logger.error(f"断开设备连接失败: {device_id} - {e}")
            
            # 清除所有设备
            DeviceFactory.clear_all_devices()
            
            # 清除回调
            self._event_callbacks.clear()
            
            # 更新状态
            self._running = False
            
            # 发送关闭完成信号
            self._signals.manager_shutdown.emit()
            
            logger.info("设备管理器已关闭")
    
    def __del__(self):
        """析构函数，确保关闭所有设备连接"""
        self.shutdown()


# 创建全局设备管理器实例
device_manager = DeviceManager() 
 