#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
热力图模块

实现基于PyQtGraph的热力图，支持颜色映射、缩放等功能。
"""

import numpy as np
from typing import Dict, List, Optional, Tuple, Union, Any

try:
    import pyqtgraph as pg
    from pyqtgraph.Qt import QtCore, QtGui
except ImportError:
    # 如果导入失败，可能会在运行时另行处理
    pass

from ..base_chart import BaseChart, ChartType
from ...utils.logger import logger


class HeatmapChart(BaseChart):
    """
    热力图类
    
    实现热力图的创建、数据管理和样式设置等功能。
    """
    
    # 预定义的颜色映射
    COLORMAPS = {
        'hot': {'ticks': [(0.0, (0, 0, 0)), (0.33, (255, 0, 0)), (0.66, (255, 255, 0)), (1.0, (255, 255, 255))], 'mode': 'rgb'},
        'jet': {'ticks': [(0.0, (0, 0, 143)), (0.25, (0, 0, 255)), (0.5, (0, 255, 255)), (0.75, (255, 255, 0)), (1.0, (255, 0, 0))], 'mode': 'rgb'},
        'gray': {'ticks': [(0.0, (0, 0, 0)), (1.0, (255, 255, 255))], 'mode': 'rgb'},
        'viridis': {'ticks': [(0.0, (68, 1, 84)), (0.25, (59, 82, 139)), (0.5, (44, 162, 95)), (0.75, (156, 188, 29)), (1.0, (253, 231, 37))], 'mode': 'rgb'},
        'plasma': {'ticks': [(0.0, (13, 8, 135)), (0.25, (126, 3, 168)), (0.5, (204, 71, 120)), (0.75, (248, 149, 64)), (1.0, (240, 249, 33))], 'mode': 'rgb'},
        'magma': {'ticks': [(0.0, (0, 0, 4)), (0.25, (88, 20, 124)), (0.5, (189, 55, 103)), (0.75, (249, 142, 82)), (1.0, (252, 255, 191))], 'mode': 'rgb'},
        'inferno': {'ticks': [(0.0, (0, 0, 4)), (0.25, (87, 15, 109)), (0.5, (187, 55, 84)), (0.75, (249, 142, 9)), (1.0, (252, 254, 164))], 'mode': 'rgb'},
        'bone': {'ticks': [(0.0, (0, 0, 0)), (0.33, (86, 86, 116)), (0.66, (167, 199, 199)), (1.0, (255, 255, 255))], 'mode': 'rgb'},
        'autumn': {'ticks': [(0.0, (255, 0, 0)), (1.0, (255, 255, 0))], 'mode': 'rgb'},
        'winter': {'ticks': [(0.0, (0, 0, 255)), (1.0, (0, 255, 128))], 'mode': 'rgb'},
        'spring': {'ticks': [(0.0, (255, 0, 255)), (1.0, (255, 255, 0))], 'mode': 'rgb'},
        'summer': {'ticks': [(0.0, (0, 128, 102)), (1.0, (255, 255, 0))], 'mode': 'rgb'},
        'copper': {'ticks': [(0.0, (0, 0, 0)), (1.0, (255, 200, 128))], 'mode': 'rgb'},
        'hsv': {'ticks': [(0.0, (255, 0, 0)), (0.167, (255, 255, 0)), (0.333, (0, 255, 0)), 
                          (0.5, (0, 255, 255)), (0.667, (0, 0, 255)), (0.833, (255, 0, 255)), 
                          (1.0, (255, 0, 0))], 'mode': 'rgb'}
    }
    
    def __init__(
        self, 
        title: str = "热力图", 
        x_label: str = "X轴", 
        y_label: str = "Y轴"
    ):
        """
        初始化热力图
        
        Args:
            title: 图表标题
            x_label: X轴标签
            y_label: Y轴标签
        """
        super().__init__(title, x_label, y_label, ChartType.HEATMAP)
        
        # 热力图特有的属性
        self._image_item = None
        self._colormap = 'viridis'  # 默认颜色映射
        self._color_bar = None
        self._data_range = (0, 1)  # 数据范围，用于颜色映射
        self._current_data = None
        self._x_range = None
        self._y_range = None
        self._auto_level = True
        self._invert_y = False  # 是否反转Y轴
        self._aspect_locked = False  # 是否锁定宽高比
    
    def _initialize_specific(self):
        """实现图表特定的初始化"""
        # 创建ImageItem用于显示热力图
        self._image_item = pg.ImageItem()
        self._plot_item.addItem(self._image_item)
        
        # 创建颜色条
        self._create_color_bar()
        
        # 配置视图
        self._plot_item.setAspectLocked(self._aspect_locked)
        if self._invert_y:
            self._plot_item.getViewBox().invertY(True)
    
    def _create_color_bar(self):
        """创建颜色条"""
        # 创建线性色条
        color_bar = pg.GradientEditorItem()
        
        # 设置默认颜色映射
        color_bar.restoreState(self.COLORMAPS[self._colormap])
        
        # 添加到绘图项
        self._plot_item.layout.addItem(color_bar, 2, 1)
        
        # 存储引用
        self._color_bar = color_bar
        
        # 连接信号
        color_bar.sigGradientChanged.connect(self._color_map_changed)
    
    def _color_map_changed(self):
        """颜色映射变化处理"""
        if self._image_item is not None and self._current_data is not None:
            # 获取颜色映射
            lut = self._color_bar.getLookupTable(256)
            
            # 应用颜色映射
            self._image_item.setLookupTable(lut)
            
            # 更新颜色条
            self._color_bar.update()
    
    def add_data(
        self, 
        data_id: str, 
        data: Union[np.ndarray, List[List[float]]], 
        **kwargs
    ):
        """
        添加数据到热力图
        
        Args:
            data_id: 数据标识符
            data: 数据内容，二维数组表示热力图数据
            **kwargs: 额外配置参数，包括：
                - colormap: 颜色映射名称
                - data_range: 数据范围，用于颜色映射
                - x_range: X轴范围，tuple(min, max)
                - y_range: Y轴范围，tuple(min, max)
                - auto_level: 是否自动调整色阶
                - invert_y: 是否反转Y轴
                - aspect_locked: 是否锁定宽高比
        """
        if not self._initialized:
            self.initialize()
        
        try:
            # 确保数据是NumPy数组
            if isinstance(data, list):
                data = np.array(data)
            
            # 确保数据是二维的
            if data.ndim != 2:
                raise ValueError(f"热力图数据必须是二维数组，但获得了 {data.ndim} 维数据")
            
            # 解析参数
            colormap = kwargs.get('colormap', self._colormap)
            data_range = kwargs.get('data_range', None)
            x_range = kwargs.get('x_range', None)
            y_range = kwargs.get('y_range', None)
            auto_level = kwargs.get('auto_level', self._auto_level)
            invert_y = kwargs.get('invert_y', self._invert_y)
            aspect_locked = kwargs.get('aspect_locked', self._aspect_locked)
            
            # 更新设置
            if colormap in self.COLORMAPS and colormap != self._colormap:
                self._colormap = colormap
                self._color_bar.restoreState(self.COLORMAPS[colormap])
            
            # 更新数据范围
            if data_range is not None:
                self._data_range = data_range
            elif auto_level:
                self._data_range = (np.min(data), np.max(data))
            
            # 更新坐标范围
            self._x_range = x_range
            self._y_range = y_range
            
            # 更新其他设置
            self._auto_level = auto_level
            
            # 设置热力图数据
            self._image_item.setImage(
                data, 
                levels=self._data_range if not auto_level else None,
                autoLevels=auto_level
            )
            
            # 应用颜色映射
            lut = self._color_bar.getLookupTable(256)
            self._image_item.setLookupTable(lut)
            
            # 设置坐标范围
            if x_range is not None and y_range is not None:
                self._image_item.setRect(QtCore.QRectF(
                    x_range[0], y_range[0], 
                    x_range[1] - x_range[0], y_range[1] - y_range[0]
                ))
            
            # 配置视图
            self._plot_item.setAspectLocked(aspect_locked)
            if invert_y != self._invert_y:
                self._invert_y = invert_y
                self._plot_item.getViewBox().invertY(invert_y)
            
            # 存储数据
            self._current_data = data
            self._data_items[data_id] = self._image_item
            
            logger.debug(f"{self._log_prefix} 添加热力图数据: {data_id}, 形状: {data.shape}")
            
        except Exception as e:
            logger.error(f"{self._log_prefix} 添加数据失败: {str(e)}")
            raise
    
    def update_data(
        self, 
        data_id: str, 
        data: Union[np.ndarray, List[List[float]]], 
        **kwargs
    ):
        """
        更新热力图中的数据
        
        Args:
            data_id: 数据标识符
            data: 新的数据内容
            **kwargs: 额外配置参数，与add_data相同
        """
        if not self._initialized:
            self.initialize()
        
        # 热力图只支持一个数据集，所以调用add_data即可
        self.add_data(data_id, data, **kwargs)
    
    def set_colormap(self, colormap_name: str):
        """
        设置颜色映射
        
        Args:
            colormap_name: 颜色映射名称
        """
        if not self._initialized or colormap_name not in self.COLORMAPS:
            return
        
        self._colormap = colormap_name
        self._color_bar.restoreState(self.COLORMAPS[colormap_name])
        
        # 重新应用颜色映射
        if self._image_item is not None and self._current_data is not None:
            lut = self._color_bar.getLookupTable(256)
            self._image_item.setLookupTable(lut)
    
    def set_data_range(self, min_val: float, max_val: float):
        """
        设置数据范围
        
        Args:
            min_val: 最小值
            max_val: 最大值
        """
        if not self._initialized or self._image_item is None:
            return
        
        self._data_range = (min_val, max_val)
        self._image_item.setLevels(self._data_range)
        self._auto_level = False
    
    def set_auto_level(self, enabled: bool):
        """
        设置是否自动调整色阶
        
        Args:
            enabled: 是否启用
        """
        if not self._initialized or self._image_item is None:
            return
        
        self._auto_level = enabled
        if enabled and self._current_data is not None:
            # 重新计算数据范围
            min_val = np.min(self._current_data)
            max_val = np.max(self._current_data)
            self._data_range = (min_val, max_val)
            self._image_item.setLevels(self._data_range)
    
    def set_aspect_locked(self, locked: bool):
        """
        设置是否锁定宽高比
        
        Args:
            locked: 是否锁定
        """
        if not self._initialized:
            return
        
        self._aspect_locked = locked
        self._plot_item.setAspectLocked(locked)
    
    def set_invert_y(self, invert: bool):
        """
        设置是否反转Y轴
        
        Args:
            invert: 是否反转
        """
        if not self._initialized:
            return
        
        self._invert_y = invert
        self._plot_item.getViewBox().invertY(invert)
    
    def add_text_overlay(self, data_values, fmt: str = "{:.1f}"):
        """
        添加文本叠加层，显示每个单元格的值
        
        Args:
            data_values: 要显示的数据值，应与热力图数据形状相同
            fmt: 数值格式化字符串
        """
        if not self._initialized or self._current_data is None:
            return
        
        # 确保数据是NumPy数组
        if isinstance(data_values, list):
            data_values = np.array(data_values)
        
        # 检查形状是否匹配
        if data_values.shape != self._current_data.shape:
            raise ValueError(f"文本数据形状 {data_values.shape} 与热力图数据形状 {self._current_data.shape} 不匹配")
        
        # 清除现有的文本项
        for item in self._plot_item.items:
            if isinstance(item, pg.TextItem):
                self._plot_item.removeItem(item)
        
        # 获取坐标范围
        rect = self._image_item.boundingRect()
        x_min, y_min = rect.left(), rect.top()
        width, height = rect.width(), rect.height()
        
        # 计算单元格大小
        cell_width = width / data_values.shape[1]
        cell_height = height / data_values.shape[0]
        
        # 添加文本项
        for i in range(data_values.shape[0]):
            for j in range(data_values.shape[1]):
                # 计算文本位置（单元格中心）
                x = x_min + j * cell_width + cell_width / 2
                y = y_min + i * cell_height + cell_height / 2
                
                # 创建文本项
                text = fmt.format(data_values[i, j])
                text_item = pg.TextItem(text, anchor=(0.5, 0.5))
                
                # 设置文本颜色（根据背景色自适应）
                value = self._current_data[i, j]
                if value < (self._data_range[0] + self._data_range[1]) / 2:
                    text_item.setColor((255, 255, 255))  # 浅色背景用白色文本
                else:
                    text_item.setColor((0, 0, 0))  # 深色背景用黑色文本
                
                # 添加到绘图项
                self._plot_item.addItem(text_item)
                text_item.setPos(x, y)
    
    def get_current_data(self):
        """获取当前热力图数据"""
        return self._current_data

    def set_position(self, data_id: str, x_range: Tuple[float, float], y_range: Tuple[float, float]):
        """
        设置热力图位置和大小
        
        Args:
            data_id: 数据标识符
            x_range: X轴范围
            y_range: Y轴范围
        """
        if not self._initialized or data_id not in self._data_items:
            return
        
        self._x_range = x_range
        self._y_range = y_range
        
        # 设置图像位置和大小
        self._image_item.setRect(QtCore.QRectF(
            x_range[0], y_range[0], 
            x_range[1] - x_range[0], y_range[1] - y_range[0]
        ))
