#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
串口配置面板

提供串口协议的配置界面。
"""

import logging
from typing import Any, Dict, List, Optional, Tuple

try:
    from PyQt5.QtWidgets import (
        QWidget, QLabel, QComboBox, QLineEdit, QCheckBox, QSpinBox,
        QDoubleSpinBox, QFormLayout, QHBoxLayout, QVBoxLayout, QPushButton,
        QGroupBox
    )
    from PyQt5.QtCore import Qt, pyqtSlot as Slot
except ImportError:
    try:
        from PySide6.QtWidgets import (
            QWidget, QLabel, QComboBox, QLineEdit, QCheckBox, QSpinBox,
            QDoubleSpinBox, QFormLayout, QHBoxLayout, QVBoxLayout, QPushButton,
            QGroupBox
        )
        from PySide6.QtCore import Qt, Slot
    except ImportError:
        raise ImportError("未安装PyQt5或PySide6，请安装其中之一")

from src.ui.config_panels.protocol_config_base import ProtocolConfigPanelBase
from src.hardware.protocols.serial_protocol import SerialProtocol

# 获取logger
logger = logging.getLogger(__name__)

class SerialConfigPanel(ProtocolConfigPanelBase):
    """
    串口配置面板
    
    提供串口协议的配置界面，支持串口、波特率、数据位等参数的设置。
    """
    
    def __init__(self, parent=None):
        """初始化串口配置面板"""
        super().__init__(parent)
        
        # 设置协议名称和描述
        self._protocol_name = "串口协议"
        self._protocol_description = "基于pyserial的串口通信协议"
        
        # 初始化UI
        self._init_ui()
        
        # 绑定事件
        self._bind_events()
        
        # 加载默认配置
        self.load_defaults()
        
    def _init_ui(self) -> None:
        """初始化UI"""
        # 创建主布局
        main_layout = QVBoxLayout()
        self.setLayout(main_layout)
        
        # 创建表单布局
        form_layout = QFormLayout()
        form_layout.setFieldGrowthPolicy(QFormLayout.AllNonFixedFieldsGrow)
        
        # === 基本设置 ===
        basic_group = QGroupBox("基本设置")
        basic_layout = QFormLayout()
        
        # 端口设置
        port_layout = QHBoxLayout()
        self.port_combo = QComboBox()
        self.port_combo.setEditable(True)
        self.refresh_button = QPushButton("刷新")
        port_layout.addWidget(self.port_combo)
        port_layout.addWidget(self.refresh_button)
        
        # 波特率设置
        self.baudrate_combo = QComboBox()
        baudrates = ["110", "300", "600", "1200", "2400", "4800", "9600", 
                    "14400", "19200", "38400", "57600", "115200", "230400", 
                    "460800", "921600"]
        self.baudrate_combo.addItems(baudrates)
        self.baudrate_combo.setCurrentText("9600")
        
        # 数据位设置
        self.bytesize_combo = QComboBox()
        self.bytesize_combo.addItems(["5", "6", "7", "8"])
        self.bytesize_combo.setCurrentText("8")
        
        # 校验位设置
        self.parity_combo = QComboBox()
        parity_options = [
            ("无校验", "N"),
            ("偶校验", "E"),
            ("奇校验", "O"),
            ("标记校验", "M"),
            ("空格校验", "S")
        ]
        for text, value in parity_options:
            self.parity_combo.addItem(text, value)
        
        # 停止位设置
        self.stopbits_combo = QComboBox()
        stopbits_options = [
            ("1", 1),
            ("1.5", 1.5),
            ("2", 2)
        ]
        for text, value in stopbits_options:
            self.stopbits_combo.addItem(text, value)
        
        # 添加到基本设置布局
        basic_layout.addRow("串口:", port_layout)
        basic_layout.addRow("波特率:", self.baudrate_combo)
        basic_layout.addRow("数据位:", self.bytesize_combo)
        basic_layout.addRow("校验位:", self.parity_combo)
        basic_layout.addRow("停止位:", self.stopbits_combo)
        
        basic_group.setLayout(basic_layout)
        main_layout.addWidget(basic_group)
        
        # === 高级设置 ===
        advanced_group = QGroupBox("高级设置")
        advanced_layout = QFormLayout()
        
        # 超时设置
        self.timeout_spinbox = QDoubleSpinBox()
        self.timeout_spinbox.setRange(0.0, 120.0)
        self.timeout_spinbox.setValue(1.0)
        self.timeout_spinbox.setSuffix(" 秒")
        
        self.write_timeout_spinbox = QDoubleSpinBox()
        self.write_timeout_spinbox.setRange(0.0, 120.0)
        self.write_timeout_spinbox.setValue(1.0)
        self.write_timeout_spinbox.setSuffix(" 秒")
        
        # 流控制设置
        self.xonxoff_checkbox = QCheckBox("启用")
        self.rtscts_checkbox = QCheckBox("启用")
        self.dsrdtr_checkbox = QCheckBox("启用")
        
        # 缓冲区设置
        self.buffer_size_spinbox = QSpinBox()
        self.buffer_size_spinbox.setRange(256, 65536)
        self.buffer_size_spinbox.setValue(4096)
        self.buffer_size_spinbox.setSingleStep(256)
        
        # 行结束符设置
        self.eol_combo = QComboBox()
        eol_options = [
            ("\\n (LF)", b"\n"),
            ("\\r\\n (CRLF)", b"\r\n"),
            ("\\r (CR)", b"\r")
        ]
        for text, value in eol_options:
            self.eol_combo.addItem(text, value)
        
        # 编码设置
        self.encoding_combo = QComboBox()
        encodings = ["utf-8", "ascii", "utf-16", "gbk", "gb2312"]
        self.encoding_combo.addItems(encodings)
        
        # 读取模式设置
        self.read_mode_combo = QComboBox()
        read_mode_options = [
            ("按行读取", "line"),
            ("原始字节", "raw")
        ]
        for text, value in read_mode_options:
            self.read_mode_combo.addItem(text, value)
        
        # 添加到高级设置布局
        advanced_layout.addRow("读取超时:", self.timeout_spinbox)
        advanced_layout.addRow("写入超时:", self.write_timeout_spinbox)
        advanced_layout.addRow("软件流控 (XON/XOFF):", self.xonxoff_checkbox)
        advanced_layout.addRow("硬件流控 (RTS/CTS):", self.rtscts_checkbox)
        advanced_layout.addRow("硬件流控 (DSR/DTR):", self.dsrdtr_checkbox)
        advanced_layout.addRow("缓冲区大小:", self.buffer_size_spinbox)
        advanced_layout.addRow("行结束符:", self.eol_combo)
        advanced_layout.addRow("字符编码:", self.encoding_combo)
        advanced_layout.addRow("读取模式:", self.read_mode_combo)
        
        advanced_group.setLayout(advanced_layout)
        main_layout.addWidget(advanced_group)
        
        # 添加弹性空间
        main_layout.addStretch(1)
        
    def _bind_events(self) -> None:
        """绑定事件"""
        # 刷新端口按钮
        self.refresh_button.clicked.connect(self._refresh_ports)
        
        # 所有控件变化时触发配置改变信号
        self.port_combo.currentIndexChanged.connect(self._on_config_changed)
        self.port_combo.currentTextChanged.connect(self._on_user_input_changed)
        self.baudrate_combo.currentIndexChanged.connect(self._on_config_changed)
        self.bytesize_combo.currentIndexChanged.connect(self._on_config_changed)
        self.parity_combo.currentIndexChanged.connect(self._on_config_changed)
        self.stopbits_combo.currentIndexChanged.connect(self._on_config_changed)
        self.timeout_spinbox.valueChanged.connect(self._on_config_changed)
        self.write_timeout_spinbox.valueChanged.connect(self._on_config_changed)
        self.xonxoff_checkbox.stateChanged.connect(self._on_config_changed)
        self.rtscts_checkbox.stateChanged.connect(self._on_config_changed)
        self.dsrdtr_checkbox.stateChanged.connect(self._on_config_changed)
        self.buffer_size_spinbox.valueChanged.connect(self._on_config_changed)
        self.eol_combo.currentIndexChanged.connect(self._on_config_changed)
        self.encoding_combo.currentIndexChanged.connect(self._on_config_changed)
        self.read_mode_combo.currentIndexChanged.connect(self._on_config_changed)
    
    def _refresh_ports(self) -> None:
        """刷新可用串口列表"""
        try:
            # 保存当前选择的端口
            current_port = self.port_combo.currentData()
            
            # 清空端口列表
            self.port_combo.clear()
            
            # 获取可用串口列表
            ports = SerialProtocol.list_available_ports()
            
            if not ports:
                self.port_combo.addItem("无可用串口", None)
            else:
                # 添加可用串口
                for port, desc, hwid in ports:
                    self.port_combo.addItem(f"{port} ({desc})", port)
                    
                # 尝试恢复之前选择的端口
                if current_port:
                    index = self.port_combo.findData(current_port)
                    if index >= 0:
                        self.port_combo.setCurrentIndex(index)
                        
            logger.debug(f"刷新串口列表，找到 {len(ports)} 个可用端口")
            
        except Exception as e:
            logger.error(f"刷新串口列表失败: {str(e)}")
            self.port_combo.addItem("获取串口列表失败", None)
    
    @Slot()
    def _on_config_changed(self) -> None:
        """配置变更处理函数"""
        self._update_config_from_ui()
        self.config_changed.emit()

    def _on_user_input_changed(self, text: str) -> None:
        """处理用户输入的串口名称"""
        if text:  # 确保输入不为空
            self._config["port"] = text  # 更新配置中的串口名称
            # 不触发config_changed信号，避免重复更新
            
    def load_defaults(self) -> None:
        """加载默认配置"""
        self._config = SerialProtocol.DEFAULT_CONFIG.copy()
        self._update_ui_from_config()
        
        # 刷新可用串口列表
        self._refresh_ports()
    
    def validate_config(self) -> bool:
        """
        验证当前配置
        
        Returns:
            配置是否有效
        """
        try:
            # 获取当前配置
            config = self.get_config()
            
            # 检查端口是否选择
            if config["port"] is None:
                logger.warning("未选择串口")
                return False
                
            # 尝试实例化串口协议并验证配置
            protocol = SerialProtocol()
            protocol._validate_config(config)
            
            return True
            
        except ValueError as e:
            logger.error(f"串口配置验证失败: {str(e)}")
            return False
            
        except Exception as e:
            logger.error(f"串口配置验证异常: {str(e)}")
            return False
    
    def _update_ui_from_config(self) -> None:
        """根据配置更新UI控件"""
        config = self._config
        
        # 更新端口
        if config.get("port"):
            index = self.port_combo.findData(config["port"])
            if index >= 0:
                self.port_combo.setCurrentIndex(index)
        
        # 更新波特率
        self.baudrate_combo.setCurrentText(str(config.get("baudrate", 9600)))
        
        # 更新数据位
        self.bytesize_combo.setCurrentText(str(config.get("bytesize", 8)))
        
        # 更新校验位
        index = self.parity_combo.findData(config.get("parity", "N"))
        if index >= 0:
            self.parity_combo.setCurrentIndex(index)
        
        # 更新停止位
        index = self.stopbits_combo.findData(config.get("stopbits", 1))
        if index >= 0:
            self.stopbits_combo.setCurrentIndex(index)
        
        # 更新超时设置
        self.timeout_spinbox.setValue(config.get("timeout", 1.0))
        self.write_timeout_spinbox.setValue(config.get("write_timeout", 1.0))
        
        # 更新流控制设置
        self.xonxoff_checkbox.setChecked(config.get("xonxoff", False))
        self.rtscts_checkbox.setChecked(config.get("rtscts", False))
        self.dsrdtr_checkbox.setChecked(config.get("dsrdtr", False))
        
        # 更新缓冲区设置
        self.buffer_size_spinbox.setValue(config.get("buffer_size", 4096))
        
        # 更新行结束符
        eol = config.get("eol", b"\n")
        index = self.eol_combo.findData(eol)
        if index >= 0:
            self.eol_combo.setCurrentIndex(index)
        
        # 更新编码
        encoding = config.get("encoding", "utf-8")
        index = self.encoding_combo.findText(encoding)
        if index >= 0:
            self.encoding_combo.setCurrentIndex(index)
        
        # 更新读取模式
        read_mode = config.get("read_mode", "line")
        index = self.read_mode_combo.findData(read_mode)
        if index >= 0:
            self.read_mode_combo.setCurrentIndex(index)
    
    def _update_config_from_ui(self) -> None:
        """从UI控件更新配置"""
        # 获取端口 - 可以是下拉选择的或用户输入的
        port = self.port_combo.currentText()
        if port == "无可用串口" or port == "获取串口列表失败":
            port = None
        
        # 获取波特率
        baudrate = int(self.baudrate_combo.currentText())
        
        # 获取数据位
        bytesize = int(self.bytesize_combo.currentText())
        
        # 获取校验位
        parity = self.parity_combo.currentData()
        
        # 获取停止位
        stopbits = self.stopbits_combo.currentData()
        
        # 获取超时设置
        timeout = self.timeout_spinbox.value()
        write_timeout = self.write_timeout_spinbox.value()
        
        # 获取流控制设置
        xonxoff = self.xonxoff_checkbox.isChecked()
        rtscts = self.rtscts_checkbox.isChecked()
        dsrdtr = self.dsrdtr_checkbox.isChecked()
        
        # 获取缓冲区设置
        buffer_size = self.buffer_size_spinbox.value()
        
        # 获取行结束符
        eol = self.eol_combo.currentData()
        
        # 获取编码
        encoding = self.encoding_combo.currentText()
        
        # 获取读取模式
        read_mode = self.read_mode_combo.currentData()
        
        # 更新配置
        self._config.update({
            "port": port,
            "baudrate": baudrate,
            "bytesize": bytesize,
            "parity": parity,
            "stopbits": stopbits,
            "timeout": timeout,
            "write_timeout": write_timeout,
            "xonxoff": xonxoff,
            "rtscts": rtscts,
            "dsrdtr": dsrdtr,
            "buffer_size": buffer_size,
            "eol": eol,
            "encoding": encoding,
            "read_mode": read_mode
        })

    def get_config(self) -> Dict[str, Any]:
        """
        获取当前配置，并处理bytes类型
        
        Returns:
            当前协议配置字典
        """
        config = super().get_config()
        
        # 转换bytes类型为字符串
        for key, value in list(config.items()):
            if isinstance(value, bytes):
                # 使用特殊格式标记这是一个bytes对象，以便后续还原
                config[key] = f"__bytes__{value.hex()}"
        
        return config 