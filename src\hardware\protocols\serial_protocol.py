#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
串口协议模块

实现基于pyserial的串口通信协议适配器。
"""

import logging
import queue
import threading
import time
from typing import Any, Dict, List, Optional, Tuple, Union

try:
    import serial
    from serial.tools import list_ports
except ImportError:
    raise ImportError("未安装pyserial库，请使用pip install pyserial安装")

from src.hardware.protocol_base import (
    ProtocolBase, ProtocolEventType, 
    ConnectionError, TimeoutError, CommandError
)

# 获取logger
logger = logging.getLogger(__name__)

class SerialProtocol(ProtocolBase):
    """
    串口协议适配器
    
    实现基于pyserial的串口通信功能，支持异步数据接收和命令发送。
    """
    
    # 默认串口配置
    DEFAULT_CONFIG = {
        "port": None,         # 串口名称，如"COM1"或"/dev/ttyUSB0"
        "baudrate": 9600,     # 波特率
        "bytesize": 8,        # 数据位 (5, 6, 7, 8)
        "parity": "N",        # 校验位 ("N"=无校验, "E"=偶校验, "O"=奇校验, "M"=标记校验, "S"=空格校验)
        "stopbits": 1,        # 停止位 (1, 1.5, 2)
        "timeout": 1.0,       # 读取超时时间(秒)
        "write_timeout": 1.0, # 写入超时时间(秒)
        "xonxoff": False,     # 是否启用软件流控
        "rtscts": False,      # 是否启用RTS/CTS硬件流控
        "dsrdtr": False,      # 是否启用DSR/DTR硬件流控
        "buffer_size": 4096,  # 接收缓冲区大小
        "eol": b"\n",         # 行结束符
        "encoding": "utf-8",  # 字符编码
        "read_mode": "line"   # 读取模式: "line"按行读取, "raw"读取原始字节
    }
    
    def __init__(self, port: str = None):
        """
        初始化串口协议适配器
        
        Args:
            port: 串口名称，如"COM1"或"/dev/ttyUSB0"
        """
        super().__init__("串口协议")
        
        # 创建初始配置
        config = self.DEFAULT_CONFIG.copy()
        if port:
            config["port"] = port
            
        # 设置配置
        self._config = config
        
        # 创建串口对象
        self._serial = None
        
        # 创建消息队列，用于线程间通信
        self._msg_queue = queue.Queue(maxsize=100)
        
        # 创建响应字典，用于保存命令响应
        self._response_dict = {}
        self._response_lock = threading.Lock()
        
        logger.debug("串口协议适配器已初始化")
    
    @staticmethod
    def list_available_ports() -> List[Tuple[str, str, str]]:
        """
        列出系统上可用的串口
        
        Returns:
            包含 (端口名, 描述, 硬件ID) 的列表
        """
        return [(port.device, port.description, port.hwid) 
                for port in list_ports.comports()]
    
    def _validate_config(self, config: Dict) -> None:
        """
        验证串口配置
        
        Args:
            config: 要验证的配置字典
            
        Raises:
            ValueError: 如果配置无效
        """
        # 检查必需的配置项
        if "port" in config and config["port"] is None:
            raise ValueError("必须指定串口名称")
            
        # 验证波特率
        if "baudrate" in config:
            valid_baudrates = [110, 300, 600, 1200, 2400, 4800, 9600, 
                              14400, 19200, 38400, 57600, 115200, 230400, 
                              460800, 921600]
            if config["baudrate"] not in valid_baudrates:
                raise ValueError(f"无效的波特率: {config['baudrate']}")
                
        # 验证数据位
        if "bytesize" in config and config["bytesize"] not in [5, 6, 7, 8]:
            raise ValueError(f"无效的数据位: {config['bytesize']}")
            
        # 验证校验位
        if "parity" in config and config["parity"] not in ["N", "E", "O", "M", "S"]:
            raise ValueError(f"无效的校验位: {config['parity']}")
            
        # 验证停止位
        if "stopbits" in config and config["stopbits"] not in [1, 1.5, 2]:
            raise ValueError(f"无效的停止位: {config['stopbits']}")
            
        # 验证超时时间
        if "timeout" in config and (not isinstance(config["timeout"], (int, float)) or config["timeout"] < 0):
            raise ValueError(f"无效的读取超时时间: {config['timeout']}")
            
        # 验证写入超时时间
        if "write_timeout" in config and (not isinstance(config["write_timeout"], (int, float)) or config["write_timeout"] < 0):
            raise ValueError(f"无效的写入超时时间: {config['write_timeout']}")
            
        # 验证读取模式
        if "read_mode" in config and config["read_mode"] not in ["line", "raw"]:
            raise ValueError(f"无效的读取模式: {config['read_mode']}")
            
        logger.debug("串口配置验证通过")
    
    def connect(self) -> bool:
        """
        连接到串口
        
        Returns:
            连接是否成功
            
        Raises:
            ConnectionError: 连接失败时抛出
        """
        if self._is_connected:
            logger.warning("串口已经连接")
            return True
            
        try:
            # 验证配置
            self._validate_config(self._config)
            
            # 获取配置
            port = self._config["port"]
            baudrate = self._config["baudrate"]
            bytesize = self._config["bytesize"]
            parity = self._config["parity"]
            stopbits = self._config["stopbits"]
            timeout = self._config["timeout"]
            write_timeout = self._config["write_timeout"]
            xonxoff = self._config["xonxoff"]
            rtscts = self._config["rtscts"]
            dsrdtr = self._config["dsrdtr"]
            
            # 确保端口名称已指定
            if port is None:
                raise ConnectionError("未指定串口名称")
                
            # 创建串口对象
            self._serial = serial.Serial(
                port=port,
                baudrate=baudrate,
                bytesize=getattr(serial, f"EIGHTBITS" if bytesize == 8 else f"SEVENBITS" if bytesize == 7 else f"SIXBITS" if bytesize == 6 else "FIVEBITS"),
                parity=getattr(serial, f"PARITY_{'NONE' if parity == 'N' else 'EVEN' if parity == 'E' else 'ODD' if parity == 'O' else 'MARK' if parity == 'M' else 'SPACE'}"),
                stopbits=getattr(serial, f"STOPBITS_{'ONE' if stopbits == 1 else 'ONE_POINT_FIVE' if stopbits == 1.5 else 'TWO'}"),
                timeout=timeout,
                write_timeout=write_timeout,
                xonxoff=xonxoff,
                rtscts=rtscts,
                dsrdtr=dsrdtr
            )
            
            # 打开串口
            if not self._serial.is_open:
                self._serial.open()
                
            # 清空缓冲区
            self._serial.reset_input_buffer()
            self._serial.reset_output_buffer()
            
            # 更新状态
            self._is_connected = True
            
            # 在connect时不启动读取线程，只在start_reading时启动
            # self._start_read_thread()
            
            # 发送连接成功信号
            self._signals.connected.emit()
            
            logger.info(f"已连接到串口 {port}，波特率: {baudrate}")
            return True
            
        except serial.SerialException as e:
            error_msg = f"连接串口失败: {str(e)}"
            logger.error(error_msg)
            # 发送错误信号
            self._signals.error_occurred.emit(ConnectionError(error_msg))
            raise ConnectionError(error_msg) from e
    
    def disconnect(self) -> bool:
        """
        断开串口连接
        
        Returns:
            断开连接是否成功
        """
        if not self._is_connected:
            logger.warning("串口未连接")
            return True
            
        try:
            # 停止读取线程
            self._stop_read_thread()
            
            # 关闭串口
            if self._serial and self._serial.is_open:
                self._serial.close()
                
            # 更新状态
            self._is_connected = False
            
            # 发送断开连接信号
            self._signals.disconnected.emit()
            
            logger.info("已断开串口连接")
            return True
            
        except Exception as e:
            logger.error(f"断开串口连接失败: {str(e)}")
            return False
    
    def _read_loop(self) -> None:
        """
        读取循环，在单独线程中运行
        
        持续从串口读取数据，并通过信号发送到主线程
        """
        read_mode = self._config["read_mode"]
        encoding = self._config["encoding"]
        
        # 确保eol是字节类型
        eol = self._config["eol"]
        if isinstance(eol, str):
            eol = eol.encode(encoding)
        elif not isinstance(eol, bytes):
            eol = b"\n"  # 默认使用换行符
        
        logger.debug("串口读取线程已启动")
        
        while self._is_running and self._is_connected and self._serial and self._serial.is_open:
            try:
                if read_mode == "line":
                    # 按行读取
                    line = self._serial.readline()
                    if line:
                        # 解码数据
                        try:
                            # 去除行尾的换行符
                            if isinstance(line, bytes) and isinstance(eol, bytes) and line.endswith(eol):
                                line = line[:-len(eol)]
                                
                            decoded_line = line.decode(encoding).strip()
                            
                            # 尝试将字符串转换为数字
                            processed_data = decoded_line
                            try:
                                # 尝试转换为整数
                                if decoded_line.isdigit() or (decoded_line.startswith('-') and decoded_line[1:].isdigit()):
                                    processed_data = int(decoded_line)
                                # 尝试转换为浮点数
                                elif '.' in decoded_line:
                                    processed_data = float(decoded_line)
                            except ValueError:
                                # 如果转换失败，保持原字符串
                                pass
                            
                            # 发送数据接收信号
                            self._signals.data_received.emit(processed_data)
                            
                            # 检查是否是命令响应
                            self._check_response(processed_data)
                            
                        except UnicodeDecodeError as e:
                            logger.warning(f"解码数据失败: {str(e)}")
                            # 发送原始字节数据
                            self._signals.data_received.emit(line)
                else:
                    # 原始读取模式
                    if self._serial.in_waiting > 0:
                        data = self._serial.read(self._serial.in_waiting)
                        if data:
                            # 发送数据接收信号
                            self._signals.data_received.emit(data)
                            
                            # 检查是否是命令响应
                            try:
                                decoded_data = data.decode(encoding).strip()
                                # 尝试将字符串转换为数字
                                processed_data = decoded_data
                                try:
                                    # 尝试转换为整数
                                    if decoded_data.isdigit() or (decoded_data.startswith('-') and decoded_data[1:].isdigit()):
                                        processed_data = int(decoded_data)
                                    # 尝试转换为浮点数
                                    elif '.' in decoded_data:
                                        processed_data = float(decoded_data)
                                except ValueError:
                                    # 如果转换失败，保持原字符串
                                    pass
                                
                                self._check_response(processed_data)
                            except UnicodeDecodeError:
                                pass
                
                # 短暂休眠，避免CPU占用过高
                time.sleep(0.01)
                
            except serial.SerialException as e:
                logger.error(f"串口读取错误: {str(e)}")
                # 发送错误信号
                self._signals.error_occurred.emit(ConnectionError(f"串口读取错误: {str(e)}"))
                # 断开连接
                self._is_connected = False
                self._signals.disconnected.emit()
                break
                
            except Exception as e:
                logger.error(f"串口读取线程异常: {str(e)}")
                import traceback
                logger.error(traceback.format_exc())
                time.sleep(0.1)  # 避免异常导致的高CPU占用
        
        logger.debug("串口读取线程已退出")
    
    def _check_response(self, data: Any) -> None:
        """
        检查数据是否是命令响应
        
        Args:
            data: 接收到的数据，可能是字符串、数字或字节类型
        """
        with self._response_lock:
            # 添加调试日志
            logger.debug(f"收到潜在响应数据: {repr(data)} 类型: {type(data)}")
            # 检查是否有等待响应的命令
            for cmd_id, event in list(self._response_dict.items()):
                if isinstance(event, threading.Event):
                    # 设置响应数据并通知等待线程
                    logger.debug(f"找到匹配的命令ID: {cmd_id}，设置响应数据")
                    self._response_dict[cmd_id] = data
                    event.set()
                    break
    
    def read_data(self, timeout: float = 1.0) -> Any:
        """
        读取数据（同步）
        
        Args:
            timeout: 超时时间（秒）
            
        Returns:
            读取到的数据
            
        Raises:
            TimeoutError: 读取超时时抛出
            ConnectionError: 未连接或连接异常时抛出
        """
        if not self._is_connected:
            raise ConnectionError("串口未连接")
            
        if not self._serial or not self._serial.is_open:
            raise ConnectionError("串口连接异常")
            
        try:
            read_mode = self._config["read_mode"]
            eol = self._config["eol"]
            encoding = self._config["encoding"]
            
            # 保存原超时设置
            old_timeout = self._serial.timeout
            self._serial.timeout = timeout
            
            try:
                if read_mode == "line":
                    # 按行读取
                    line = self._serial.readline()
                    if not line:
                        raise TimeoutError("读取数据超时")
                        
                    # 去除行尾的换行符
                    if line.endswith(eol):
                        line = line[:-len(eol)]
                        
                    # 解码数据
                    try:
                        return line.decode(encoding)
                    except UnicodeDecodeError:
                        return line
                else:
                    # 原始读取模式
                    if self._serial.in_waiting > 0:
                        data = self._serial.read(self._serial.in_waiting)
                        return data
                    else:
                        # 最少读取一个字节
                        data = self._serial.read(1)
                        if not data:
                            raise TimeoutError("读取数据超时")
                        return data
            finally:
                # 恢复原超时设置
                self._serial.timeout = old_timeout
                
        except serial.SerialException as e:
            logger.error(f"读取数据失败: {str(e)}")
            raise ConnectionError(f"读取数据失败: {str(e)}") from e
    
    def send_command(self, command: Union[str, bytes], wait_response: bool = False, 
                    timeout: float = 1.0) -> Optional[Any]:
        """
        发送命令
        
        Args:
            command: 要发送的命令，字符串或字节
            wait_response: 是否等待响应
            timeout: 等待响应的超时时间（秒）
            
        Returns:
            如果wait_response为True，返回响应数据；否则返回None
            
        Raises:
            CommandError: 命令发送失败时抛出
            TimeoutError: 等待响应超时时抛出
            ConnectionError: 未连接或连接异常时抛出
        """
        if not self._is_connected:
            raise ConnectionError("串口未连接")
            
        if not self._serial or not self._serial.is_open:
            raise ConnectionError("串口连接异常")
            
        try:
            encoding = self._config["encoding"]
            eol = self._config["eol"]
            
            # 将命令转换为字节
            if isinstance(command, str):
                if not command.endswith("\n") and eol == b"\n":
                    command += "\n"
                command_bytes = command.encode(encoding)
            else:
                command_bytes = command
                
            # 生成命令ID
            cmd_id = id(command_bytes)
            response_event = None
            
            # 如果需要等待响应，创建一个事件
            if wait_response:
                response_event = threading.Event()
                with self._response_lock:
                    self._response_dict[cmd_id] = response_event
            
            # 发送命令
            bytes_written = self._serial.write(command_bytes)
            
            if bytes_written != len(command_bytes):
                raise CommandError(f"命令发送不完整: 已发送 {bytes_written} 字节，共 {len(command_bytes)} 字节")
                
            # 发送命令已发送事件
            self._handle_event(ProtocolEventType.COMMAND_SENT, command)
            
            # 等待响应
            if wait_response:
                if not response_event.wait(timeout):
                    # 超时，移除响应事件
                    with self._response_lock:
                        self._response_dict.pop(cmd_id, None)
                    raise TimeoutError("等待响应超时")
                    
                # 获取响应数据
                with self._response_lock:
                    response = self._response_dict.pop(cmd_id, None)
                    
                return response
            
            return None
            
        except serial.SerialException as e:
            logger.error(f"发送命令失败: {str(e)}")
            raise CommandError(f"发送命令失败: {str(e)}") from e
    
    def clear_buffer(self) -> None:
        """
        清空串口接收缓冲区
        """
        if self._serial and self._serial.is_open:
            self._serial.reset_input_buffer()  # 清空输入缓冲区
            logger.info("串口接收缓冲区已清空") 