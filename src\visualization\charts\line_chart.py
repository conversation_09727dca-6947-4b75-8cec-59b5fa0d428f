#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
折线图模块

实现基于PyQtGraph的折线图，支持多条线、动态数据更新等功能。
"""

import numpy as np
from typing import Dict, List, Optional, Tuple, Union, Any, Type

try:
    import pyqtgraph as pg
    from pyqtgraph.Qt import QtCore, QtGui
except ImportError:
    # 如果导入失败，可能会在运行时另行处理
    pass

from ..base_chart import BaseChart, ChartType
from ...utils.logger import logger


class LineChart(BaseChart):
    """
    折线图类
    
    实现折线图的创建、数据管理和样式设置等功能。
    """
    
    def __init__(
        self, 
        title: str = "折线图", 
        x_label: str = "X轴", 
        y_label: str = "Y轴"
    ):
        """
        初始化折线图
        
        Args:
            title: 图表标题
            x_label: X轴标签
            y_label: Y轴标签
        """
        super().__init__(title, x_label, y_label, ChartType.LINE)
        
        # 折线图特有的属性
        self._line_styles = {}  # 存储每条线的样式
        self._symbol_styles = {}  # 存储每条线的点标记样式
        self._is_stepped = {}  # 存储每条线是否使用阶梯样式
        self._line_width = {}  # 存储每条线的宽度
    
    def _initialize_specific(self):
        """实现图表特定的初始化"""
        # 折线图特有的初始化操作（如果有）
        pass
    
    def add_data(
        self, 
        data_id: str, 
        data: Union[Tuple[List, List], np.ndarray], 
        **kwargs
    ):
        """
        添加数据到折线图
        
        Args:
            data_id: 数据标识符
            data: 数据内容，可以是 (x, y) 元组或二维数组
            **kwargs: 额外配置参数，包括：
                - name: 线条名称（显示在图例中）
                - color: 线条颜色，格式为 (R,G,B) 或 '#RRGGBB'
                - style: 线条样式，如 Qt.SolidLine, Qt.DashLine 等
                - symbol: 点标记样式，如 'o', 't', '+', None 等
                - symbol_size: 点标记大小
                - symbol_brush: 点标记填充颜色
                - stepped: 是否使用阶梯样式
                - line_width: 线条宽度
        """
        if not self._initialized:
            self.initialize()
        
        try:
            # 解析数据
            x_data, y_data = self._parse_data(data)
            
            # 解析样式参数
            name = kwargs.get('name', data_id)
            color = kwargs.get('color', None)
            style = kwargs.get('style', None)
            symbol = kwargs.get('symbol', None)
            symbol_size = kwargs.get('symbol_size', 10)
            symbol_brush = kwargs.get('symbol_brush', color)
            stepped = kwargs.get('stepped', False)
            line_width = kwargs.get('line_width', 1)
            
            # 如果没有指定颜色，使用自动生成的颜色
            if color is None:
                # 使用预定义的颜色列表
                colors = [
                    (255, 0, 0),      # 红色
                    (0, 0, 255),      # 蓝色
                    (0, 255, 0),      # 绿色
                    (255, 165, 0),    # 橙色
                    (128, 0, 128),    # 紫色
                    (255, 192, 203),  # 粉红色
                    (165, 42, 42),    # 棕色
                    (0, 255, 255),    # 青色
                ]
                color = colors[len(self._data_items) % len(colors)]
            
            # 创建折线数据项
            pen = pg.mkPen(color=color, width=line_width)
            if style is not None:
                pen.setStyle(style)
            
            # 创建点标记配置
            symbol_opts = {}
            if symbol is not None:
                symbol_opts['symbol'] = symbol
                symbol_opts['symbolSize'] = symbol_size
                if symbol_brush is not None:
                    symbol_opts['symbolBrush'] = symbol_brush
            
            # 使用阶梯样式
            if stepped:
                connect = 'finite'  # 允许断点
                # 将数据转换为阶梯形式
                x_stepped = []
                y_stepped = []
                for i in range(len(x_data) - 1):
                    x_stepped.extend([x_data[i], x_data[i+1]])
                    y_stepped.extend([y_data[i], y_data[i]])
                x_data = x_stepped
                y_data = y_stepped
            else:
                connect = 'finite'  # 允许断点
            
            # 创建折线项
            plot_item = self._plot_item.plot(
                x=x_data, 
                y=y_data, 
                pen=pen, 
                name=name,
                connect=connect,
                **symbol_opts
            )
            
            # 存储数据项和样式信息
            self._data_items[data_id] = plot_item
            self._line_styles[data_id] = style
            self._symbol_styles[data_id] = symbol
            self._is_stepped[data_id] = stepped
            self._line_width[data_id] = line_width
            
            logger.debug(f"{self._log_prefix} 添加折线: {data_id}, 数据点数: {len(x_data)}")
            
        except Exception as e:
            logger.error(f"{self._log_prefix} 添加数据失败: {str(e)}")
            raise
    
    def update_data(
        self, 
        data_id: str, 
        data: Union[Tuple[List, List], np.ndarray], 
        **kwargs
    ):
        """
        更新折线图中的数据
        
        Args:
            data_id: 数据标识符
            data: 新的数据内容
            **kwargs: 额外的样式参数，与add_data相同
        """
        if not self._initialized or data_id not in self._data_items:
            if data_id not in self._data_items:
                # 如果数据不存在，则添加新数据
                self.add_data(data_id, data, **kwargs)
            return
        
        try:
            # 解析数据
            x_data, y_data = self._parse_data(data)
            
            # 获取线条项
            line_item = self._data_items[data_id]
            
            # 检查是否更新样式
            if 'color' in kwargs or 'style' in kwargs or 'line_width' in kwargs:
                color = kwargs.get('color', line_item.opts['pen'].color())
                style = kwargs.get('style', self._line_styles.get(data_id))
                line_width = kwargs.get('line_width', self._line_width.get(data_id, 1))
                
                pen = pg.mkPen(color=color, width=line_width)
                if style is not None:
                    pen.setStyle(style)
                line_item.setPen(pen)
                
                self._line_styles[data_id] = style
                self._line_width[data_id] = line_width
            
            # 检查是否更新点标记样式
            if 'symbol' in kwargs or 'symbol_size' in kwargs or 'symbol_brush' in kwargs:
                symbol = kwargs.get('symbol', self._symbol_styles.get(data_id))
                symbol_size = kwargs.get('symbol_size', line_item.opts.get('symbolSize', 10))
                symbol_brush = kwargs.get('symbol_brush', line_item.opts.get('symbolBrush'))
                
                line_item.setSymbol(symbol)
                line_item.setSymbolSize(symbol_size)
                if symbol_brush is not None:
                    line_item.setSymbolBrush(symbol_brush)
                
                self._symbol_styles[data_id] = symbol
            
            # 检查是否需要转换为阶梯样式
            stepped = kwargs.get('stepped', self._is_stepped.get(data_id, False))
            if stepped != self._is_stepped.get(data_id, False):
                self._is_stepped[data_id] = stepped
                # 重新添加数据以应用阶梯样式
                self.clear_data(data_id)
                self.add_data(data_id, data, **kwargs)
                return
            
            # 更新数据
            line_item.setData(x_data, y_data)
            
            logger.debug(f"{self._log_prefix} 更新折线: {data_id}, 数据点数: {len(x_data)}")
            
        except Exception as e:
            logger.error(f"{self._log_prefix} 更新数据失败: {str(e)}")
            raise
    
    def _parse_data(self, data) -> Tuple[List, List]:
        """
        解析数据格式
        
        Args:
            data: 输入数据，可以是 (x, y) 元组或二维数组
            
        Returns:
            Tuple[List, List]: x和y数据列表
        """
        if isinstance(data, tuple) and len(data) == 2:
            # 如果是 (x, y) 元组
            x_data, y_data = data
            
            # 确保是列表类型
            if isinstance(x_data, np.ndarray):
                x_data = x_data.tolist()
            if isinstance(y_data, np.ndarray):
                y_data = y_data.tolist()
                
            return x_data, y_data
            
        elif isinstance(data, np.ndarray):
            # 如果是二维数组
            if data.ndim == 2 and data.shape[1] == 2:
                # 如果是 [[x1, y1], [x2, y2], ...] 格式
                return data[:, 0].tolist(), data[:, 1].tolist()
            elif data.ndim == 1:
                # 如果是一维数组，使用索引作为X轴
                return list(range(len(data))), data.tolist()
        
        # 其他情况，尝试转换为列表
        if hasattr(data, '__iter__'):
            return list(range(len(data))), list(data)
        
        raise ValueError(f"不支持的数据格式: {type(data)}")
    
    def set_x_range(self, min_val: float, max_val: float):
        """
        设置X轴范围
        
        Args:
            min_val: 最小值
            max_val: 最大值
        """
        if not self._initialized:
            return
        
        self._plot_item.setXRange(min_val, max_val)
        self._auto_range = False
    
    def set_y_range(self, min_val: float, max_val: float):
        """
        设置Y轴范围
        
        Args:
            min_val: 最小值
            max_val: 最大值
        """
        if not self._initialized:
            return
        
        self._plot_item.setYRange(min_val, max_val)
        self._auto_range = False
