{"app": {"name": "Data-Show", "version": "0.1.0", "theme": "light", "language": "zh_CN", "log_level": "INFO"}, "ui": {"window_width": 1280, "window_height": 800, "font_size": 12, "show_toolbar": true, "show_statusbar": true}, "visualization": {"default_chart_type": "line", "update_interval": 100, "max_data_points": 1000}, "protocol": {"serial": {"baudrate": 9600, "bytesize": 8, "parity": "N", "stopbits": 1, "timeout": 1}, "modbus": {"timeout": 3, "retries": 3}, "tcp_ip": {"buffer_size": 1024, "timeout": 5}, "mqtt": {"keep_alive": 60}}, "data": {"storage_type": "sqlite", "db_path": "./data/data.db", "auto_save": true, "save_interval": 60}}