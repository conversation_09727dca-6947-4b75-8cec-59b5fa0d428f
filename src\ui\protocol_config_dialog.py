#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
硬件协议配置对话框

提供硬件协议配置界面，支持多种协议的配置管理。
"""

import json
import logging
import os
from typing import Any, Dict, List, Optional, Type

try:
    from PyQt5.QtWidgets import (
        QDialog, QVBoxLayout, QHBoxLayout, QListWidget, QListWidgetItem,
        QStackedWidget, QLabel, QPushButton, QDialogButtonBox, QMessageBox,
        QWidget, QSplitter
    )
    from PyQt5.QtCore import Qt, QSize, pyqtSlot as Slot
    from PyQt5.QtGui import QIcon
except ImportError:
    try:
        from PySide6.QtWidgets import (
            QDialog, QVBoxLayout, QHBoxLayout, QListWidget, QListWidgetItem,
            QStackedWidget, QLabel, QPushButton, QDialog<PERSON>uttonBox, Q<PERSON>essageBox,
            QWidget, QSplitter
        )
        from PySide6.QtCore import Qt, QSize, Slot
        from PySide6.QtGui import QIcon
    except ImportError:
        raise ImportError("未安装PyQt5或PySide6，请安装其中之一")

from src.ui.config_panels.protocol_config_base import ProtocolConfigPanelBase
from src.ui.config_panels.serial_config_panel import SerialConfigPanel
from src.core.config import Config

# 获取logger
logger = logging.getLogger(__name__)

class ProtocolConfigDialog(QDialog):
    """
    硬件协议配置对话框
    
    提供界面用于配置不同的硬件通信协议。
    """
    
    def __init__(self, parent=None):
        """初始化配置对话框"""
        super().__init__(parent)
        
        # 设置窗口属性
        self.setWindowTitle("硬件协议配置")
        self.setMinimumSize(800, 500)
        
        # 协议配置面板字典
        self._panel_classes = {}
        self._panels = {}
        self._configs = {}
        
        # 初始化UI
        self._init_ui()
        
        # 绑定事件
        self._bind_events()
        
        # 注册默认协议
        self._register_default_protocols()
        
        # 加载配置
        self._load_configs()
        
    def _init_ui(self) -> None:
        """初始化UI"""
        # 创建主布局
        self.main_layout = QVBoxLayout()
        self.setLayout(self.main_layout)
        
        # 创建分割器
        self.splitter = QSplitter(Qt.Horizontal)
        
        # 创建协议列表
        self.protocol_list = QListWidget()
        self.protocol_list.setMinimumWidth(180)
        self.protocol_list.setMaximumWidth(250)
        
        # 创建配置区域
        self.config_stack = QStackedWidget()
        
        # 默认空白页面
        empty_widget = QWidget()
        empty_layout = QVBoxLayout()
        empty_layout.addWidget(QLabel("请选择一个协议进行配置"))
        empty_widget.setLayout(empty_layout)
        self.config_stack.addWidget(empty_widget)
        
        # 添加到分割器
        self.splitter.addWidget(self.protocol_list)
        self.splitter.addWidget(self.config_stack)
        
        # 设置分割器初始大小
        self.splitter.setSizes([200, 600])
        
        # 添加到主布局
        self.main_layout.addWidget(self.splitter)
        
        # 添加按钮
        self.button_box = QDialogButtonBox(
            QDialogButtonBox.Ok | QDialogButtonBox.Cancel | QDialogButtonBox.Apply | QDialogButtonBox.Reset
        )
        self.main_layout.addWidget(self.button_box)
    
    def _bind_events(self) -> None:
        """绑定事件"""
        # 协议列表选择事件
        self.protocol_list.currentRowChanged.connect(self._on_protocol_selected)
        
        # 按钮事件
        self.button_box.accepted.connect(self.accept)
        self.button_box.rejected.connect(self.reject)
        self.button_box.button(QDialogButtonBox.Apply).clicked.connect(self._on_apply_clicked)
        self.button_box.button(QDialogButtonBox.Reset).clicked.connect(self._on_reset_clicked)
    
    def _register_protocol(self, panel_class: Type[ProtocolConfigPanelBase]) -> None:
        """
        注册协议配置面板
        
        Args:
            panel_class: 协议配置面板类
        """
        # 实例化面板
        panel = panel_class(self)
        
        # 存储面板
        protocol_name = panel.protocol_name
        self._panel_classes[protocol_name] = panel_class
        self._panels[protocol_name] = panel
        
        # 添加到堆栈窗口
        self.config_stack.addWidget(panel)
        
        # 添加到协议列表
        item = QListWidgetItem(protocol_name)
        item.setData(Qt.UserRole, protocol_name)
        self.protocol_list.addItem(item)
        
        logger.debug(f"已注册协议: {protocol_name}")
    
    def _register_default_protocols(self) -> None:
        """注册默认支持的协议"""
        # 注册串口协议
        self._register_protocol(SerialConfigPanel)
    
    def _on_protocol_selected(self, row: int) -> None:
        """
        协议选择事件处理函数
        
        Args:
            row: 选中的行索引
        """
        if row < 0:
            # 没有选中协议
            self.config_stack.setCurrentIndex(0)
            return
            
        # 获取协议名称
        item = self.protocol_list.item(row)
        protocol_name = item.data(Qt.UserRole)
        
        # 找到对应的面板
        panel = self._panels.get(protocol_name)
        if panel:
            # 切换到对应的面板
            self.config_stack.setCurrentWidget(panel)
            
            logger.debug(f"已切换到协议: {protocol_name}")
    
    def _on_apply_clicked(self) -> None:
        """应用按钮点击事件"""
        # 保存当前配置
        self._save_configs()
        
        logger.info("已应用协议配置")
    
    def _on_reset_clicked(self) -> None:
        """重置按钮点击事件"""
        # 获取当前选中的协议
        current_item = self.protocol_list.currentItem()
        if not current_item:
            return
            
        protocol_name = current_item.data(Qt.UserRole)
        panel = self._panels.get(protocol_name)
        
        if panel:
            # 重置当前面板
            panel.load_defaults()
            logger.debug(f"已重置协议配置: {protocol_name}")
    
    def accept(self) -> None:
        """对话框接受"""
        # 验证所有配置
        if not self._validate_configs():
            # 配置无效，不关闭对话框
            return
            
        # 保存配置
        self._save_configs()
        
        # 接受对话框
        super().accept()
    
    def _validate_configs(self) -> bool:
        """
        验证所有配置
        
        Returns:
            配置是否有效
        """
        # 获取当前选中的协议
        current_item = self.protocol_list.currentItem()
        if not current_item:
            return True
            
        protocol_name = current_item.data(Qt.UserRole)
        panel = self._panels.get(protocol_name)
        
        if panel:
            # 验证当前面板
            if not panel.validate_config():
                # 显示错误消息
                QMessageBox.warning(
                    self,
                    "配置错误",
                    f"{protocol_name} 配置无效，请检查输入。"
                )
                return False
                
        return True
    
    def _load_configs(self) -> None:
        """加载所有协议配置"""
        try:
            # 获取全局配置
            config = Config.get_instance()
            protocol_configs = config.get("hardware.protocols", {})
            
            # 处理bytes类型的恢复
            for protocol_name, protocol_config in protocol_configs.items():
                if isinstance(protocol_config, dict):
                    for key, value in list(protocol_config.items()):
                        if isinstance(value, str) and value.startswith("__bytes__"):
                            # 从特殊格式恢复bytes对象
                            try:
                                hex_value = value[9:]  # 移除 "__bytes__" 前缀
                                protocol_config[key] = bytes.fromhex(hex_value)
                            except Exception as e:
                                logger.warning(f"恢复bytes类型失败: {str(e)}")
            
            # 遍历所有协议面板
            for protocol_name, panel in self._panels.items():
                # 获取协议配置
                protocol_config = protocol_configs.get(protocol_name)
                
                if protocol_config:
                    # 设置面板配置
                    panel.set_config(protocol_config)
                    self._configs[protocol_name] = protocol_config.copy()
                    logger.debug(f"加载协议配置: {protocol_name}")
                else:
                    # 使用默认配置
                    panel.load_defaults()
                    self._configs[protocol_name] = panel.get_config()
                    logger.debug(f"使用默认配置: {protocol_name}")
                    
        except Exception as e:
            logger.error(f"加载协议配置失败: {str(e)}")
            
            # 使用默认配置
            for protocol_name, panel in self._panels.items():
                panel.load_defaults()
                self._configs[protocol_name] = panel.get_config()
    
    def _save_configs(self) -> None:
        """保存所有协议配置"""
        try:
            # 更新所有配置
            for protocol_name, panel in self._panels.items():
                self._configs[protocol_name] = panel.get_config()
                
            # 获取全局配置
            config = Config.get_instance()
            
            # 更新协议配置
            config.set("hardware.protocols", self._configs)
            
            # 保存配置
            config.save()
            
            logger.info("保存协议配置成功")
            
        except Exception as e:
            logger.error(f"保存协议配置失败: {str(e)}")
            QMessageBox.warning(
                self,
                "保存失败",
                f"无法保存协议配置: {str(e)}"
            )
    
    def get_protocol_config(self, protocol_name: str) -> Dict[str, Any]:
        """
        获取指定协议的配置
        
        Args:
            protocol_name: 协议名称
            
        Returns:
            协议配置字典，如果不存在则返回空字典
        """
        return self._configs.get(protocol_name, {}) 