#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
事件总线模块

该模块实现了一个简单的事件总线，用于在不同模块间进行消息传递。
采用发布/订阅模式，允许组件注册事件处理器并发布事件。
"""

import asyncio
import functools
import inspect
import logging
import threading
import traceback
import uuid
from typing import Any, Callable, Dict, List, Optional, Set, Tuple, Union

class EventBus:
    """事件总线类，使用单例模式确保全局只有一个事件总线实例"""
    
    _instance = None
    
    def __new__(cls):
        """实现单例模式"""
        if cls._instance is None:
            cls._instance = super(EventBus, cls).__new__(cls)
            cls._instance._initialized = False
        return cls._instance
    
    def __init__(self):
        """初始化事件总线"""
        if self._initialized:
            return
            
        self._initialized = True
        self._handlers: Dict[str, List[Tuple[Callable, bool]]] = {}  # 事件处理器字典 {事件名: [(处理函数, 是否异步), ...]}
        self._handler_ids: Dict[str, List[str]] = {}  # 处理器ID字典 {事件名: [处理器ID, ...]}
        self._lock = threading.RLock()  # 用于保护处理器字典的线程锁
        self._loop = None  # 异步事件循环
        
        self._logger = logging.getLogger(__name__)
    
    def register(self, event_name: str, handler: Callable, is_async: bool = False) -> str:
        """
        注册事件处理器
        
        Args:
            event_name: 事件名称
            handler: 事件处理函数
            is_async: 是否为异步处理函数
            
        Returns:
            处理器ID，可用于注销处理器
        """
        handler_id = str(uuid.uuid4())
        
        with self._lock:
            if event_name not in self._handlers:
                self._handlers[event_name] = []
                self._handler_ids[event_name] = []
                
            self._handlers[event_name].append((handler, is_async))
            self._handler_ids[event_name].append(handler_id)
            
        self._logger.debug(f"注册事件处理器: {event_name}, ID: {handler_id}, 异步: {is_async}")
        return handler_id
    
    def unregister(self, event_name: str, handler_id: str) -> bool:
        """
        注销事件处理器
        
        Args:
            event_name: 事件名称
            handler_id: 处理器ID
            
        Returns:
            是否成功注销
        """
        with self._lock:
            if event_name not in self._handlers or event_name not in self._handler_ids:
                return False
                
            if handler_id not in self._handler_ids[event_name]:
                return False
                
            index = self._handler_ids[event_name].index(handler_id)
            self._handlers[event_name].pop(index)
            self._handler_ids[event_name].pop(index)
            
            # 如果没有处理器，则移除事件
            if not self._handlers[event_name]:
                del self._handlers[event_name]
                del self._handler_ids[event_name]
                
        self._logger.debug(f"注销事件处理器: {event_name}, ID: {handler_id}")
        return True
    
    def unregister_all(self, event_name: Optional[str] = None) -> None:
        """
        注销所有事件处理器
        
        Args:
            event_name: 事件名称，如果为None则注销所有事件的所有处理器
        """
        with self._lock:
            if event_name is None:
                self._handlers.clear()
                self._handler_ids.clear()
                self._logger.debug("注销所有事件处理器")
            elif event_name in self._handlers:
                del self._handlers[event_name]
                del self._handler_ids[event_name]
                self._logger.debug(f"注销事件 {event_name} 的所有处理器")
    
    def publish(self, event_name: str, *args, **kwargs) -> None:
        """
        发布事件（同步）
        
        Args:
            event_name: 事件名称
            *args, **kwargs: 传递给处理函数的参数
        """
        handlers = []
        
        with self._lock:
            if event_name in self._handlers:
                handlers = self._handlers[event_name].copy()
        
        if not handlers:
            self._logger.debug(f"没有处理器注册事件: {event_name}")
            return
            
        for handler, is_async in handlers:
            try:
                if is_async:
                    # 如果是异步处理器，创建任务但不等待
                    self._ensure_loop()
                    asyncio.run_coroutine_threadsafe(handler(*args, **kwargs), self._loop)
                else:
                    # 同步调用处理器
                    handler(*args, **kwargs)
            except Exception as e:
                self._logger.error(f"事件处理器 {handler.__name__} 执行失败: {str(e)}")
                self._logger.debug(traceback.format_exc())
    
    async def publish_async(self, event_name: str, *args, **kwargs) -> None:
        """
        发布事件（异步）
        
        Args:
            event_name: 事件名称
            *args, **kwargs: 传递给处理函数的参数
        """
        handlers = []
        
        with self._lock:
            if event_name in self._handlers:
                handlers = self._handlers[event_name].copy()
        
        if not handlers:
            self._logger.debug(f"没有处理器注册事件: {event_name}")
            return
            
        for handler, is_async in handlers:
            try:
                if is_async:
                    # 异步等待异步处理器
                    await handler(*args, **kwargs)
                else:
                    # 使用线程池运行同步处理器
                    loop = asyncio.get_event_loop()
                    await loop.run_in_executor(None, lambda: handler(*args, **kwargs))
            except Exception as e:
                self._logger.error(f"事件处理器 {handler.__name__} 执行失败: {str(e)}")
                self._logger.debug(traceback.format_exc())
    
    def _ensure_loop(self) -> None:
        """确保有一个可用的事件循环"""
        if self._loop is None or self._loop.is_closed():
            try:
                self._loop = asyncio.get_event_loop()
            except RuntimeError:
                # 如果在非主线程中，创建新的事件循环
                self._loop = asyncio.new_event_loop()
                asyncio.set_event_loop(self._loop)
    
    def has_subscribers(self, event_name: str) -> bool:
        """
        检查事件是否有订阅者
        
        Args:
            event_name: 事件名称
            
        Returns:
            是否有订阅者
        """
        with self._lock:
            return event_name in self._handlers and len(self._handlers[event_name]) > 0
    
    def list_events(self) -> List[str]:
        """
        获取所有已注册的事件名称
        
        Returns:
            事件名称列表
        """
        with self._lock:
            return list(self._handlers.keys())
    
    def subscriber(self, event_name: str, is_async: bool = False):
        """
        事件订阅装饰器
        
        Args:
            event_name: 事件名称
            is_async: 是否为异步处理函数
            
        Returns:
            装饰器函数
        """
        def decorator(func):
            self.register(event_name, func, is_async)
            return func
        return decorator


# 创建全局事件总线实例
event_bus = EventBus()

# 导出装饰器函数，方便使用
def on_event(event_name: str, is_async: bool = False):
    """
    事件订阅装饰器
    
    Args:
        event_name: 事件名称
        is_async: 是否为异步处理函数
        
    Returns:
        装饰器函数
    """
    return event_bus.subscriber(event_name, is_async)


# 常用事件名称定义
class AppEvents:
    """应用程序事件定义"""
    STARTUP = "app.startup"
    SHUTDOWN = "app.shutdown"
    EXCEPTION = "app.exception"
    CONFIG_CHANGED = "app.config_changed"
    
class UIEvents:
    """用户界面事件定义"""
    WINDOW_CREATED = "ui.window_created"
    WINDOW_CLOSED = "ui.window_closed"
    
class ProtocolEvents:
    """协议事件定义"""
    CONNECTED = "protocol.connected"
    DISCONNECTED = "protocol.disconnected"
    DATA_RECEIVED = "protocol.data_received"
    ERROR = "protocol.error"
    
class DataEvents:
    """数据事件定义"""
    DATA_UPDATED = "data.updated"
    DATA_SAVED = "data.saved"
    DATA_EXPORTED = "data.exported"
    
class ChartEvents:
    """图表事件定义"""
    CREATED = "chart.created"
    UPDATED = "chart.updated"
    REMOVED = "chart.removed"


