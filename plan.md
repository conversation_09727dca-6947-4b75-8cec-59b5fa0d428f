# Data-Show 项目开发规划

## 项目概述

Data-Show是一款基于Python和Qt开发的数据可视化软件，旨在提供多种类型的数据可视化功能，并支持多种硬件接口协议。软件采用模块化设计，主要包括UI界面、数据可视化、硬件接口、数据处理、日志系统等核心模块。

## 项目架构

```
data-show/
├── main.py                 # 应用程序入口
├── src/
│   ├── core/               # 核心模块
│   │   ├── app.py          # 应用程序类
│   │   ├── config.py       # 配置管理
│   │   ├── event_bus.py    # 事件总线
│   │   └── thread_manager.py # 线程管理
│   ├── ui/                 # 用户界面
│   │   ├── main_window.ui  # 主窗口UI定义
│   │   └── main_window_controller.py # 主窗口控制器
│   ├── visualization/      # 数据可视化
│   │   ├── chart_initializer.py # 图表初始化器
│   │   ├── chart_manager.py # 图表管理器
│   │   ├── base_chart.py   # 图表基类
│   │   └── charts/         # 各类图表
│   │       ├── time_series.py # 时序图
│   │       ├── line_chart.py # 折线图
│   │       ├── scatter.py  # 散点图
│   │       ├── histogram.py # 柱状图
│   │       ├── heatmap.py  # 热力图
│   │       ├── pie_chart.py # 饼图
│   │       ├── box_plot.py # 箱线图
│   │       └── radar.py    # 雷达图
│   ├── hardware/           # 硬件接口
│   │   ├── protocols/      # 协议实现
│   │   │   ├── serial_protocol.py # 串口协议
│   │   │   ├── modbus_protocol.py # Modbus协议
│   │   │   ├── tcp_protocol.py # TCP/IP协议
│   │   │   ├── mqtt_protocol.py # MQTT协议
│   │   │   ├── can_protocol.py # CAN总线协议
│   │   │   ├── opcua_protocol.py # OPC UA协议
│   │   │   ├── i2c_protocol.py # I2C协议
│   │   │   └── spi_protocol.py # SPI协议
│   │   ├── device_factory.py # 设备工厂
│   │   ├── device_manager.py # 设备管理器
│   │   └── protocol_base.py # 协议基类
│   ├── data/               # 数据处理
│   │   ├── data_processor.py # 数据处理器
│   │   ├── data_buffer.py  # 数据缓冲区
│   │   ├── analyzer.py     # 数据分析器
│   │   ├── cleaner.py      # 数据清洗器
│   │   ├── storage/        # 数据存储
│   │   └── exporters/      # 数据导出工具
│   └── utils/              # 工具类
│       ├── logger.py       # 日志工具
│       └── log_manager.py  # 日志管理器
└── resources/              # 资源文件
    └── images/             # 图像资源
```

## 已完成任务

### UI界面 ✅
- [x] 设计并实现基本UI框架
- [x] 添加垂直分割器实现主体区域与日志区域大小调整
- [x] 实现右侧区域采用stacked布局
- [x] 添加底部日志显示区域
- [x] 添加数据分析区域的按钮工具栏（添加、删除、导出、设置、重置视图）

### 数据可视化 ⚠️
- [x] 实现图表初始化器
- [x] 实现基础图表类 (base_chart.py)
- [x] 实现图表管理器 (chart_manager.py)
- [x] 实现时序图组件
- [x] 实现折线图组件
- [x] 实现饼图组件 (修复显示错误)
- [x] 实现箱线图组件 (修复显示错误)
- [x] 创建散点图组件文件结构
- [x] 创建柱状图组件文件结构
- [x] 创建热力图组件文件结构
- [x] 创建雷达图组件文件结构
- [ ] 完善散点图功能和交互
- [ ] 完善柱状图功能和交互
- [ ] 完善热力图功能和交互
- [ ] 完善雷达图功能和交互
- [ ] 实现图表数据实时更新功能
- [ ] 实现多数据源叠加显示功能

### 日志系统 ✅
- [x] 实现基础日志记录功能
- [x] 实现日志文件输出
- [x] 实现日志UI显示集成
- [x] 实现彩色日志显示（根据日志级别）
- [x] 实现日志级别设置功能
- [x] 实现日志保存功能
- [x] 添加日志区域右键菜单

### 事件处理 ⚠️
- [x] 实现主窗口控制器
- [x] 实现数据分析区域按钮事件处理
- [x] 实现日志区域事件处理
- [ ] 实现数据采集开始/停止功能
- [ ] 实现设备连接/断开功能
- [ ] 实现图表与数据源绑定机制

### 硬件协议支持 🔄
- [ ] 设计并实现协议接口基类 (protocol_base.py)
- [ ] 实现设备工厂模式 (device_factory.py)
- [ ] 实现串口通信
- [ ] 实现Modbus协议支持
- [ ] 实现TCP/IP通信
- [ ] 实现MQTT协议支持
- [ ] 实现CAN总线支持
- [ ] 实现OPC UA协议支持
- [ ] 实现I2C通信
- [ ] 实现SPI通信

### 数据处理 🔄
- [ ] 实现数据缓冲区 (用于实时数据处理)
- [ ] 实现数据解析器
- [ ] 实现数据过滤器
- [ ] 实现简单统计分析功能
- [ ] 实现数据持久化
- [ ] 实现数据导出功能

### 配置系统 🔄
- [ ] 实现应用程序配置管理
- [ ] 实现用户偏好设置
- [ ] 实现配置文件导入/导出
- [ ] 实现工作区布局保存/恢复

## 架构优化计划

### 插件系统设计
- [ ] 设计插件管理器 (plugin_manager.py)
- [ ] 实现插件加载、卸载机制
- [ ] 定义标准插件接口
- [ ] 为硬件协议实现插件化
- [ ] 为图表类型实现插件化
- [ ] 提供插件开发示例和文档

### 配置系统优化
- [ ] 设计集中式配置管理
- [ ] 实现配置自动保存和加载
- [ ] 添加配置UI界面
- [ ] 实现配置项验证机制

### 性能优化
- [ ] 实现数据处理的多线程支持
- [ ] 优化大数据量图表渲染
- [ ] 实现数据缓存机制
- [ ] 添加性能监控工具

## 进行中任务

1. **图表功能完善**
   - 完成各类图表类型基础功能（散点图、柱状图、热力图、雷达图）
   - 实现图表交互功能（缩放、平移、选择）
   - 优化图表性能
   - 实现图表与数据源的绑定

2. **硬件协议框架搭建**
   - 设计通用协议接口
   - 实现设备管理器
   - 优先开发串口和Modbus协议
   
3. **数据处理基础框架**
   - 实现数据缓冲区设计
   - 设计数据处理管道
   - 开发基础数据分析功能

## 待开发任务

1. **硬件协议实现**
   - 完成各种协议的具体实现
   - 添加设备自动发现功能
   - 实现设备配置管理
   - 开发协议调试工具

2. **数据分析功能**
   - 增强数据过滤和预处理能力
   - 实现数据导出到多种格式
   - 添加批量数据处理功能
   - 实现简单的数据挖掘功能

3. **用户体验改进**
   - 添加浅色/深色主题切换
   - 优化UI响应速度
   - 添加国际化支持
   - 优化数据导入导出流程

## 开发优先级

### 高优先级
- 完善已有图表类型的功能实现
- 设计并实现协议基类和设备管理框架
- 实现串口通信基础功能
- 开发数据缓冲区和基础数据处理功能

### 中优先级
- 实现Modbus协议支持
- 开发TCP/IP和MQTT协议
- 完善配置管理系统
- 实现数据分析基本功能
- 优化图表交互体验

### 低优先级
- 实现更高级的图表交互功能
- 开发I2C和SPI等低层协议
- 添加用户自定义脚本功能
- 实现完整的插件系统
- 开发云连接功能

## 时间规划

### 短期目标（1个月内）
- 完善所有基本图表类型的核心功能
- 设计并实现协议接口基类
- 实现串口通信基础功能
- 开发数据缓冲区和简单数据处理模块

### 中期目标（1-3个月）
- 完成Modbus和TCP/IP协议支持
- 实现设备管理器和设备工厂
- 开发数据持久化和导出功能
- 完善图表与数据源的绑定机制
- 实现简单配置系统

### 长期目标（3-6个月）
- 完成所有计划的硬件协议支持
- 实现完整的数据分析功能
- 开发插件系统框架
- 优化性能和用户体验
- 添加高级功能（如自定义脚本、云连接等）

## 下一步开发建议

基于当前项目状态，以下是推荐的近期开发任务，按优先级排序：

### 立即开发任务（1-2周）

1. **完善现有图表组件**
   - 对已有图表文件（scatter.py, histogram.py, heatmap.py, radar.py）进行功能完善
   - 实现数据更新和交互机制
   - 添加缩放、平移等基础交互功能
   - 测试各图表类型在不同数据量下的性能表现

2. **建立硬件协议基础框架**
   - 设计并实现协议基类（protocol_base.py）
   - 定义统一的设备接口和事件机制
   - 实现设备管理器的基本框架
   - 创建设备工厂模式结构

3. **实现数据处理核心模块**
   - 开发数据缓冲区类（data_buffer.py）
   - 实现基本数据解析和过滤功能
   - 设计数据处理管道结构
   - 开发内存与文件存储机制

### 技术实施建议

1. **图表功能完善**
   - 建议采用组合模式，使每个图表类继承自基础图表类（base_chart.py）
   - 提取公共功能到基类，减少代码重复
   - 实现统一的数据绑定接口，便于与硬件协议集成
   - 注重图表性能优化，特别是大数据量场景

2. **硬件协议开发**
   - 优先开发串口协议，作为其他协议的基础和示例
   - 使用抽象工厂模式设计设备创建机制
   - 实现异步通信机制，避免UI阻塞
   - 添加协议自动检测功能，提高用户体验

3. **配置系统改进**
   - 使用 JSON 或 YAML 格式存储配置
   - 实现用户配置与默认配置分离
   - 添加配置自动保存和恢复功能
   - 开发配置UI界面，提高用户友好性

### 开发流程建议

1. **代码管理**
   - 建立功能分支开发流程
   - 实施单元测试，特别是对硬件协议和数据处理模块
   - 使用代码审查确保代码质量
   - 保持文档与代码同步更新

2. **迭代计划**
   - 采用两周迭代周期
   - 每次迭代聚焦于一个核心功能模块
   - 迭代结束进行测试和评审
   - 及时调整开发计划和优先级

3. **资源分配**
   - 图表功能完善可并行进行
   - 硬件协议开发需要重点关注
   - 数据处理模块与协议开发协同进行
   - 配置系统可在基础功能稳定后开发

通过以上开发建议，项目将能够在短期内取得实质性进展，为后续更复杂功能的开发奠定基础。
