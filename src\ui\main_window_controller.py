#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
主窗口控制器模块

负责处理主窗口的信号和槽，协调UI与业务逻辑之间的交互
"""

import os
import sys
import logging
import time
from typing import Optional, Dict

try:
    # 尝试导入PyQt5
    from PyQt5.QtCore import QObject, pyqtSlot, Qt, QPoint
    from PyQt5.QtWidgets import (
        QApplication, QMainWindow, QMessageBox, QFileDialog, QMenu, QInputDialog,
        QDialog, QVBoxLayout, QGroupBox, QRadioButton, QDialogButtonBox
    )
except ImportError:
    try:
        # 尝试导入PySide6
        from PySide6.QtCore import QObject, Qt, QPoint
        from PySide6.QtWidgets import (
            QApplication, QMainWindow, QMessageBox, QFileDialog, QMenu, QInputDialog,
            QDialog, QVBoxLayout, QGroupBox, Q<PERSON>adioButton, QDialogButtonBox
        )
        
        # PySide6兼容性处理
        pyqtSlot = lambda *args, **kwargs: lambda func: func
    except ImportError:
        # 运行时可能会另行处理
        pass

from ..utils.logger import logger
from ..utils.log_manager import log_manager
from ..visualization.chart_initializer import chart_initializer
from .protocol_config_dialog import ProtocolConfigDialog
from .protocol_selection_dialog import ProtocolSelectionDialog
from ..hardware.device_factory import DeviceFactory, DeviceType
from ..hardware.device_manager import DeviceManager
from ..hardware.protocol_base import ProtocolEventType


class LogSettingsDialog(QDialog):
    """日志设置对话框"""
    
    def __init__(self, parent=None, current_level=logging.INFO):
        """
        初始化日志设置对话框
        
        Args:
            parent: 父窗口
            current_level: 当前日志级别
        """
        super().__init__(parent)
        self.setWindowTitle("日志设置")
        self.resize(300, 200)
        
        # 创建布局
        layout = QVBoxLayout(self)
        
        # 日志级别选择
        level_group = QGroupBox("日志显示级别")
        level_layout = QVBoxLayout()
        
        self.level_buttons = {}
        
        for level, name in [
            (logging.DEBUG, "调试 (DEBUG) - 显示所有消息"),
            (logging.INFO, "信息 (INFO) - 显示信息、警告和错误"),
            (logging.WARNING, "警告 (WARNING) - 仅显示警告和错误"),
            (logging.ERROR, "错误 (ERROR) - 仅显示错误消息"),
            (logging.CRITICAL, "严重 (CRITICAL) - 仅显示严重错误")
        ]:
            rb = QRadioButton(name)
            if level == current_level:
                rb.setChecked(True)
            self.level_buttons[level] = rb
            level_layout.addWidget(rb)
            
        level_group.setLayout(level_layout)
        layout.addWidget(level_group)
        
        # 按钮
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)
        
    def get_selected_level(self):
        """
        获取选中的日志级别
        
        Returns:
            日志级别常量
        """
        for level, button in self.level_buttons.items():
            if button.isChecked():
                return level
        return logging.INFO  # 默认


class MainWindowController(QObject):
    """主窗口控制器，处理主窗口UI事件"""
    
    def __init__(self, main_window):
        """
        初始化主窗口控制器
        
        Args:
            main_window: 主窗口实例
        """
        super().__init__()
        self.main_window = main_window
        self.log_prefix = "[MainWindowController]"
        
        # 设备连接状态
        self._current_device_id = None
        self._current_protocol_name = None
        self._current_protocol_config = None
        self._is_connected = False
        self._is_acquiring = False
        
        # 获取设备管理器实例
        self._device_manager = DeviceManager()
        
        # 初始化图表相关变量
        self._time_series_chart = None  # 时间序列图实例
        self._data_series = {}  # 数据序列字典，存储不同的数据系列
        
        # 初始化日志显示区域
        self._setup_log_display()
        
        # 连接UI信号
        self._connect_signals()
        
        # 更新UI状态
        self._update_ui_state()
        
        # 初始化图表
        self._initialize_charts()
        
        logger.info(f"{self.log_prefix} 主窗口控制器初始化完成")
    
    def _setup_log_display(self):
        """设置日志显示区域"""
        if hasattr(self.main_window, 'logDisplay'):
            # 设置右键菜单
            self.main_window.logDisplay.setContextMenuPolicy(Qt.CustomContextMenu)
            self.main_window.logDisplay.customContextMenuRequested.connect(self.show_log_context_menu)
            
            # 设置只读模式
            self.main_window.logDisplay.setReadOnly(True)
            
            # 设置字体
            try:
                font = self.main_window.logDisplay.font()
                font.setFamily("Consolas")  # 使用等宽字体
                self.main_window.logDisplay.setFont(font)
            except Exception as e:
                logger.warning(f"{self.log_prefix} 设置日志字体失败: {str(e)}")
    
    def _connect_signals(self):
        """连接UI信号到对应的槽函数"""
        try:
            # 连接导航栏按钮信号
            # 文件菜单
            if hasattr(self.main_window, 'actionNewConfig'):
                self.main_window.actionNewConfig.triggered.connect(self.on_new_config)
            if hasattr(self.main_window, 'actionOpenConfig'):
                self.main_window.actionOpenConfig.triggered.connect(self.on_open_config)
            if hasattr(self.main_window, 'actionSaveConfig'):
                self.main_window.actionSaveConfig.triggered.connect(self.on_save_config)
            if hasattr(self.main_window, 'actionExportData'):
                self.main_window.actionExportData.triggered.connect(self.on_export_data)
            
            # 连接工具栏按钮信号
            if hasattr(self.main_window, 'actionConnect'):
                self.main_window.actionConnect.triggered.connect(self.on_connect)
            if hasattr(self.main_window, 'actionDisconnect'):
                self.main_window.actionDisconnect.triggered.connect(self.on_disconnect)
            if hasattr(self.main_window, 'actionStart'):
                self.main_window.actionStart.triggered.connect(self.on_start)
            if hasattr(self.main_window, 'actionStop'):
                self.main_window.actionStop.triggered.connect(self.on_stop)
            if hasattr(self.main_window, 'actionOptions'):
                self.main_window.actionOptions.triggered.connect(self.on_options)
            
            # 连接新增的数据分析区域按钮信号
            if hasattr(self.main_window, 'addButton'):
                self.main_window.addButton.clicked.connect(self.on_add_chart)
            if hasattr(self.main_window, 'deleteButton'):
                self.main_window.deleteButton.clicked.connect(self.on_delete_chart)
            if hasattr(self.main_window, 'exportButton'):
                self.main_window.exportButton.clicked.connect(self.on_export_chart)
            if hasattr(self.main_window, 'settingsButton'):
                self.main_window.settingsButton.clicked.connect(self.on_chart_settings)
            if hasattr(self.main_window, 'resetViewButton'):
                self.main_window.resetViewButton.clicked.connect(self.on_reset_view)
            
            # 连接日志区域按钮信号
            if hasattr(self.main_window, 'saveLogButton'):
                self.main_window.saveLogButton.clicked.connect(self.on_save_log)
            
            logger.info(f"{self.log_prefix} 主窗口信号连接完成")
        except Exception as e:
            logger.error(f"{self.log_prefix} 连接UI信号失败: {str(e)}")
            import traceback
            logger.debug(traceback.format_exc())
    
    @pyqtSlot()
    def on_new_config(self):
        """新建配置文件槽函数"""
        logger.info(f"{self.log_prefix} 新建配置文件")
        # TODO: 实现新建配置文件逻辑
    
    @pyqtSlot()
    def on_open_config(self):
        """打开配置文件槽函数"""
        logger.info(f"{self.log_prefix} 打开配置文件")
        # TODO: 实现打开配置文件逻辑
    
    @pyqtSlot()
    def on_save_config(self):
        """保存配置文件槽函数"""
        logger.info(f"{self.log_prefix} 保存配置文件")
        # TODO: 实现保存配置文件逻辑
    
    @pyqtSlot()
    def on_export_data(self):
        """导出数据槽函数"""
        logger.info(f"{self.log_prefix} 导出数据")
        # TODO: 实现导出数据逻辑
    
    @pyqtSlot()
    def on_connect(self):
        """连接设备槽函数"""
        logger.info(f"{self.log_prefix} 正在连接设备...")
        
        # 如果已经连接，则不执行操作
        if self._is_connected:
            logger.warning(f"{self.log_prefix} 已有设备连接，请先断开连接")
            QMessageBox.information(
                self.main_window,
                "已连接",
                "已有设备连接，请先断开连接后再连接新设备。"
            )
            return
        
        try:
            # 显示协议选择对话框
            dialog = ProtocolSelectionDialog(self.main_window)
            result = dialog.exec_()
            
            if result == QDialog.Accepted:
                # 获取选择的协议和配置
                protocol_name = dialog.get_selected_protocol()
                protocol_config = dialog.get_selected_config()
                
                if not protocol_name or not protocol_config:
                    logger.warning(f"{self.log_prefix} 未选择有效的协议配置")
                    return
                
                # 确定设备类型
                device_type = None
                if protocol_name == "串口协议":
                    device_type = DeviceType.SERIAL
                # 可以在此处添加对其他协议类型的支持
                elif protocol_name == "Modbus协议":
                    device_type = DeviceType.MODBUS
                elif protocol_name == "TCP/IP协议":
                    device_type = DeviceType.TCP
                # ... 其他协议类型判断 ...
                
                if not device_type:
                    logger.error(f"{self.log_prefix} 不支持的协议类型: {protocol_name}")
                    QMessageBox.critical(
                        self.main_window,
                        "不支持的协议",
                        f"不支持的协议类型: {protocol_name}"
                    )
                    return
                
                # 生成设备ID
                device_id = f"{device_type.name}_{protocol_name}"
                
                # 创建设备
                device_id = self._device_manager.create_device(device_type, protocol_config, device_id)
                
                if not device_id:
                    logger.error(f"{self.log_prefix} 创建设备失败: {protocol_name}")
                    QMessageBox.critical(
                        self.main_window,
                        "创建设备失败",
                        f"无法创建设备: {protocol_name}"
                    )
                    return
                
                # 获取设备实例
                device = self._device_manager.get_device(device_id)
                
                # 注册设备事件回调
                self._register_device_callbacks(device_id, device)
                
                # 连接设备
                success = device.connect()
                
                if success:
                    # 更新状态
                    self._current_device_id = device_id
                    self._current_protocol_name = protocol_name
                    self._current_protocol_config = protocol_config
                    self._is_connected = True
                    
                    # 更新UI状态
                    self._update_ui_state()
                    
                    # 更新状态栏
                    if hasattr(self.main_window, 'statusbar'):
                        self.main_window.statusbar.showMessage(f"已连接: {protocol_name}")
                    
                    logger.info(f"{self.log_prefix} 设备连接成功: {protocol_name}")
                    QMessageBox.information(
                        self.main_window,
                        "连接成功",
                        f"设备 {protocol_name} 已成功连接!"
                    )
                else:
                    # 连接失败，移除设备
                    self._device_manager.remove_device(device_id)
                    logger.error(f"{self.log_prefix} 设备连接失败: {protocol_name}")
                    QMessageBox.critical(
                        self.main_window,
                        "连接失败",
                        f"无法连接设备: {protocol_name}"
                    )
            
        except Exception as e:
            logger.error(f"{self.log_prefix} 连接设备时发生异常: {str(e)}")
            import traceback
            logger.debug(traceback.format_exc())
            QMessageBox.critical(
                self.main_window,
                "连接错误",
                f"连接设备时发生错误: {str(e)}"
            )
    
    def _clear_charts(self):
        """清除所有图表数据"""
        if self._time_series_chart:
            try:
                # 清除所有数据系列
                self._time_series_chart.clear_data()
                # 重置数据系列字典
                self._data_series = {}
                logger.debug(f"{self.log_prefix} 清除所有图表数据")
            except Exception as e:
                logger.error(f"{self.log_prefix} 清除图表数据失败: {str(e)}")
    
    @pyqtSlot()
    def on_disconnect(self):
        """断开连接槽函数"""
        logger.info(f"{self.log_prefix} 断开设备连接")
        
        # 如果未连接，则不执行操作
        if not self._is_connected or not self._current_device_id:
            logger.warning(f"{self.log_prefix} 没有连接的设备")
            return
        
        try:
            # 如果正在采集数据，先停止采集
            if self._is_acquiring:
                self.on_stop()
            
            # 获取设备
            device = self._device_manager.get_device(self._current_device_id)
            
            if device:
                # 断开设备连接
                success = device.disconnect()
                
                if success:
                    logger.info(f"{self.log_prefix} 设备 {self._current_protocol_name} 断开连接成功")
                    
                    # 停用图表自动滚动（即使已经在on_stop中停用，这里也再次确保）
                    if self._time_series_chart:
                        self._time_series_chart.set_auto_scroll(enabled=False)
                        logger.debug(f"{self.log_prefix} 断开连接时停用图表自动滚动")
                    
                    # 清除图表数据
                    self._clear_charts()
                    
                    # 更新状态栏
                    if hasattr(self.main_window, 'statusbar'):
                        self.main_window.statusbar.showMessage("已断开连接", 3000)
                else:
                    logger.error(f"{self.log_prefix} 设备 {self._current_protocol_name} 断开连接失败")
                    QMessageBox.warning(
                        self.main_window,
                        "断开失败",
                        f"断开设备 {self._current_protocol_name} 连接失败"
                    )
            
            # 清理设备资源
            self._device_manager.remove_device(self._current_device_id)
            
            # 重置状态
            self._current_device_id = None
            self._current_protocol_name = None
            self._current_protocol_config = None
            self._is_connected = False
            
            # 更新UI状态
            self._update_ui_state()
            
        except Exception as e:
            logger.error(f"{self.log_prefix} 断开设备连接时发生异常: {str(e)}")
            import traceback
            logger.debug(traceback.format_exc())
            QMessageBox.critical(
                self.main_window,
                "断开错误",
                f"断开设备连接时发生错误: {str(e)}"
            )
    
    # 添加设备回调注册方法
    def _register_device_callbacks(self, device_id: str, device) -> None:
        """
        注册设备事件回调
        
        Args:
            device_id: 设备ID
            device: 设备实例
        """
        # 注册连接事件回调
        device.register_event_callback(
            ProtocolEventType.CONNECTED,
            lambda: self._on_device_connected(device_id)
        )
        
        # 注册断开连接事件回调
        device.register_event_callback(
            ProtocolEventType.DISCONNECTED,
            lambda: self._on_device_disconnected(device_id)
        )
        
        # 注册数据接收事件回调 - 这里会在连接时就注册数据接收回调
        device.register_event_callback(
            ProtocolEventType.DATA_RECEIVED,
            lambda data: self._on_device_data_received(device_id, data)
        )
        
        # 注册错误事件回调
        device.register_event_callback(
            ProtocolEventType.ERROR,
            lambda error: self._on_device_error(device_id, error)
        )
    
    # 设备事件回调方法
    def _on_device_connected(self, device_id: str) -> None:
        """
        设备连接事件回调
        
        Args:
            device_id: 设备ID
        """
        logger.info(f"{self.log_prefix} 设备 {device_id} 已连接")
        
        # 如果是当前设备，更新UI状态
        if device_id == self._current_device_id:
            self._is_connected = True
            self._update_ui_state()
    
    def _on_device_disconnected(self, device_id: str) -> None:
        """
        设备断开连接事件回调
        
        Args:
            device_id: 设备ID
        """
        logger.info(f"{self.log_prefix} 设备 {device_id} 已断开连接")
        
        # 如果是当前设备，更新UI状态
        if device_id == self._current_device_id:
            self._is_connected = False
            self._update_ui_state()
    
    def _on_device_data_received(self, device_id: str, data) -> None:
        """
        设备数据接收事件回调
        
        Args:
            device_id: 设备ID
            data: 接收到的数据
        """
        # 只处理当前设备的数据
        if device_id != self._current_device_id:
            return
            
        logger.debug(f"{self.log_prefix} 接收到来自设备 {device_id} 的数据: {data}")
        
        # TODO: 处理接收到的数据，例如更新图表等
    
    def _on_device_error(self, device_id: str, error) -> None:
        """
        设备错误事件回调
        
        Args:
            device_id: 设备ID
            error: 错误信息
        """
        logger.error(f"{self.log_prefix} 设备 {device_id} 发生错误: {error}")
        
        # 如果是当前设备，显示错误消息
        if device_id == self._current_device_id:
            QMessageBox.critical(
                self.main_window,
                "设备错误",
                f"设备 {self._current_protocol_name} 发生错误: {error}"
            )
    
    # 更新UI状态方法
    def _update_ui_state(self) -> None:
        """更新UI状态"""
        # 更新工具栏按钮状态
        if hasattr(self.main_window, 'actionConnect'):
            self.main_window.actionConnect.setEnabled(not self._is_connected)
        
        if hasattr(self.main_window, 'actionDisconnect'):
            self.main_window.actionDisconnect.setEnabled(self._is_connected)
        
        if hasattr(self.main_window, 'actionStart'):
            self.main_window.actionStart.setEnabled(self._is_connected and not self._is_acquiring)
        
        if hasattr(self.main_window, 'actionStop'):
            self.main_window.actionStop.setEnabled(self._is_connected and self._is_acquiring)
        
        # 更新菜单状态
        if hasattr(self.main_window, 'menuConnect'):
            # 查找菜单中的连接和断开连接动作
            for action in self.main_window.menuConnect.actions():
                if action.objectName() == 'actionConnect':
                    action.setEnabled(not self._is_connected)
                elif action.objectName() == 'actionDisconnect':
                    action.setEnabled(self._is_connected)
        
        # 更新状态栏
        if hasattr(self.main_window, 'statusbar'):
            if self._is_connected:
                self.main_window.statusbar.showMessage(f"已连接: {self._current_protocol_name}")
            else:
                self.main_window.statusbar.showMessage("未连接")
    
    @pyqtSlot()
    def on_start(self):
        """开始采集槽函数"""
        logger.info(f"{self.log_prefix} 开始采集数据")
        
        # 检查是否已连接设备
        if not self._is_connected or not self._current_device_id:
            logger.warning(f"{self.log_prefix} 未连接设备，无法开始采集")
            QMessageBox.warning(
                self.main_window,
                "未连接设备",
                "请先连接设备后再开始采集数据。"
            )
            return
        
        # 如果已经在采集，则不执行操作
        if self._is_acquiring:
            logger.warning(f"{self.log_prefix} 已经在采集数据")
            return
        
        try:
            # 获取设备
            device = self._device_manager.get_device(self._current_device_id)
            
            if not device:
                logger.error(f"{self.log_prefix} 获取设备失败，设备ID: {self._current_device_id}")
                return
            
            # 清除之前的图表数据
            if self._time_series_chart:
                # 清除当前设备的数据系列
                series_id = self._current_protocol_name or "default"
                self._time_series_chart.clear_data(series_id)
                
                # 重置数据系列计数
                if series_id in self._data_series:
                    self._data_series[series_id]["count"] = 0
                
                # 启用自动滚动，仅在开始采集时激活
                self._time_series_chart.set_auto_scroll(enabled=True, window_size=30.0)
                
                logger.debug(f"{self.log_prefix} 清除图表数据并启用自动滚动: {series_id}")
            
            # 清空接收缓冲区
            device.clear_buffer()
            
            # 连接数据接收信号
            device.signals.data_received.connect(self._on_data_received)
            
            # 启动数据读取线程
            success = device.start_reading()
            
            if success:
                # 更新状态
                self._is_acquiring = True
                
                # 更新UI状态
                self._update_ui_state()
                
                # 更新状态栏
                if hasattr(self.main_window, 'statusbar'):
                    self.main_window.statusbar.showMessage(f"正在采集数据: {self._current_protocol_name}")
                    
                logger.info(f"{self.log_prefix} 成功开始数据采集")
                
            else:
                logger.error(f"{self.log_prefix} 启动数据采集失败")
                QMessageBox.critical(
                    self.main_window,
                    "采集失败",
                    "启动数据采集失败，请检查设备连接。"
                )
                
        except Exception as e:
            logger.error(f"{self.log_prefix} 开始采集数据时发生异常: {str(e)}")
            import traceback
            logger.debug(traceback.format_exc())
            QMessageBox.critical(
                self.main_window,
                "采集错误",
                f"开始采集数据时发生错误: {str(e)}"
            )
    
    def _on_data_received(self, data):
        """
        处理接收到的数据
        
        Args:
            data: 接收到的数据，可能是字符串、数字或字节类型
        """
        # 格式化数据显示
        if isinstance(data, (int, float)):
            formatted_data = f"数值: {data}"
            # 提取数值用于绘图
            value = float(data)
        elif isinstance(data, bytes):
            formatted_data = f"字节: {data.hex()}"
            # 尝试将字节数据转换为数值
            try:
                value = float(data.decode().strip())
            except (ValueError, UnicodeDecodeError):
                # 如果无法转换为数值，则不绘制
                value = None
        else:
            formatted_data = f"文本: {data}"
            # 尝试将文本转换为数值
            try:
                value = float(str(data).strip())
            except ValueError:
                # 如果无法转换为数值，则不绘制
                value = None
            
        logger.info(f"{self.log_prefix} 接收到数据: {formatted_data}")
        print(f"接收到的数据: {formatted_data} (类型: {type(data).__name__})")
        
        # 添加数据到图表
        if value is not None and self._time_series_chart:
            try:
                # 使用当前协议名称作为数据系列ID
                series_id = self._current_protocol_name or "default"
                
                # 如果是新的数据系列，初始化相关设置
                if series_id not in self._data_series:
                    self._data_series[series_id] = {
                        "count": 0,
                        "color": (255, 0, 0)  # 默认红色
                    }
                
                # 添加数据点到时间序列图
                self._time_series_chart.add_real_time_point(series_id, value)
                
                # 更新数据计数
                self._data_series[series_id]["count"] += 1
                
                logger.debug(f"{self.log_prefix} 添加数据点到图表: {series_id} = {value}")
            except Exception as e:
                logger.error(f"{self.log_prefix} 添加数据到图表失败: {str(e)}")
        
        # 如果有日志显示区域，也可以显示接收到的数据
        if hasattr(self.main_window, 'logDisplay'):
            current_time = time.strftime("%H:%M:%S", time.localtime())
            self.main_window.logDisplay.appendPlainText(f"[{current_time}] 接收: {formatted_data}")
    
    @pyqtSlot()
    def on_stop(self):
        """停止采集槽函数"""
        logger.info(f"{self.log_prefix} 停止采集数据")
        
        # 如果未在采集，则不执行操作
        if not self._is_acquiring:
            logger.warning(f"{self.log_prefix} 未开始采集，无需停止")
            return
        
        try:
            # 获取设备
            device = self._device_manager.get_device(self._current_device_id)
            
            if not device:
                logger.error(f"{self.log_prefix} 获取设备失败，设备ID: {self._current_device_id}")
                return
                
            # 停止数据读取线程
            success = device.stop_reading()
            
            if success:
                # 断开信号连接，避免重复连接
                try:
                    device.signals.data_received.disconnect(self._on_data_received)
                except (TypeError, RuntimeError):
                    # 如果信号未连接，忽略错误
                    pass
                
                # 停用图表自动滚动
                if self._time_series_chart:
                    self._time_series_chart.set_auto_scroll(enabled=False)
                    logger.debug(f"{self.log_prefix} 停用图表自动滚动")
                
                # 更新状态
                self._is_acquiring = False
                
                # 更新UI状态
                self._update_ui_state()
                
                # 更新状态栏
                if hasattr(self.main_window, 'statusbar'):
                    self.main_window.statusbar.showMessage(f"已停止数据采集: {self._current_protocol_name}")
                    
                # 保留图表上的最后数据，不清除
                logger.info(f"{self.log_prefix} 成功停止数据采集")
            else:
                logger.error(f"{self.log_prefix} 停止数据采集失败")
                QMessageBox.warning(
                    self.main_window,
                    "停止失败",
                    "停止数据采集失败。"
                )
                
        except Exception as e:
            logger.error(f"{self.log_prefix} 停止采集数据时发生异常: {str(e)}")
            import traceback
            logger.debug(traceback.format_exc())
            QMessageBox.critical(
                self.main_window,
                "停止错误",
                f"停止采集数据时发生错误: {str(e)}"
            )
    
    @pyqtSlot()
    def on_options(self):
        """选项设置槽函数"""
        logger.info(f"{self.log_prefix} 打开选项设置")
        try:
            # 创建协议配置对话框
            dialog = ProtocolConfigDialog(self.main_window)
            
            # 显示对话框
            result = dialog.exec_()
            
            if result == QDialog.Accepted:
                logger.info(f"{self.log_prefix} 协议配置已保存")
            
        except Exception as e:
            logger.error(f"{self.log_prefix} 打开协议配置对话框失败: {str(e)}")
            QMessageBox.critical(self.main_window, "错误", f"打开协议配置对话框失败: {str(e)}")
    
    @pyqtSlot()
    def on_add_chart(self):
        """添加图表槽函数"""
        logger.info(f"{self.log_prefix} 添加图表")
        # 显示图表类型选择对话框
        chart_types = ["折线图", "散点图", "柱状图", "饼图", "热力图", "箱线图", "雷达图"]
        chart_type, ok = QInputDialog.getItem(
            self.main_window, 
            "选择图表类型",
            "请选择要添加的图表类型:",
            chart_types,
            0,
            False
        )
        
        if ok and chart_type:
            logger.info(f"{self.log_prefix} 添加{chart_type}")
            # TODO: 根据选择的图表类型创建对应的图表
    
    @pyqtSlot()
    def on_delete_chart(self):
        """删除图表槽函数"""
        logger.info(f"{self.log_prefix} 删除图表")
        # TODO: 实现删除当前选中图表逻辑
    
    @pyqtSlot()
    def on_export_chart(self):
        """导出图表槽函数"""
        logger.info(f"{self.log_prefix} 导出图表")
        # TODO: 实现导出当前图表逻辑
    
    @pyqtSlot()
    def on_chart_settings(self):
        """图表设置槽函数"""
        logger.info(f"{self.log_prefix} 图表设置")
        # TODO: 实现图表设置逻辑
    
    @pyqtSlot()
    def on_reset_view(self):
        """重置视图槽函数"""
        logger.info(f"{self.log_prefix} 重置视图")
        # TODO: 实现重置视图逻辑
    
    @pyqtSlot(QPoint)
    def show_log_context_menu(self, pos):
        """
        显示日志区域上下文菜单
        
        Args:
            pos: 鼠标点击位置
        """
        if not hasattr(self.main_window, 'logDisplay'):
            return
            
        context_menu = QMenu(self.main_window)
        
        # 日志级别设置
        set_level_action = context_menu.addAction("设置日志级别")
        set_level_action.triggered.connect(self.show_log_settings)
        
        # 保存日志
        save_action = context_menu.addAction("保存日志")
        save_action.triggered.connect(self.on_save_log)
        
        context_menu.addSeparator()
        
        # 复制选中文本
        copy_action = context_menu.addAction("复制")
        copy_action.triggered.connect(lambda: self.main_window.logDisplay.copy())
        copy_action.setEnabled(self.main_window.logDisplay.textCursor().hasSelection())
        
        # 全选
        select_all_action = context_menu.addAction("全选")
        select_all_action.triggered.connect(self.main_window.logDisplay.selectAll)
        
        context_menu.addSeparator()
        
        # 清空日志
        clear_action = context_menu.addAction("清空")
        clear_action.triggered.connect(self.main_window.logDisplay.clear)
        
        # 显示菜单
        context_menu.exec_(self.main_window.logDisplay.mapToGlobal(pos))
    
    @pyqtSlot()
    def show_log_settings(self):
        """显示日志设置对话框"""
        try:
            # 创建并显示日志设置对话框
            dialog = LogSettingsDialog(self.main_window, log_manager.log_level)
            if dialog.exec_() == QDialog.Accepted:
                new_level = dialog.get_selected_level()
                log_manager.set_level(new_level)
                logger.info(f"{self.log_prefix} 日志级别已设置为 {logging.getLevelName(new_level)}")
        except Exception as e:
            logger.error(f"{self.log_prefix} 设置日志级别失败: {str(e)}")
            QMessageBox.critical(self.main_window, "错误", f"设置日志级别失败: {str(e)}")
    
    @pyqtSlot()
    def on_save_log(self):
        """保存日志槽函数"""
        if not hasattr(self.main_window, 'logDisplay'):
            return
            
        try:
            # 显示文件保存对话框
            file_path, filter_used = QFileDialog.getSaveFileName(
                self.main_window,
                "保存日志",
                "",
                "文本文件 (*.txt);;HTML文件 (*.html);;所有文件 (*)"
            )
            
            if not file_path:
                return  # 用户取消
                
            # 使用日志管理器保存日志
            success, error_msg = log_manager.save_logs(file_path)
            
            if success:
                logger.info(f"{self.log_prefix} 日志已保存到 {file_path}")
                QMessageBox.information(
                    self.main_window,
                    "成功",
                    f"日志已成功保存到:\n{file_path}"
                )
            else:
                logger.error(f"{self.log_prefix} 保存日志失败: {error_msg}")
                QMessageBox.critical(
                    self.main_window,
                    "保存失败",
                    f"保存日志失败:\n{error_msg}"
                )
                
        except Exception as e:
            logger.error(f"{self.log_prefix} 保存日志异常: {str(e)}")
            QMessageBox.critical(
                self.main_window,
                "错误",
                f"保存日志过程中发生异常:\n{str(e)}"
            )

    def _initialize_charts(self):
        """初始化图表"""
        try:
            # 初始化时间序列图
            if hasattr(self.main_window, 'visualizationFrame'):
                # 使用chart_initializer初始化时间序列图
                self._time_series_chart = chart_initializer.initialize_time_series_chart(
                    self.main_window.visualizationFrame
                )
                
                if self._time_series_chart:
                    # 设置时间序列图的属性
                    self._time_series_chart.set_title("实时数据监测")
                    # 初始化时不启用自动滚动，将在start按钮点击后启用
                    
                    # 设置更直观的时间格式：显示小时:分钟:秒钟
                    self._time_series_chart.set_time_format("%H:%M:%S")
                    
                    # 设置坐标轴标签更直观
                    self._time_series_chart._plot_item.setLabel('bottom', "时间 (时:分:秒)", color=self._time_series_chart._text_color)
                    self._time_series_chart._plot_item.setLabel('left', "数值", color=self._time_series_chart._text_color)
                    
                    logger.info(f"{self.log_prefix} 时间序列图初始化成功")
                else:
                    logger.error(f"{self.log_prefix} 时间序列图初始化失败")
            else:
                logger.warning(f"{self.log_prefix} 主窗口缺少visualizationFrame，无法初始化时间序列图")
                
        except Exception as e:
            logger.error(f"{self.log_prefix} 初始化图表失败: {str(e)}")
            import traceback
            logger.debug(traceback.format_exc())


# 初始化函数，在应用程序启动时调用
def initialize(main_window):
    """
    初始化主窗口控制器
    
    Args:
        main_window: 主窗口实例
        
    Returns:
        主窗口控制器实例
    """
    controller = MainWindowController(main_window)
    return controller 