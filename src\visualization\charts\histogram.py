#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
柱状图模块

实现基于PyQtGraph的柱状图，支持分组柱状图、堆叠柱状图等功能。
"""

import numpy as np
from typing import Dict, List, Optional, Tuple, Union, Any

try:
    import pyqtgraph as pg
    from pyqtgraph.Qt import QtCore, QtGui
except ImportError:
    # 如果导入失败，可能会在运行时另行处理
    pass

from ..base_chart import BaseChart, ChartType
from ...utils.logger import logger


class HistogramChart(BaseChart):
    """
    柱状图类
    
    实现柱状图的创建、数据管理和样式设置等功能。
    """
    
    def __init__(
        self, 
        title: str = "柱状图", 
        x_label: str = "类别", 
        y_label: str = "数值"
    ):
        """
        初始化柱状图
        
        Args:
            title: 图表标题
            x_label: X轴标签
            y_label: Y轴标签
        """
        super().__init__(title, x_label, y_label, ChartType.BAR)
        
        # 柱状图特有的属性
        self._bar_colors = {}  # 存储每组柱子的颜色
        self._bar_widths = {}  # 存储每组柱子的宽度
        self._bar_brushes = {}  # 存储每组柱子的填充样式
        self._categories = []  # 存储类别标签
        self._stacked = False  # 是否为堆叠柱状图
        self._group_spacing = 0.2  # 组之间的间距
        self._bar_spacing = 0.05  # 柱子之间的间距
    
    def _initialize_specific(self):
        """实现图表特定的初始化"""
        # 设置x轴显示为分类模式
        self._plot_item.getAxis('bottom').setTicks([[], []])  # 清除自动生成的刻度
    
    def set_categories(self, categories: List[str]):
        """
        设置类别标签
        
        Args:
            categories: 类别名称列表
        """
        # 存储类别
        self._categories = categories
        
        if not self._initialized:
            return
        
        try:
            # 更新x轴刻度 - 修改格式以确保每项只包含两个元素
            ticks = []
            for i, cat in enumerate(categories):
                # 确保类别标签是字符串类型
                if not isinstance(cat, str):
                    cat = str(cat)
                # 创建二元组 (位置, 标签)
                ticks.append((i, cat))
            
            # 使用二维列表包装ticks，PyQtGraph需要这种格式
            self._plot_item.getAxis('bottom').setTicks([ticks])
            
            logger.debug(f"{self._log_prefix} 设置类别标签: {categories}")
        except Exception as e:
            logger.error(f"{self._log_prefix} 设置类别标签失败: {str(e)}")
    
    def add_data(
        self, 
        data_id: str, 
        data: Union[List, np.ndarray], 
        **kwargs
    ):
        """
        添加数据到柱状图
        
        Args:
            data_id: 数据标识符
            data: 数据内容，每个类别对应的值
            **kwargs: 额外配置参数，包括：
                - name: 数据系列名称（显示在图例中）
                - color: 柱子颜色，格式为 (R,G,B) 或 '#RRGGBB'
                - bar_width: 柱子宽度，默认为0.6
                - brush_style: 填充样式，默认为实心
                - categories: 类别标签列表
                - offset: 柱子偏移量，用于分组柱状图
        """
        if not self._initialized:
            self.initialize()
        
        try:
            # 解析数据
            if isinstance(data, np.ndarray):
                data = data.tolist()
            
            # 确保数据是列表类型
            if not isinstance(data, list):
                raise ValueError(f"数据必须是列表或NumPy数组，但获得了 {type(data)}")
            
            # 解析样式参数
            name = kwargs.get('name', data_id)
            color = kwargs.get('color', None)
            bar_width = kwargs.get('bar_width', 0.6)
            categories = kwargs.get('categories', None)
            offset = kwargs.get('offset', 0)
            
            # 如果提供了类别标签，则更新
            if categories is not None:
                self.set_categories(categories)
            
            # 如果没有设置类别，使用索引
            if not self._categories:
                self._categories = [str(i) for i in range(len(data))]
            
            # 确保数据长度与类别数量匹配
            if len(data) != len(self._categories):
                raise ValueError(f"数据长度 ({len(data)}) 与类别数量 ({len(self._categories)}) 不匹配")
            
            # 如果没有指定颜色，使用自动生成的颜色
            if color is None:
                # 使用预定义的颜色列表
                colors = [
                    (255, 0, 0),      # 红色
                    (0, 0, 255),      # 蓝色
                    (0, 255, 0),      # 绿色
                    (255, 165, 0),    # 橙色
                    (128, 0, 128),    # 紫色
                    (255, 192, 203),  # 粉红色
                    (165, 42, 42),    # 棕色
                    (0, 255, 255),    # 青色
                ]
                color = colors[len(self._data_items) % len(colors)]
            
            # 创建画刷
            brush = pg.mkBrush(color)
            
            # 创建柱子的x坐标
            x = np.arange(len(data)) + offset
            
            # 创建柱状图数据项
            bar_item = pg.BarGraphItem(
                x=x,
                height=data,
                width=bar_width,
                brush=brush,
                pen=pg.mkPen(color=(0, 0, 0, 100), width=1),
                name=name
            )
            
            # 添加到绘图项
            self._plot_item.addItem(bar_item)
            
            # 存储数据项和样式信息
            self._data_items[data_id] = bar_item
            self._bar_colors[data_id] = color
            self._bar_widths[data_id] = bar_width
            self._bar_brushes[data_id] = brush
            
            # 调整x轴范围，确保显示所有柱子
            self._plot_item.setXRange(-0.5, len(self._categories) - 0.5)
            
            logger.debug(f"{self._log_prefix} 添加柱状图数据: {data_id}, 数据点数: {len(data)}")
            
        except Exception as e:
            logger.error(f"{self._log_prefix} 添加数据失败: {str(e)}")
            raise
    
    def update_data(
        self, 
        data_id: str, 
        data: Union[List, np.ndarray], 
        **kwargs
    ):
        """
        更新柱状图中的数据
        
        Args:
            data_id: 数据标识符
            data: 新的数据内容
            **kwargs: 额外的样式参数，与add_data相同
        """
        if not self._initialized or data_id not in self._data_items:
            if data_id not in self._data_items:
                # 如果数据不存在，则添加新数据
                self.add_data(data_id, data, **kwargs)
            return
        
        try:
            # 解析数据
            if isinstance(data, np.ndarray):
                data = data.tolist()
            
            # 确保数据是列表类型
            if not isinstance(data, list):
                raise ValueError(f"数据必须是列表或NumPy数组，但获得了 {type(data)}")
            
            # 获取柱状图项
            bar_item = self._data_items[data_id]
            
            # 获取当前偏移量
            current_x = bar_item.opts.get('x', [])
            offset = 0
            if len(current_x) > 0:
                offset = current_x[0] - 0  # 假设第一个柱子在位置0
            
            # 解析样式参数
            bar_width = kwargs.get('bar_width', self._bar_widths.get(data_id, 0.6))
            color = kwargs.get('color', self._bar_colors.get(data_id))
            categories = kwargs.get('categories', None)
            new_offset = kwargs.get('offset', offset)
            
            # 如果提供了类别标签，则更新
            if categories is not None:
                self.set_categories(categories)
            
            # 确保数据长度与类别数量匹配
            if len(data) != len(self._categories):
                raise ValueError(f"数据长度 ({len(data)}) 与类别数量 ({len(self._categories)}) 不匹配")
            
            # 更新颜色（如果提供）
            if color is not None and color != self._bar_colors.get(data_id):
                self._bar_colors[data_id] = color
                brush = pg.mkBrush(color)
                self._bar_brushes[data_id] = brush
            else:
                brush = self._bar_brushes.get(data_id)
            
            # 创建柱子的x坐标
            x = np.arange(len(data)) + new_offset
            
            # 更新柱状图数据
            bar_item.setOpts(
                x=x,
                height=data,
                width=bar_width,
                brush=brush
            )
            
            # 更新样式信息
            self._bar_widths[data_id] = bar_width
            
            logger.debug(f"{self._log_prefix} 更新柱状图数据: {data_id}, 数据点数: {len(data)}")
            
        except Exception as e:
            logger.error(f"{self._log_prefix} 更新数据失败: {str(e)}")
            raise
    
    def set_bar_width(self, data_id: str, width: float):
        """
        设置指定数据系列的柱子宽度
        
        Args:
            data_id: 数据标识符
            width: 柱子宽度
        """
        if not self._initialized or data_id not in self._data_items:
            return
        
        bar_item = self._data_items[data_id]
        bar_item.setOpts(width=width)
        self._bar_widths[data_id] = width
    
    def set_bar_color(self, data_id: str, color):
        """
        设置指定数据系列的柱子颜色
        
        Args:
            data_id: 数据标识符
            color: 颜色，格式为 (R,G,B) 或 '#RRGGBB'
        """
        if not self._initialized or data_id not in self._data_items:
            return
        
        brush = pg.mkBrush(color)
        
        bar_item = self._data_items[data_id]
        bar_item.setOpts(brush=brush)
        
        self._bar_colors[data_id] = color
        self._bar_brushes[data_id] = brush
    
    def create_grouped_bars(self, data_dict: Dict[str, List], **kwargs):
        """
        创建分组柱状图
        
        Args:
            data_dict: 数据字典，格式为 {data_id: values}
            **kwargs: 额外配置参数
                - categories: 类别标签列表
                - colors: 颜色字典或列表
                - bar_width: 单个柱子的宽度，默认为0.2
                - group_spacing: 组之间的间距，默认为0.2
        """
        if not self._initialized:
            self.initialize()
        
        # 解析参数
        categories = kwargs.get('categories', None)
        colors = kwargs.get('colors', None)
        bar_width = kwargs.get('bar_width', 0.2)
        self._group_spacing = kwargs.get('group_spacing', 0.2)
        
        # 如果提供了类别标签，则更新
        if categories is not None:
            self.set_categories(categories)
        
        # 获取数据系列数量
        n_series = len(data_dict)
        
        # 清除现有数据
        self.clear_data()
        
        # 为每个数据系列添加柱子
        for i, (data_id, values) in enumerate(data_dict.items()):
            # 计算偏移量，使柱子分组排列
            offset = (i - n_series/2) * bar_width + bar_width/2
            
            # 确定颜色
            color = None
            if colors is not None:
                if isinstance(colors, dict) and data_id in colors:
                    color = colors[data_id]
                elif isinstance(colors, list) and i < len(colors):
                    color = colors[i]
            
            # 添加数据
            self.add_data(
                data_id=data_id,
                data=values,
                bar_width=bar_width,
                color=color,
                offset=offset,
                name=data_id
            )
        
        # 调整x轴范围和标签
        self._plot_item.setXRange(-0.5, len(self._categories) - 0.5)
    
    def create_stacked_bars(self, data_dict: Dict[str, List], **kwargs):
        """
        创建堆叠柱状图
        
        Args:
            data_dict: 数据字典，格式为 {data_id: values}
            **kwargs: 额外配置参数
                - categories: 类别标签列表
                - colors: 颜色字典或列表
                - bar_width: 柱子的宽度，默认为0.6
        """
        if not self._initialized:
            self.initialize()
        
        # 解析参数
        categories = kwargs.get('categories', None)
        colors = kwargs.get('colors', None)
        bar_width = kwargs.get('bar_width', 0.6)
        
        # 如果提供了类别标签，则更新
        if categories is not None:
            self.set_categories(categories)
        
        # 启用堆叠模式
        self._stacked = True
        
        # 清除现有数据
        self.clear_data()
        
        # 计算每个位置的累积高度
        n_categories = len(self._categories)
        cumulative_heights = [0] * n_categories
        
        # 为每个数据系列添加柱子
        for i, (data_id, values) in enumerate(data_dict.items()):
            # 确定颜色
            color = None
            if colors is not None:
                if isinstance(colors, dict) and data_id in colors:
                    color = colors[data_id]
                elif isinstance(colors, list) and i < len(colors):
                    color = colors[i]
            
            # 复制当前累积高度，作为这组柱子的起始位置
            bottom = cumulative_heights.copy()
            
            # 添加数据（作为单独的柱子，后续需要自定义绘制堆叠效果）
            stacked_data = []
            for j, value in enumerate(values):
                # 创建自定义BarGraphItem显示堆叠柱子
                x = j
                height = value
                y = bottom[j]
                
                # 确保数据有效
                if height <= 0:
                    continue
                
                # 更新累积高度
                cumulative_heights[j] += height
                
                # 创建画刷
                if color is None:
                    # 使用预定义的颜色列表
                    colors_list = [
                        (255, 0, 0),      # 红色
                        (0, 0, 255),      # 蓝色
                        (0, 255, 0),      # 绿色
                        (255, 165, 0),    # 橙色
                        (128, 0, 128),    # 紫色
                        (255, 192, 203),  # 粉红色
                        (165, 42, 42),    # 棕色
                        (0, 255, 255),    # 青色
                    ]
                    color_i = i % len(colors_list)
                    bar_color = colors_list[color_i]
                else:
                    bar_color = color
                
                brush = pg.mkBrush(bar_color)
                
                # 创建柱状图项
                bar_item = pg.BarGraphItem(
                    x=[x],
                    height=[height],
                    width=bar_width,
                    brush=brush,
                    pen=pg.mkPen(color=(0, 0, 0, 100), width=1),
                    name=data_id if j == 0 else None  # 只在图例中显示一次
                )
                
                # 设置柱子底部位置
                bar_item.setOpts(bottom=[y])
                
                # 添加到绘图项
                self._plot_item.addItem(bar_item)
                
                # 存储为子项
                subitem_id = f"{data_id}_{j}"
                self._data_items[subitem_id] = bar_item
                self._bar_colors[subitem_id] = bar_color
                self._bar_widths[subitem_id] = bar_width
                self._bar_brushes[subitem_id] = brush
            
            logger.debug(f"{self._log_prefix} 添加堆叠柱状图数据: {data_id}, 数据点数: {len(values)}")
        
        # 调整y轴范围以显示所有柱子
        max_height = max(cumulative_heights) if cumulative_heights else 1
        self._plot_item.setYRange(0, max_height * 1.1)
        
        # 调整x轴范围
        self._plot_item.setXRange(-0.5, len(self._categories) - 0.5)
