# 1. 需求与规范文档

| 版本 | 最后更新时间 | 更新者 | 变更摘要 |
| :--- | :--- | :--- | :--- |
| 1.0  | 2025-07-31 09:30:00 +08:00 | PM | 初始创建 |
| 1.1  | 2025-07-31 09:45:00 +08:00 | PM | 深度需求分析和项目现状评估 |

---

## 0. 项目初始化状态

### 0.1 APEX-6强制初始化检查清单
- [ ] PromptX中已创建CM角色并定制完成
- [ ] PromptX中已创建PM角色并定制完成
- [ ] PromptX中已创建BA角色并定制完成
- [ ] PromptX中已创建SA角色并定制完成
- [ ] PromptX中已创建PL角色并定制完成
- [ ] PromptX中已创建LD角色并定制完成
- [x] 用户已在.serena\memories\路径下创建X.Continuous Context and Intelligence Document.md
- [x] 用户已在.serena\memories\路径下创建1.Requirements and Specifications Document.md
- [x] 用户已在.serena\memories\路径下创建2.Solution Architecture and Innovation Document.md
- [x] 用户已在.serena\memories\路径下创建3.Technology Stack and Design Document.md
- [x] 用户已在.serena\memories\路径下创建4.Project Planning and Task Management.md
- [x] 用户已在.serena\memories\路径下创建5.Development Progress and Testing.md

### 0.2 工具集成验证状态
- [x] ACE(AugmentContextEngine)信息收集工具可正常使用
- [x] Augment自带的task工具已激活并可进行项目管理
- [x] Serena MCP记忆管理工具连接正常，可读写.serena\memories\文档
- [ ] PromptX MCP角色管理工具可正常切换6个专业角色
- [ ] Context 7 MCP专业库文档查询工具可正常使用
- [x] PowerShell环境验证通过，支持所有控制台操作
- [ ] mcp.server_time时间服务可正常获取准确时间戳

## 1. 项目概述

### 1.1 项目目标
* **核心目标：** 开发一个基于Python Qt的专业数据可视化软件，支持多种协议硬件接口的数据采集，提供丰富的图表可视化和分析功能
* **业务价值：**
  - 为工程师和研究人员提供统一的数据采集和可视化平台
  - 支持多种硬件协议，降低设备集成复杂度
  - 提供实时数据监控和历史数据分析能力
  - 提高数据分析效率和决策质量

### 1.2 项目范围
* **范围内 (In Scope):**
  - 基于PyQt5的桌面应用程序开发
  - 支持8种硬件协议：串口、Modbus、TCP/IP、CAN总线、MQTT、OPC UA、I2C、SPI
  - 支持8种图表类型：折线图、散点图、时间序列图、柱状图、热力图、饼图、箱线图、雷达图
  - 实时数据采集、处理和可视化
  - 数据存储和导出功能
  - 配置管理和日志系统
  - 基础测试框架和示例代码
* **范围外 (Out of Scope):**
  - Web版本或移动端应用
  - 云端数据存储和同步
  - 高级数据挖掘和机器学习功能
  - 多用户权限管理系统
  - 插件开发SDK（第一版本不包含）

## 2. 用户需求

### 2.1 用户故事 (User Stories)
| ID | 角色 | 我想要... (功能) | 以便... (价值) | 优先级 |
| :-- | :-- | :--- | :--- | :--- |
| US001 | 作为工程师 | 连接串口设备并实时显示数据 | 监控设备运行状态 | 高 |
| US002 | 作为研究人员 | 同时显示多种图表类型 | 从不同角度分析数据 | 高 |
| US003 | 作为测试人员 | 配置Modbus设备参数 | 快速连接不同的Modbus设备 | 高 |
| US004 | 作为数据分析师 | 导出图表和数据 | 在其他工具中进一步分析 | 中 |
| US005 | 作为系统集成商 | 支持多种协议设备 | 统一管理不同厂商设备 | 高 |
| US006 | 作为运维人员 | 查看历史数据趋势 | 发现设备异常模式 | 中 |
| US007 | 作为开发者 | 自定义图表样式 | 满足特定显示需求 | 低 |

### 2.2 功能性需求 (Functional Requirements)

#### 2.2.1 硬件协议支持
* **FR001: 串口通信**
  * **描述:** 支持标准串口通信，可配置波特率、数据位、校验位等参数
  * **规则:** 支持COM1-COM256端口，波特率范围9600-115200，支持ASCII和二进制数据
* **FR002: Modbus协议**
  * **描述:** 支持Modbus RTU和TCP协议，读取保持寄存器、输入寄存器等
  * **规则:** 支持功能码01-06，设备地址1-247，超时重试机制
* **FR003: TCP/IP通信**
  * **描述:** 支持TCP客户端和服务器模式，UDP通信
  * **规则:** 支持IPv4，端口范围1-65535，连接超时5秒
* **FR004: MQTT协议**
  * **描述:** 支持MQTT客户端，订阅和发布消息
  * **规则:** 支持QoS 0-2，保持连接，自动重连机制

#### 2.2.2 数据可视化
* **FR005: 实时图表显示**
  * **描述:** 支持8种图表类型的实时数据显示
  * **规则:** 更新频率可配置（10ms-10s），最大数据点1000个
* **FR006: 图表交互**
  * **描述:** 支持缩放、平移、数据点选择等交互操作
  * **规则:** 鼠标滚轮缩放，拖拽平移，点击显示数值
* **FR007: 多图表布局**
  * **描述:** 支持网格布局、标签页布局等多种显示方式
  * **规则:** 最多支持16个图表同时显示

#### 2.2.3 数据管理
* **FR008: 数据存储**
  * **描述:** 支持SQLite数据库存储历史数据
  * **规则:** 自动保存间隔60秒，数据保留期可配置
* **FR009: 数据导出**
  * **描述:** 支持CSV、PNG格式导出
  * **规则:** 导出文件名包含时间戳，支持批量导出

### 2.3 非功能性需求 (Non-Functional Requirements)
* **性能要求:**
  - 应用启动时间 < 5秒
  - 图表刷新延迟 < 100ms
  - 支持1000个数据点实时显示
  - 内存使用 < 500MB（正常工作负载）
* **可靠性要求:**
  - 连续运行24小时无崩溃
  - 网络断线自动重连
  - 数据丢失率 < 0.1%
  - 异常恢复时间 < 30秒
* **可用性要求:**
  - 支持Windows 10/11操作系统
  - 界面响应时间 < 200ms
  - 错误信息清晰易懂
  - 支持中文界面
* **兼容性要求:**
  - Python 3.8+ 环境
  - PyQt5 5.15+ 版本
  - 支持1920x1080及以上分辨率
* **安全性要求:**
  - 配置文件加密存储敏感信息
  - 网络通信数据校验
  - 日志文件访问权限控制

## 3. 系统规范

### 3.1 编码规范 (遵循APEX-6核心编码原则)
* **核心编码原则:** KISS, YAGNI, SOLID, DRY, 高内聚低耦合, 代码可读性, 可测试性, 安全编码
* **主要语言:** Python 3.8+
* **风格指南:** PEP 8 Python编码规范
* **命名约定:**
  - 变量和函数使用snake_case
  - 类名使用PascalCase
  - 常量使用UPPER_CASE
  - 私有成员使用_前缀
* **代码注释:**
  - 注释覆盖率要求 > 20%
  - 复杂逻辑必须添加注释
  - 使用中文注释说明业务逻辑
* **文档字符串:**
  - 所有公共函数和类必须有docstring
  - 使用Google风格的docstring格式
  - 包含参数说明、返回值和异常信息
* **CHENGQI注释格式:**
  ```python
  # {{CHENGQI: Action: [Added/Modified/Removed]; Timestamp: [YYYY-MM-DD HH:MM:SS +08:00]; Reason: [Task ID: #123, brief why]; Principle_Applied: [SOLID/Security/etc]; Role: [CM/PM/BA/SA/PL/LD]; Mode: [MODEX,1-5];}}
  ```

### 3.2 开发规范
* **版本控制规范:**
  * 分支策略: [Git Flow/GitHub Flow/...]
  * 提交信息格式: [Conventional Commits/...]
  * 代码审查要求: [必须/可选]
* **测试规范:**
  * 单元测试覆盖率要求: [百分比]
  * 测试命名约定: [格式规范]
  * 测试数据管理: [策略说明]
* **质量标准:**
  * 代码复杂度限制: [圈复杂度上限]
  * 性能要求: [响应时间/吞吐量标准]
  * 安全编码规范: [具体要求]

### 3.3 项目管理规范 (遵循APEX-6工作流)
* **递进式工作流原则:** 后续模式必须基于前序模式产出的文档开展工作，确保工作流的连贯性和一致性
* **文档强制维护原则:** 每次开发活动都必须同步更新Serena中的相关文档，不得遗漏
* **需求确认原则:** 每次用户提问后，必须先复述理解的需求，提出解决方案，获得用户确认后再执行
* **多轮沟通机制:** 特别在模式2方案细化阶段，需要与用户进行充分的多轮深度沟通
* **角色切换规范:** 每种模式切换时必须同步切换到对应的专业角色
* **工具集成要求:**
  - 强制使用ACE进行信息收集
  - 使用Augment task工具进行项目规划与追踪
  - 使用Serena MCP进行文档读写操作
  - 使用PromptX MCP进行角色切换
  - 模式5中使用专业库时必须先使用Context 7 MCP查询文档
* **PowerShell指令要求:** 所有控制台操作必须使用Windows PowerShell准确指令
* **时间戳管理:** 所有文件操作必须使用mcp.server_time服务获取准确时间戳

### 3.4 验收标准
* **功能验收:**
  - 所有8种硬件协议能够正常连接和通信
  - 所有8种图表类型能够正确显示数据
  - 数据采集、存储、导出功能正常
  - 配置管理和日志系统工作正常
* **质量验收:**
  - 代码覆盖率 > 80%
  - 单元测试通过率 100%
  - 性能指标满足非功能性需求
  - 代码符合PEP 8规范
* **文档验收:**
  - API文档完整覆盖所有公共接口
  - 用户手册包含安装和使用说明
  - 开发文档包含架构设计和扩展指南

## 4. 项目现状评估 (基于ACE分析结果)

### 4.1 当前完成度分析
**整体完成度: 约60%**

#### 4.1.1 已完成模块 ✅
* **核心框架 (90%完成)**
  - ✅ 应用程序主类 (src/core/app.py)
  - ✅ 配置管理系统 (src/core/config.py)
  - ✅ 事件总线机制 (src/core/event_bus.py)
  - ✅ 线程管理器 (src/core/thread_manager.py)

* **用户界面 (85%完成)**
  - ✅ 主窗口UI设计 (src/ui/main_window.ui)
  - ✅ 主窗口控制器 (src/ui/main_window_controller.py)
  - ✅ 垂直分割器和布局管理
  - ✅ 日志显示区域和右键菜单

* **日志系统 (95%完成)**
  - ✅ 基础日志记录功能
  - ✅ 文件输出和UI显示集成
  - ✅ 彩色日志显示
  - ✅ 日志级别设置和保存功能

* **数据可视化基础 (70%完成)**
  - ✅ 图表基类 (src/visualization/base_chart.py)
  - ✅ 图表管理器 (src/visualization/chart_manager.py)
  - ✅ 图表初始化器 (src/visualization/chart_initializer.py)
  - ✅ 时序图组件 (完整实现)
  - ✅ 折线图组件 (完整实现)
  - ✅ 饼图组件 (已修复显示错误)
  - ✅ 箱线图组件 (已修复显示错误)

#### 4.1.2 部分完成模块 ⚠️
* **硬件协议支持 (30%完成)**
  - ✅ 协议基类 (src/hardware/protocol_base.py)
  - ✅ 设备管理器框架 (src/hardware/device_manager.py)
  - ✅ 串口协议 (src/hardware/protocols/serial_protocol.py) - 完整实现
  - ❌ Modbus协议 - 仅有框架，未实现
  - ❌ TCP/IP协议 - 未实现
  - ❌ CAN总线协议 - 未实现
  - ❌ MQTT协议 - 未实现
  - ❌ OPC UA协议 - 未实现
  - ❌ I2C协议 - 未实现
  - ❌ SPI协议 - 未实现

* **数据可视化图表 (50%完成)**
  - ⚠️ 散点图 (src/visualization/charts/scatter.py) - 文件存在但功能不完整
  - ⚠️ 柱状图 (src/visualization/charts/histogram.py) - 文件存在但功能不完整
  - ⚠️ 热力图 (src/visualization/charts/heatmap.py) - 文件存在但功能不完整
  - ⚠️ 雷达图 (src/visualization/charts/radar.py) - 文件存在但功能不完整

#### 4.1.3 待开发模块 ❌
* **数据处理层 (10%完成)**
  - ⚠️ 数据分析器 (src/data/analyzer.py) - 基础框架
  - ⚠️ 数据缓冲区 (src/data/buffer.py) - 基础框架
  - ⚠️ 数据清洗 (src/data/cleaner.py) - 基础框架
  - ❌ 数据存储模块 - 未实现

* **测试框架 (20%完成)**
  - ✅ 测试运行器 (run_tests.py)
  - ⚠️ 核心模块测试 - 部分实现
  - ❌ 硬件协议测试 - 未实现
  - ❌ 可视化组件测试 - 未实现

### 4.2 关键问题识别
1. **包管理问题** ✅ 已解决
   - 原问题：uv install命令错误
   - 解决方案：使用uv pip install -e .成功安装依赖

2. **硬件协议实现不完整** ❌ 待解决
   - 仅串口协议完整实现
   - 其他7种协议需要完整开发

3. **图表功能不完整** ❌ 待解决
   - 4种图表类型功能不完整
   - 缺少数据实时更新机制

4. **数据处理层薄弱** ❌ 待解决
   - 缺少完整的数据存储方案
   - 数据分析功能基础薄弱

### 4.3 开发优先级建议
**高优先级 (立即开发):**
1. 完善现有图表组件功能
2. 实现Modbus协议支持
3. 开发数据缓冲区和存储机制

**中优先级 (1-2周内):**
1. 实现TCP/IP和MQTT协议
2. 完善数据分析功能
3. 增强图表交互体验

**低优先级 (后续版本):**
1. 实现CAN、OPC UA、I2C、SPI协议
2. 开发高级数据分析功能
3. 优化性能和用户体验

## 5. 递进输出 (为模式2方案细化提供需求基础)

### 5.1 为BA角色提供的关键信息
| 输出类别 | 具体内容 | 方案设计影响 | 优先级 |
| :--- | :--- | :--- | :--- |
| 项目目标 | 基于Python Qt的数据可视化软件，支持多协议硬件接口 | 技术栈选择和架构设计约束 | 高 |
| 功能需求 | 8种协议+8种图表+实时数据处理 | 模块化设计和接口标准化要求 | 高 |
| 非功能需求 | 性能<100ms延迟，可靠性24h运行，兼容Windows | 技术选型和优化策略约束 | 高 |
| 项目现状 | 60%完成度，核心框架完成，协议层待开发 | 增量开发策略和重构需求 | 高 |
| 开发规范 | Python PEP8，SOLID原则，测试覆盖率>80% | 代码质量和可维护性要求 | 中 |
| 验收标准 | 功能完整性+性能指标+代码质量标准 | 方案评估和验证标准 | 中 |

### 5.2 需求传递检查清单
- [x] 项目目标明确且可衡量 - 数据可视化软件，支持多协议硬件接口
- [x] 功能需求完整且无歧义 - 详细定义了9个功能需求和用户故事
- [x] 非功能需求具体且可验证 - 明确了性能、可靠性、兼容性指标
- [x] 开发规范详细且可执行 - Python PEP8，SOLID原则，测试要求
- [x] 验收标准清晰且可操作 - 功能、质量、文档验收标准明确
- [x] 用户期望和约束条件已明确记录 - 基于现有项目60%完成度
- [x] 项目现状评估完整 - 详细分析了已完成、部分完成、待开发模块

### 5.3 后续阶段准备
* **方案设计准备:** 为BA角色提供完整的需求分析基础和项目现状，支持基于现有代码的增量开发方案设计
* **用户沟通准备:** 提供详细的功能需求和技术约束，支持多轮沟通确定开发优先级和实现策略
* **技术约束传递:** 明确了Python Qt技术栈、性能要求、兼容性约束，为技术方案选择提供指导

**递进关系说明:** 本文档作为模式1的产出，为模式2的方案细化与多轮沟通提供完整的需求基础，确保BA角色能够基于准确的需求信息进行解决方案设计。
