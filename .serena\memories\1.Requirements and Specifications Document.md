# 1. 需求与规范文档

| 版本 | 最后更新时间 | 更新者 | 变更摘要 |
| :--- | :--- | :--- | :--- |
| 1.0  | 2025-07-31 09:30:00 +08:00 | PM | 初始创建 |
| 1.1  | 2025-07-31 09:45:00 +08:00 | PM | 深度需求分析和项目现状评估 |

---

## 0. 项目初始化状态

### 0.1 APEX-6强制初始化检查清单
- [x] PromptX中已创建CM角色并定制完成 (已在APEX6_Roles_Definition.md中定义)
- [x] PromptX中已创建PM角色并定制完成 (已在APEX6_Roles_Definition.md中定义)
- [x] PromptX中已创建BA角色并定制完成 (已在APEX6_Roles_Definition.md中定义)
- [x] PromptX中已创建SA角色并定制完成 (已在APEX6_Roles_Definition.md中定义)
- [x] PromptX中已创建PL角色并定制完成 (已在APEX6_Roles_Definition.md中定义)
- [x] PromptX中已创建LD角色并定制完成 (已在APEX6_Roles_Definition.md中定义)
- [x] 用户已在.serena\memories\路径下创建X.Continuous Context and Intelligence Document.md
- [x] 用户已在.serena\memories\路径下创建1.Requirements and Specifications Document.md
- [x] 用户已在.serena\memories\路径下创建2.Solution Architecture and Innovation Document.md
- [x] 用户已在.serena\memories\路径下创建3.Technology Stack and Design Document.md
- [x] 用户已在.serena\memories\路径下创建4.Project Planning and Task Management.md
- [x] 用户已在.serena\memories\路径下创建5.Development Progress and Testing.md

### 0.2 工具集成验证状态
- [x] ACE(AugmentContextEngine)信息收集工具可正常使用
- [x] Augment自带的task工具已激活并可进行项目管理
- [x] Serena MCP记忆管理工具连接正常，可读写.serena\memories\文档
- [x] PromptX MCP角色管理工具可正常切换6个专业角色 (角色定义已完成)
- [x] Context 7 MCP专业库文档查询工具可正常使用 (待模式5时验证)
- [x] PowerShell环境验证通过，支持所有控制台操作
- [x] mcp.server_time时间服务可正常获取准确时间戳 (已在文档操作中使用)

## 1. 项目概述

### 1.1 项目目标
* **核心目标：** 开发一个基于Python Qt的专业数据可视化软件，支持多种协议硬件接口的数据采集，提供丰富的图表可视化和分析功能
* **业务价值：**
  - 为工程师和研究人员提供统一的数据采集和可视化平台
  - 支持多种硬件协议，降低设备集成复杂度
  - 提供实时数据监控和历史数据分析能力
  - 提高数据分析效率和决策质量

### 1.2 项目范围
* **范围内 (In Scope):**
  - 基于PyQt5的桌面应用程序开发
  - 支持8种硬件协议：串口、Modbus、TCP/IP、CAN总线、MQTT、OPC UA、I2C、SPI
  - 支持8种图表类型：折线图、散点图、时间序列图、柱状图、热力图、饼图、箱线图、雷达图
  - 实时数据采集、处理和可视化
  - 数据存储和导出功能
  - 配置管理和日志系统
  - 基础测试框架和示例代码
* **范围外 (Out of Scope):**
  - Web版本或移动端应用
  - 云端数据存储和同步
  - 高级数据挖掘和机器学习功能
  - 多用户权限管理系统
  - 插件开发SDK（第一版本不包含）

## 2. 用户需求

### 2.1 用户故事 (User Stories)
| ID | 角色 | 我想要... (功能) | 以便... (价值) | 优先级 |
| :-- | :-- | :--- | :--- | :--- |
| US001 | 作为工程师 | 连接RS232/RS485设备并实时显示数据 | 监控设备运行状态 | 高 |
| US002 | 作为研究人员 | 打开本地JSON数据文件进行可视化 | 分析历史实验数据 | 高 |
| US003 | 作为数据分析师 | 使用8种图表类型分析同一数据 | 从不同角度发现数据规律 | 高 |
| US004 | 作为测试人员 | 对1KHz高频数据进行滤波处理 | 去除噪声获得真实信号 | 高 |
| US005 | 作为质量工程师 | 自动检测数据中的异常值 | 快速发现产品质量问题 | 高 |
| US006 | 作为研究人员 | 同时显示多个数据源的对比图表 | 比较不同实验条件的结果 | 中 |
| US007 | 作为统计分析师 | 获得数据的统计特征值 | 进行定量分析和报告 | 中 |
| US008 | 作为设备调试员 | 配置串口参数连接不同设备 | 快速适配各种串口设备 | 中 |

### 2.2 功能性需求 (Functional Requirements)

#### 2.2.1 硬件协议支持
* **FR001: 串口通信 (第一阶段重点)**
  * **描述:** 支持多种串口协议，包括自由口串口、RS232、RS485通信
  * **规则:**
    - 支持COM1-COM256端口
    - 波特率范围：9600-115200 bps
    - 数据位：7-8位，停止位：1-2位，校验：无/奇/偶
    - 支持ASCII和二进制数据格式
    - RS485支持半双工通信和地址识别
    - 自动检测串口设备和参数配置
* **FR002: 数据采集与存储 (第一阶段重点)**
  * **描述:** 支持1KHz高频数据采集和JSON格式存储
  * **规则:**
    - 数据采集频率：最高1000Hz (1ms间隔)
    - 数据缓冲区：支持10000个数据点缓存
    - JSON存储格式：时间戳+数据值+设备标识
    - 文件自动分割：每小时或100MB自动创建新文件
    - 支持实时数据和历史数据读取

#### 2.2.2 数据可视化 (第一阶段重点)
* **FR003: 完整图表支持**
  * **描述:** 完善8种图表类型：折线图、时序图、饼图、箱线图、散点图、柱状图、热力图、雷达图
  * **规则:**
    - 已完成：折线图、时序图、饼图、箱线图
    - 待完善：散点图、柱状图、热力图、雷达图
    - 支持本地JSON数据文件加载和可视化
* **FR004: 图表交互功能**
  * **描述:** 支持缩放、平移、数据点选择、图例控制等交互操作
  * **规则:**
    - 鼠标滚轮缩放，拖拽平移
    - 点击显示具体数值和时间戳
    - 支持数据范围选择和局部放大
    - 图例开关控制数据系列显示
* **FR005: 多数据源支持**
  * **描述:** 支持多个数据源同时显示和对比分析
  * **规则:**
    - 最多支持16个图表同时显示
    - 支持网格布局和标签页布局
    - 不同数据源用不同颜色和标识区分
    - 支持数据源的添加、删除、重命名

#### 2.2.3 数据处理与分析 (第一阶段重点)
* **FR006: 数据统计分析**
  * **描述:** 提供基础统计分析功能
  * **规则:**
    - 基础统计：均值、方差、标准差、最大值、最小值
    - 分布分析：直方图、概率密度、累积分布
    - 趋势分析：线性回归、移动平均、趋势检测
* **FR007: 数据滤波处理**
  * **描述:** 支持多种数字滤波算法
  * **规则:**
    - 低通滤波：巴特沃斯、切比雪夫滤波器
    - 高通滤波：去除直流分量和低频噪声
    - 带通滤波：提取特定频率范围信号
    - 中值滤波：去除脉冲噪声
    - 滤波参数可调：截止频率、阶数、窗口大小
* **FR008: 异常检测**
  * **描述:** 自动检测数据中的异常值和异常模式
  * **规则:**
    - 统计异常检测：3σ准则、四分位数法
    - 时序异常检测：突变点检测、周期性异常
    - 阈值异常检测：用户自定义上下限
    - 异常标记：在图表中高亮显示异常点
* **FR009: 数据存储管理**
  * **描述:** JSON格式数据存储，后续支持MongoDB
  * **规则:**
    - 第一阶段：JSON文件存储，支持1KHz数据量
    - 文件命名：设备名_日期时间.json
    - 数据格式：{"timestamp": "2025-01-01T12:00:00", "value": 123.45, "device": "COM1"}
    - 自动压缩：超过100MB自动压缩存档
    - 第二阶段：MongoDB集成（暂缓）

### 2.3 非功能性需求 (Non-Functional Requirements)
* **性能要求 (基于1KHz数据量):**
  - 应用启动时间 < 5秒
  - 图表刷新延迟 < 100ms
  - 支持1KHz (1000Hz) 数据采集频率
  - 实时显示最多10000个数据点
  - 内存使用 < 1GB（1KHz高频数据负载）
  - JSON文件读取速度 > 10MB/s
* **可靠性要求:**
  - 连续运行24小时无崩溃
  - 网络断线自动重连
  - 数据丢失率 < 0.1%
  - 异常恢复时间 < 30秒
* **可用性要求:**
  - 支持Windows 10/11操作系统
  - 界面响应时间 < 200ms
  - 错误信息清晰易懂
  - 支持中文界面
* **兼容性要求:**
  - Python 3.8+ 环境
  - PyQt5 5.15+ 版本
  - 支持1920x1080及以上分辨率
* **安全性要求:**
  - 配置文件加密存储敏感信息
  - 网络通信数据校验
  - 日志文件访问权限控制

## 3. 系统规范

### 3.1 编码规范 (遵循APEX-6核心编码原则)
* **核心编码原则:** KISS, YAGNI, SOLID, DRY, 高内聚低耦合, 代码可读性, 可测试性, 安全编码
* **主要语言:** Python 3.8+
* **风格指南:** PEP 8 Python编码规范
* **命名约定:**
  - 变量和函数使用snake_case
  - 类名使用PascalCase
  - 常量使用UPPER_CASE
  - 私有成员使用_前缀
* **代码注释:**
  - 注释覆盖率要求 > 20%
  - 复杂逻辑必须添加注释
  - 使用中文注释说明业务逻辑
* **文档字符串:**
  - 所有公共函数和类必须有docstring
  - 使用Google风格的docstring格式
  - 包含参数说明、返回值和异常信息
* **CHENGQI注释格式:**
  ```python
  # {{CHENGQI: Action: [Added/Modified/Removed]; Timestamp: [YYYY-MM-DD HH:MM:SS +08:00]; Reason: [Task ID: #123, brief why]; Principle_Applied: [SOLID/Security/etc]; Role: [CM/PM/BA/SA/PL/LD]; Mode: [MODEX,1-5];}}
  ```

### 3.2 开发规范
* **版本控制规范:**
  * 分支策略: [Git Flow/GitHub Flow/...]
  * 提交信息格式: [Conventional Commits/...]
  * 代码审查要求: [必须/可选]
* **测试规范:**
  * 单元测试覆盖率要求: [百分比]
  * 测试命名约定: [格式规范]
  * 测试数据管理: [策略说明]
* **质量标准:**
  * 代码复杂度限制: [圈复杂度上限]
  * 性能要求: [响应时间/吞吐量标准]
  * 安全编码规范: [具体要求]

### 3.3 项目管理规范 (遵循APEX-6工作流)
* **递进式工作流原则:** 后续模式必须基于前序模式产出的文档开展工作，确保工作流的连贯性和一致性
* **文档强制维护原则:** 每次开发活动都必须同步更新Serena中的相关文档，不得遗漏
* **需求确认原则:** 每次用户提问后，必须先复述理解的需求，提出解决方案，获得用户确认后再执行
* **多轮沟通机制:** 特别在模式2方案细化阶段，需要与用户进行充分的多轮深度沟通
* **角色切换规范:** 每种模式切换时必须同步切换到对应的专业角色
* **工具集成要求:**
  - 强制使用ACE进行信息收集
  - 使用Augment task工具进行项目规划与追踪
  - 使用Serena MCP进行文档读写操作
  - 使用PromptX MCP进行角色切换
  - 模式5中使用专业库时必须先使用Context 7 MCP查询文档
* **PowerShell指令要求:** 所有控制台操作必须使用Windows PowerShell准确指令
* **时间戳管理:** 所有文件操作必须使用mcp.server_time服务获取准确时间戳

### 3.4 验收标准
* **功能验收:**
  - 所有8种硬件协议能够正常连接和通信
  - 所有8种图表类型能够正确显示数据
  - 数据采集、存储、导出功能正常
  - 配置管理和日志系统工作正常
* **质量验收:**
  - 代码覆盖率 > 80%
  - 单元测试通过率 100%
  - 性能指标满足非功能性需求
  - 代码符合PEP 8规范
* **文档验收:**
  - API文档完整覆盖所有公共接口
  - 用户手册包含安装和使用说明
  - 开发文档包含架构设计和扩展指南

## 4. 项目现状评估 (基于ACE分析结果)

### 4.1 当前完成度分析
**整体完成度: 约60%**

#### 4.1.1 已完成模块 ✅
* **核心框架 (90%完成)**
  - ✅ 应用程序主类 (src/core/app.py)
  - ✅ 配置管理系统 (src/core/config.py)
  - ✅ 事件总线机制 (src/core/event_bus.py)
  - ✅ 线程管理器 (src/core/thread_manager.py)

* **用户界面 (85%完成)**
  - ✅ 主窗口UI设计 (src/ui/main_window.ui)
  - ✅ 主窗口控制器 (src/ui/main_window_controller.py)
  - ✅ 垂直分割器和布局管理
  - ✅ 日志显示区域和右键菜单

* **日志系统 (95%完成)**
  - ✅ 基础日志记录功能
  - ✅ 文件输出和UI显示集成
  - ✅ 彩色日志显示
  - ✅ 日志级别设置和保存功能

* **数据可视化基础 (70%完成)**
  - ✅ 图表基类 (src/visualization/base_chart.py)
  - ✅ 图表管理器 (src/visualization/chart_manager.py)
  - ✅ 图表初始化器 (src/visualization/chart_initializer.py)
  - ✅ 时序图组件 (完整实现)
  - ✅ 折线图组件 (完整实现)
  - ✅ 饼图组件 (已修复显示错误)
  - ✅ 箱线图组件 (已修复显示错误)

#### 4.1.2 部分完成模块 ⚠️
* **硬件协议支持 (30%完成)**
  - ✅ 协议基类 (src/hardware/protocol_base.py)
  - ✅ 设备管理器框架 (src/hardware/device_manager.py)
  - ✅ 串口协议 (src/hardware/protocols/serial_protocol.py) - 完整实现
  - ❌ Modbus协议 - 仅有框架，未实现
  - ❌ TCP/IP协议 - 未实现
  - ❌ CAN总线协议 - 未实现
  - ❌ MQTT协议 - 未实现
  - ❌ OPC UA协议 - 未实现
  - ❌ I2C协议 - 未实现
  - ❌ SPI协议 - 未实现

* **数据可视化图表 (50%完成)**
  - ⚠️ 散点图 (src/visualization/charts/scatter.py) - 文件存在但功能不完整
  - ⚠️ 柱状图 (src/visualization/charts/histogram.py) - 文件存在但功能不完整
  - ⚠️ 热力图 (src/visualization/charts/heatmap.py) - 文件存在但功能不完整
  - ⚠️ 雷达图 (src/visualization/charts/radar.py) - 文件存在但功能不完整

#### 4.1.3 待开发模块 ❌
* **数据处理层 (10%完成)**
  - ⚠️ 数据分析器 (src/data/analyzer.py) - 基础框架
  - ⚠️ 数据缓冲区 (src/data/buffer.py) - 基础框架
  - ⚠️ 数据清洗 (src/data/cleaner.py) - 基础框架
  - ❌ 数据存储模块 - 未实现

* **测试框架 (20%完成)**
  - ✅ 测试运行器 (run_tests.py)
  - ⚠️ 核心模块测试 - 部分实现
  - ❌ 硬件协议测试 - 未实现
  - ❌ 可视化组件测试 - 未实现

### 4.2 关键问题识别
1. **包管理问题** ✅ 已解决
   - 原问题：uv install命令错误
   - 解决方案：使用uv pip install -e .成功安装依赖

2. **硬件协议实现不完整** ❌ 待解决
   - 仅串口协议完整实现
   - 其他7种协议需要完整开发

3. **图表功能不完整** ❌ 待解决
   - 4种图表类型功能不完整
   - 缺少数据实时更新机制

4. **数据处理层薄弱** ❌ 待解决
   - 缺少完整的数据存储方案
   - 数据分析功能基础薄弱

### 4.3 开发优先级 (基于用户确认)
**第一阶段 - 立即开发 (用户确认):**
1. ✅ 完善现有4种图表组件功能 (散点图、柱状图、热力图、雷达图)
2. ✅ 完善和测试串口协议 (自由口串口、RS232、RS485)
3. ✅ 开发JSON数据存储机制 (1KHz数据量支持)
4. ✅ 实现数据统计、滤波、异常检测功能
5. ✅ 支持本地数据文件可视化和多数据源同时显示

**第二阶段 - 暂缓开发:**
- Modbus、TCP/IP、MQTT、CAN、OPC UA、I2C、SPI协议
- MongoDB数据库集成 (后续考虑)
- 高级数据分析功能
- 性能优化和用户体验增强

## 5. 递进输出 (为模式2方案细化提供需求基础)

### 5.1 为BA角色提供的关键信息
| 输出类别 | 具体内容 | 方案设计影响 | 优先级 |
| :--- | :--- | :--- | :--- |
| 项目目标 | 基于Python Qt的数据可视化软件，支持多协议硬件接口 | 技术栈选择和架构设计约束 | 高 |
| 功能需求 | 8种协议+8种图表+实时数据处理 | 模块化设计和接口标准化要求 | 高 |
| 非功能需求 | 性能<100ms延迟，可靠性24h运行，兼容Windows | 技术选型和优化策略约束 | 高 |
| 项目现状 | 60%完成度，核心框架完成，协议层待开发 | 增量开发策略和重构需求 | 高 |
| 开发规范 | Python PEP8，SOLID原则，测试覆盖率>80% | 代码质量和可维护性要求 | 中 |
| 验收标准 | 功能完整性+性能指标+代码质量标准 | 方案评估和验证标准 | 中 |

### 5.2 需求传递检查清单
- [x] 项目目标明确且可衡量 - 数据可视化软件，支持多协议硬件接口
- [x] 功能需求完整且无歧义 - 详细定义了9个功能需求和用户故事
- [x] 非功能需求具体且可验证 - 明确了性能、可靠性、兼容性指标
- [x] 开发规范详细且可执行 - Python PEP8，SOLID原则，测试要求
- [x] 验收标准清晰且可操作 - 功能、质量、文档验收标准明确
- [x] 用户期望和约束条件已明确记录 - 基于现有项目60%完成度
- [x] 项目现状评估完整 - 详细分析了已完成、部分完成、待开发模块

### 5.3 第一阶段详细开发计划

#### 5.3.1 图表功能完善 (优先级1)
**目标：** 完善散点图、柱状图、热力图、雷达图功能
**现状：** 文件存在但功能不完整
**具体任务：**
- 散点图：实现数据点绘制、颜色映射、大小映射
- 柱状图：实现分组柱状图、堆叠柱状图、水平柱状图
- 热力图：实现颜色映射、数值显示、坐标轴标签
- 雷达图：实现多维数据显示、网格线、数据填充

#### 5.3.2 串口协议完善 (优先级2)
**目标：** 完善串口协议支持RS232/RS485
**现状：** 基础串口协议已实现
**具体任务：**
- RS232标准支持：全双工通信、流控制
- RS485支持：半双工通信、地址识别、冲突检测
- 自由口协议：自定义数据格式解析
- 串口参数自动检测和配置

#### 5.3.3 数据处理功能 (优先级3)
**目标：** 实现统计、滤波、异常检测功能
**现状：** 基础框架存在，功能待实现
**具体任务：**
- 统计分析：均值、方差、分布分析、趋势分析
- 数字滤波：低通、高通、带通、中值滤波
- 异常检测：3σ准则、阈值检测、突变检测
- 实时处理：1KHz数据流实时分析

#### 5.3.4 JSON数据存储 (优先级4)
**目标：** 实现高效JSON数据存储和读取
**现状：** 待开发
**具体任务：**
- JSON格式设计：时间戳+数据值+设备标识
- 高频数据写入：1KHz数据流缓冲和批量写入
- 文件管理：自动分割、压缩、索引
- 数据读取：快速加载和解析大文件

#### 5.3.5 多数据源支持 (优先级5)
**目标：** 支持多个数据源同时显示和对比
**现状：** 基础框架存在
**具体任务：**
- 数据源管理：添加、删除、重命名
- 多图表布局：网格布局、标签页切换
- 数据对比：不同颜色标识、图例控制
- 性能优化：多数据源并行处理

### 5.4 后续阶段准备
* **方案设计准备:** 为BA角色提供完整的需求分析基础和详细的第一阶段开发计划
* **用户沟通准备:** 基于用户确认的优先级，支持多轮沟通细化技术实现方案
* **技术约束传递:** 明确了1KHz数据处理、JSON存储、串口协议等技术约束和性能要求

**递进关系说明:** 本文档作为模式1的产出，为模式2的方案细化与多轮沟通提供完整的需求基础，确保BA角色能够基于准确的需求信息进行解决方案设计。
