# 1. 需求与规范文档

| 版本 | 最后更新时间 | 更新者 | 变更摘要 |
| :--- | :--- | :--- | :--- |
| 1.0  | YYYY-MM-DD HH:MM:SS | PM | 初始创建 |
|      |                     |    |          |

---

## 0. 项目初始化状态

### 0.1 APEX-6强制初始化检查清单
- [ ] PromptX中已创建CM角色并定制完成
- [ ] PromptX中已创建PM角色并定制完成
- [ ] PromptX中已创建BA角色并定制完成
- [ ] PromptX中已创建SA角色并定制完成
- [ ] PromptX中已创建PL角色并定制完成
- [ ] PromptX中已创建LD角色并定制完成
- [ ] 用户已在.serena\memories\路径下创建X.Continuous Context and Intelligence Document.md
- [ ] 用户已在.serena\memories\路径下创建1.Requirements and Specifications Document.md
- [ ] 用户已在.serena\memories\路径下创建2.Solution Architecture and Innovation Document.md
- [ ] 用户已在.serena\memories\路径下创建3.Technology Stack and Design Document.md
- [ ] 用户已在.serena\memories\路径下创建4.Project Planning and Task Management.md
- [ ] 用户已在.serena\memories\路径下创建5.Development Progress and Testing.md

### 0.2 工具集成验证状态
- [ ] ACE(AugmentContextEngine)信息收集工具可正常使用
- [ ] Augment自带的task工具已激活并可进行项目管理
- [ ] Serena MCP记忆管理工具连接正常，可读写.serena\memories\文档
- [ ] PromptX MCP角色管理工具可正常切换6个专业角色
- [ ] Context 7 MCP专业库文档查询工具可正常使用
- [ ] PowerShell环境验证通过，支持所有控制台操作
- [ ] mcp.server_time时间服务可正常获取准确时间戳

## 1. 项目概述

### 1.1 项目目标
* (描述项目的核心目标和期望达成的业务价值。)

### 1.2 项目范围
* **范围内 (In Scope):**
  * (列出明确包含的功能和交付物。)
* **范围外 (Out of Scope):**
  * (列出明确不包含的功能和交付物。)

## 2. 用户需求

### 2.1 用户故事 (User Stories)
| ID | 角色 | 我想要... (功能) | 以便... (价值) | 优先级 |
| :-- | :-- | :--- | :--- | :--- |
| US001 | 作为 [用户类型] | [某个操作] | [达成某个目的] | 高/中/低 |
|       |     |      |      |         |

### 2.2 功能性需求 (Functional Requirements)
* **FR001: [功能名称]**
  * **描述:** (详细描述功能。)
  * **规则:** (列出业务规则和逻辑。)

### 2.3 非功能性需求 (Non-Functional Requirements)
* **性能:** (例如: 页面加载时间小于3秒。)
* **安全性:** (例如: 所有API需进行身份验证。)
* **可用性:** (例如: 需兼容主流浏览器最新版本。)
* **可靠性:** (例如: 系统可用性需达到99.9%。)

## 3. 系统规范

### 3.1 编码规范 (遵循APEX-6核心编码原则)
* **核心编码原则:** KISS, YAGNI, SOLID, DRY, 高内聚低耦合, 代码可读性, 可测试性, 安全编码
* **主要语言:** [Python/JavaScript/...]
* **风格指南:** [PEP 8/ESLint/...]
* **命名约定:** (例如: 变量使用camelCase，类名使用PascalCase。)
* **代码注释:** (注释覆盖率要求和格式规范。)
* **文档字符串:** (函数和类的文档字符串格式要求。)
* **CHENGQI注释格式:**
  ```
  // {{CHENGQI: Action: [Added/Modified/Removed]; Timestamp: [YYYY-MM-DD HH:MM:SS +08:00]; Reason: [Task ID: #123, brief why]; Principle_Applied: [SOLID/Security/etc]; Role: [CM/PM/BA/SA/PL/LD]; Mode: [MODEX,1-5];}}
  ```

### 3.2 开发规范
* **版本控制规范:**
  * 分支策略: [Git Flow/GitHub Flow/...]
  * 提交信息格式: [Conventional Commits/...]
  * 代码审查要求: [必须/可选]
* **测试规范:**
  * 单元测试覆盖率要求: [百分比]
  * 测试命名约定: [格式规范]
  * 测试数据管理: [策略说明]
* **质量标准:**
  * 代码复杂度限制: [圈复杂度上限]
  * 性能要求: [响应时间/吞吐量标准]
  * 安全编码规范: [具体要求]

### 3.3 项目管理规范 (遵循APEX-6工作流)
* **递进式工作流原则:** 后续模式必须基于前序模式产出的文档开展工作，确保工作流的连贯性和一致性
* **文档强制维护原则:** 每次开发活动都必须同步更新Serena中的相关文档，不得遗漏
* **需求确认原则:** 每次用户提问后，必须先复述理解的需求，提出解决方案，获得用户确认后再执行
* **多轮沟通机制:** 特别在模式2方案细化阶段，需要与用户进行充分的多轮深度沟通
* **角色切换规范:** 每种模式切换时必须同步切换到对应的专业角色
* **工具集成要求:**
  - 强制使用ACE进行信息收集
  - 使用Augment task工具进行项目规划与追踪
  - 使用Serena MCP进行文档读写操作
  - 使用PromptX MCP进行角色切换
  - 模式5中使用专业库时必须先使用Context 7 MCP查询文档
* **PowerShell指令要求:** 所有控制台操作必须使用Windows PowerShell准确指令
* **时间戳管理:** 所有文件操作必须使用mcp.server_time服务获取准确时间戳

### 3.4 验收标准
* **功能验收:** (描述如何验证功能需求是否被满足。)
* **质量验收:** (描述代码质量、性能、安全性验收标准。)
* **文档验收:** (描述文档完整性和准确性验收标准。)

## 4. 递进输出 (为模式2方案细化提供需求基础)

### 4.1 为BA角色提供的关键信息
| 输出类别 | 具体内容 | 方案设计影响 | 优先级 |
| :--- | :--- | :--- | :--- |
| 项目目标 | [核心目标和业务价值] | [方案选择的指导原则] | 高 |
| 功能需求 | [详细功能规格和用户故事] | [方案功能范围约束] | 高 |
| 非功能需求 | [性能、安全、可用性要求] | [技术方案选择约束] | 高 |
| 开发规范 | [编码规范和质量标准] | [技术实现约束] | 中 |
| 验收标准 | [功能和质量验收标准] | [方案评估标准] | 中 |

### 4.2 需求传递检查清单
- [ ] 项目目标明确且可衡量
- [ ] 功能需求完整且无歧义
- [ ] 非功能需求具体且可验证
- [ ] 开发规范详细且可执行
- [ ] 验收标准清晰且可操作
- [ ] 用户期望和约束条件已明确记录

### 4.3 后续阶段准备
* **方案设计准备:** 为BA角色提供完整的需求分析基础，支持多方案设计和对比分析
* **用户沟通准备:** 为多轮沟通提供需求背景和用户期望信息
* **技术约束传递:** 为技术方案选择提供明确的约束条件和质量要求

**递进关系说明:** 本文档作为模式1的产出，为模式2的方案细化与多轮沟通提供完整的需求基础，确保BA角色能够基于准确的需求信息进行解决方案设计。
