#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
线程管理模块

该模块负责创建、管理和清理应用程序中的线程。
提供线程池、定时任务线程、工作线程等功能。
"""

import inspect
import logging
import threading
import time
import traceback
import uuid
from concurrent.futures import ThreadPoolExecutor
from dataclasses import dataclass
from enum import Enum, auto
from typing import Any, Callable, Dict, List, Optional, Set, Tuple, Union

from .event_bus import event_bus, AppEvents

class ThreadStatus(Enum):
    """线程状态枚举"""
    CREATED = auto()    # 已创建但未启动
    RUNNING = auto()    # 正在运行
    PAUSED = auto()     # 暂停中
    STOPPING = auto()   # 正在停止
    STOPPED = auto()    # 已停止
    ERROR = auto()      # 发生错误

@dataclass
class ThreadInfo:
    """线程信息数据类"""
    id: str                     # 线程唯一标识符
    name: str                   # 线程名称
    thread: threading.Thread    # 线程对象
    status: ThreadStatus        # 线程状态
    created_at: float           # 创建时间戳
    started_at: Optional[float] = None  # 启动时间戳
    stopped_at: Optional[float] = None  # 停止时间戳
    error: Optional[Exception] = None   # 错误信息（如果有）
    context: Dict[str, Any] = None      # 线程上下文信息

class PeriodicThread(threading.Thread):
    """周期性任务线程"""
    
    def __init__(self, callback: Callable, interval: float, name: str = None, 
                 args: Tuple = None, kwargs: Dict = None):
        """
        初始化周期性任务线程
        
        Args:
            callback: 需要周期性执行的回调函数
            interval: 执行间隔（秒）
            name: 线程名称
            args: 传递给回调函数的位置参数
            kwargs: 传递给回调函数的关键字参数
        """
        super().__init__(name=name)
        self.daemon = True
        self.callback = callback
        self.interval = interval
        self.args = args or ()
        self.kwargs = kwargs or {}
        self._stop_event = threading.Event()
        self._pause_event = threading.Event()
        self._pause_event.set()  # 默认不暂停
        self._logger = logging.getLogger(__name__)
        
    def run(self):
        """线程运行函数"""
        while not self._stop_event.is_set():
            # 等待暂停事件被设置（即恢复状态）
            self._pause_event.wait()
            
            if self._stop_event.is_set():
                break
                
            try:
                # 执行回调
                start_time = time.time()
                self.callback(*self.args, **self.kwargs)
                elapsed = time.time() - start_time
                
                # 计算等待时间（考虑回调执行时间）
                wait_time = max(0, self.interval - elapsed)
                
                # 等待指定时间，或者直到停止标志被设置
                if self._stop_event.wait(wait_time):
                    break
                    
            except Exception as e:
                self._logger.error(f"周期性任务 {self.name} 执行出错: {str(e)}")
                self._logger.debug(traceback.format_exc())
                
                # 短暂等待后继续
                if self._stop_event.wait(1):
                    break
    
    def stop(self):
        """停止线程"""
        self._stop_event.set()
        self._pause_event.set()  # 确保线程不会卡在暂停状态
        
    def pause(self):
        """暂停线程"""
        self._pause_event.clear()
        
    def resume(self):
        """恢复线程"""
        self._pause_event.set()
        
    @property
    def is_stopped(self) -> bool:
        """线程是否已停止"""
        return self._stop_event.is_set()
        
    @property
    def is_paused(self) -> bool:
        """线程是否已暂停"""
        return not self._pause_event.is_set()

class WorkerThread(threading.Thread):
    """工作线程"""
    
    def __init__(self, target: Callable, name: str = None, 
                 args: Tuple = None, kwargs: Dict = None, daemon: bool = True):
        """
        初始化工作线程
        
        Args:
            target: 线程执行的函数
            name: 线程名称
            args: 传递给目标函数的位置参数
            kwargs: 传递给目标函数的关键字参数
            daemon: 是否为守护线程
        """
        super().__init__(target=target, name=name, args=args or (), kwargs=kwargs or {})
        self.daemon = daemon
        self._stop_event = threading.Event()
        self._logger = logging.getLogger(__name__)
        
    def stop(self):
        """停止线程"""
        self._stop_event.set()
        
    @property
    def is_stopped(self) -> bool:
        """线程是否已停止"""
        return self._stop_event.is_set()

class ThreadManager:
    """线程管理器类，使用单例模式确保全局只有一个线程管理器实例"""
    
    _instance = None
    
    def __new__(cls):
        """实现单例模式"""
        if cls._instance is None:
            cls._instance = super(ThreadManager, cls).__new__(cls)
            cls._instance._initialized = False
        return cls._instance
    
    def __init__(self):
        """初始化线程管理器"""
        if self._initialized:
            return
            
        self._initialized = True
        self._threads: Dict[str, ThreadInfo] = {}  # 线程信息字典 {线程ID: 线程信息}
        self._lock = threading.RLock()  # 用于保护线程字典的线程锁
        self._pool = ThreadPoolExecutor(max_workers=10)  # 线程池
        self._shutdown = False  # 是否已关闭
        
        self._logger = logging.getLogger(__name__)
        
        # 注册应用程序关闭事件处理器
        event_bus.register(AppEvents.SHUTDOWN, self.shutdown)
    
    def create_thread(self, target: Callable, name: str = None, 
                     args: Tuple = None, kwargs: Dict = None,
                     daemon: bool = True, auto_start: bool = True) -> str:
        """
        创建并返回一个工作线程
        
        Args:
            target: 线程执行的函数
            name: 线程名称
            args: 传递给目标函数的位置参数
            kwargs: 传递给目标函数的关键字参数
            daemon: 是否为守护线程
            auto_start: 是否自动启动线程
            
        Returns:
            线程ID
        """
        if self._shutdown:
            raise RuntimeError("线程管理器已关闭，无法创建新线程")
            
        thread_id = str(uuid.uuid4())
        thread_name = name or f"Worker-{thread_id[:8]}"
        
        thread = WorkerThread(
            target=self._thread_wrapper(thread_id, target),
            name=thread_name,
            args=args,
            kwargs=kwargs,
            daemon=daemon
        )
        
        thread_info = ThreadInfo(
            id=thread_id,
            name=thread_name,
            thread=thread,
            status=ThreadStatus.CREATED,
            created_at=time.time(),
            context={}
        )
        
        with self._lock:
            self._threads[thread_id] = thread_info
            
        self._logger.debug(f"创建工作线程: {thread_name} (ID: {thread_id})")
        
        if auto_start:
            self.start_thread(thread_id)
            
        return thread_id
    
    def create_periodic_thread(self, callback: Callable, interval: float, 
                              name: str = None, args: Tuple = None, 
                              kwargs: Dict = None, auto_start: bool = True) -> str:
        """
        创建并返回一个周期性任务线程
        
        Args:
            callback: 需要周期性执行的回调函数
            interval: 执行间隔（秒）
            name: 线程名称
            args: 传递给回调函数的位置参数
            kwargs: 传递给回调函数的关键字参数
            auto_start: 是否自动启动线程
            
        Returns:
            线程ID
        """
        if self._shutdown:
            raise RuntimeError("线程管理器已关闭，无法创建新线程")
            
        thread_id = str(uuid.uuid4())
        thread_name = name or f"Periodic-{thread_id[:8]}"
        
        thread = PeriodicThread(
            callback=callback,
            interval=interval,
            name=thread_name,
            args=args,
            kwargs=kwargs
        )
        
        thread_info = ThreadInfo(
            id=thread_id,
            name=thread_name,
            thread=thread,
            status=ThreadStatus.CREATED,
            created_at=time.time(),
            context={}
        )
        
        with self._lock:
            self._threads[thread_id] = thread_info
            
        self._logger.debug(f"创建周期性任务线程: {thread_name} (ID: {thread_id}), 间隔: {interval}秒")
        
        if auto_start:
            self.start_thread(thread_id)
            
        return thread_id
    
    def submit_task(self, func: Callable, *args, **kwargs) -> Any:
        """
        提交任务到线程池执行
        
        Args:
            func: 要执行的函数
            *args, **kwargs: 传递给函数的参数
            
        Returns:
            Future对象，可用于获取任务执行结果
        """
        if self._shutdown:
            raise RuntimeError("线程管理器已关闭，无法提交新任务")
            
        return self._pool.submit(func, *args, **kwargs)
    
    def start_thread(self, thread_id: str) -> bool:
        """
        启动线程
        
        Args:
            thread_id: 线程ID
            
        Returns:
            是否成功启动
        """
        with self._lock:
            if thread_id not in self._threads:
                self._logger.warning(f"找不到线程ID: {thread_id}")
                return False
                
            thread_info = self._threads[thread_id]
            
            if thread_info.status != ThreadStatus.CREATED:
                self._logger.warning(f"线程 {thread_info.name} 已经启动或已结束")
                return False
                
            thread_info.thread.start()
            thread_info.status = ThreadStatus.RUNNING
            thread_info.started_at = time.time()
            
        self._logger.debug(f"启动线程: {thread_info.name} (ID: {thread_id})")
        return True
    
    def stop_thread(self, thread_id: str) -> bool:
        """
        停止线程
        
        Args:
            thread_id: 线程ID
            
        Returns:
            是否成功发送停止信号
        """
        with self._lock:
            if thread_id not in self._threads:
                self._logger.warning(f"找不到线程ID: {thread_id}")
                return False
                
            thread_info = self._threads[thread_id]
            
            if thread_info.status in (ThreadStatus.STOPPED, ThreadStatus.STOPPING):
                self._logger.warning(f"线程 {thread_info.name} 已经处于停止状态")
                return False
                
            thread_info.status = ThreadStatus.STOPPING
            
            # 如果线程支持stop方法（我们自定义的线程类）
            if hasattr(thread_info.thread, 'stop') and callable(thread_info.thread.stop):
                thread_info.thread.stop()
                
        self._logger.debug(f"发送停止信号到线程: {thread_info.name} (ID: {thread_id})")
        return True
    
    def pause_thread(self, thread_id: str) -> bool:
        """
        暂停线程（仅适用于周期性任务线程）
        
        Args:
            thread_id: 线程ID
            
        Returns:
            是否成功发送暂停信号
        """
        with self._lock:
            if thread_id not in self._threads:
                self._logger.warning(f"找不到线程ID: {thread_id}")
                return False
                
            thread_info = self._threads[thread_id]
            
            if thread_info.status != ThreadStatus.RUNNING:
                self._logger.warning(f"线程 {thread_info.name} 不在运行状态，无法暂停")
                return False
                
            # 检查是否为周期性任务线程
            if not isinstance(thread_info.thread, PeriodicThread):
                self._logger.warning(f"线程 {thread_info.name} 不是周期性任务线程，无法暂停")
                return False
                
            thread_info.thread.pause()
            thread_info.status = ThreadStatus.PAUSED
            
        self._logger.debug(f"暂停线程: {thread_info.name} (ID: {thread_id})")
        return True
    
    def resume_thread(self, thread_id: str) -> bool:
        """
        恢复暂停的线程（仅适用于周期性任务线程）
        
        Args:
            thread_id: 线程ID
            
        Returns:
            是否成功发送恢复信号
        """
        with self._lock:
            if thread_id not in self._threads:
                self._logger.warning(f"找不到线程ID: {thread_id}")
                return False
                
            thread_info = self._threads[thread_id]
            
            if thread_info.status != ThreadStatus.PAUSED:
                self._logger.warning(f"线程 {thread_info.name} 不在暂停状态，无法恢复")
                return False
                
            # 检查是否为周期性任务线程
            if not isinstance(thread_info.thread, PeriodicThread):
                self._logger.warning(f"线程 {thread_info.name} 不是周期性任务线程，无法恢复")
                return False
                
            thread_info.thread.resume()
            thread_info.status = ThreadStatus.RUNNING
            
        self._logger.debug(f"恢复线程: {thread_info.name} (ID: {thread_id})")
        return True
    
    def wait_thread(self, thread_id: str, timeout: Optional[float] = None) -> bool:
        """
        等待线程结束
        
        Args:
            thread_id: 线程ID
            timeout: 等待超时时间（秒），None表示无限等待
            
        Returns:
            线程是否已结束
        """
        with self._lock:
            if thread_id not in self._threads:
                self._logger.warning(f"找不到线程ID: {thread_id}")
                return False
                
            thread_info = self._threads[thread_id]
            thread = thread_info.thread
            
        # 在锁外等待，避免死锁
        thread.join(timeout)
        return not thread.is_alive()
    
    def is_alive(self, thread_id: str) -> bool:
        """
        检查线程是否活动
        
        Args:
            thread_id: 线程ID
            
        Returns:
            线程是否活动
        """
        with self._lock:
            if thread_id not in self._threads:
                return False
                
            return self._threads[thread_id].thread.is_alive()
    
    def get_thread_status(self, thread_id: str) -> Optional[ThreadStatus]:
        """
        获取线程状态
        
        Args:
            thread_id: 线程ID
            
        Returns:
            线程状态
        """
        with self._lock:
            if thread_id not in self._threads:
                return None
                
            return self._threads[thread_id].status
    
    def get_thread_info(self, thread_id: str) -> Optional[ThreadInfo]:
        """
        获取线程信息
        
        Args:
            thread_id: 线程ID
            
        Returns:
            线程信息
        """
        with self._lock:
            return self._threads.get(thread_id)
    
    def list_threads(self) -> List[ThreadInfo]:
        """
        获取所有线程信息
        
        Returns:
            线程信息列表
        """
        with self._lock:
            return list(self._threads.values())
    
    def cleanup_finished_threads(self) -> int:
        """
        清理已结束的线程
        
        Returns:
            清理的线程数量
        """
        to_remove = []
        
        with self._lock:
            for thread_id, thread_info in self._threads.items():
                # 检查线程是否已结束
                if not thread_info.thread.is_alive():
                    # 如果状态不是STOPPED或ERROR，则更新为STOPPED
                    if thread_info.status not in (ThreadStatus.STOPPED, ThreadStatus.ERROR):
                        thread_info.status = ThreadStatus.STOPPED
                        thread_info.stopped_at = time.time()
                    # 无论状态如何，都标记为要删除
                    to_remove.append(thread_id)
            
            for thread_id in to_remove:
                del self._threads[thread_id]
                
        if to_remove:
            self._logger.debug(f"清理 {len(to_remove)} 个已结束的线程")
            
        return len(to_remove)
    
    def shutdown(self, wait: bool = True, cancel_futures: bool = False) -> None:
        """
        关闭线程管理器，停止所有线程
        
        Args:
            wait: 是否等待所有线程结束
            cancel_futures: 是否取消未完成的future
        """
        if self._shutdown:
            return
            
        self._shutdown = True
        self._logger.info("关闭线程管理器...")
        
        # 停止所有线程
        with self._lock:
            thread_ids = list(self._threads.keys())
            
        for thread_id in thread_ids:
            self.stop_thread(thread_id)
        
        # 关闭线程池
        self._pool.shutdown(wait=wait, cancel_futures=cancel_futures)
        
        if wait:
            # 等待所有线程结束
            for thread_id in thread_ids:
                self.wait_thread(thread_id)
            
        self._logger.info("线程管理器已关闭")
    
    def _thread_wrapper(self, thread_id: str, target: Callable) -> Callable:
        """
        线程执行函数包装器，用于捕获异常和更新状态
        
        Args:
            thread_id: 线程ID
            target: 原始线程执行函数
            
        Returns:
            包装后的函数
        """
        def wrapper(*args, **kwargs):
            try:
                result = target(*args, **kwargs)
                
                with self._lock:
                    if thread_id in self._threads:
                        thread_info = self._threads[thread_id]
                        thread_info.status = ThreadStatus.STOPPED
                        thread_info.stopped_at = time.time()
                        
                return result
                
            except Exception as e:
                self._logger.error(f"线程 {thread_id} 执行出错: {str(e)}")
                self._logger.debug(traceback.format_exc())
                
                with self._lock:
                    if thread_id in self._threads:
                        thread_info = self._threads[thread_id]
                        thread_info.status = ThreadStatus.ERROR
                        thread_info.error = e
                        thread_info.stopped_at = time.time()
                
                # 向事件总线发送异常事件
                event_bus.publish(AppEvents.EXCEPTION, e, thread_id=thread_id)
                
                raise
                
        return wrapper

# 创建全局线程管理器实例
thread_manager = ThreadManager()
