#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
配置管理模块测试
"""

import os
import sys
import unittest
import json
import tempfile
import shutil
from unittest.mock import patch, MagicMock

# 确保可以导入被测试模块
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..')))

from src.core.config import Config

class TestConfig(unittest.TestCase):
    """配置管理测试类"""
    
    def setUp(self):
        """每个测试方法执行前的设置"""
        # 创建临时目录来存放测试配置文件
        self.test_dir = tempfile.mkdtemp()
        
        # 模拟配置文件路径
        self.mock_config_dir = os.path.join(self.test_dir, 'config')
        os.makedirs(self.mock_config_dir, exist_ok=True)
        
        self.mock_default_config_path = os.path.join(self.mock_config_dir, 'default_settings.json')
        self.mock_user_config_path = os.path.join(self.mock_config_dir, 'user_settings.json')
        
        # 创建默认配置文件
        default_config = {
            "app": {
                "name": "Test App",
                "version": "1.0.0"
            },
            "data": {
                "storage_type": "memory"
            }
        }
        
        with open(self.mock_default_config_path, 'w', encoding='utf-8') as f:
            json.dump(default_config, f)
        
        # 重置Config单例
        Config._instance = None
        
        # 使用对象补丁方式来模拟配置路径
        # 先创建一个Config实例
        config_instance = Config()
        
        # 直接设置_config_dir属性
        config_instance._config_dir = self.mock_config_dir
        config_instance._default_config_path = self.mock_default_config_path
        config_instance._user_config_path = self.mock_user_config_path
        
        # 重新加载配置
        config_instance.load_config()
    
    def tearDown(self):
        """每个测试方法执行后的清理"""
        # 删除临时目录
        shutil.rmtree(self.test_dir)
    
    def test_singleton(self):
        """测试Config类是否正确实现单例模式"""
        config1 = Config()
        config2 = Config()
        self.assertIs(config1, config2)
    
    def test_get_default_config(self):
        """测试从默认配置中获取值"""
        config = Config()
        self.assertEqual(config.get("app.name"), "Test App")
        self.assertEqual(config.get("app.version"), "1.0.0")
        self.assertEqual(config.get("data.storage_type"), "memory")
    
    def test_get_with_default(self):
        """测试获取不存在的配置项时使用默认值"""
        config = Config()
        self.assertEqual(config.get("app.nonexistent", "default_value"), "default_value")
    
    def test_set_and_get(self):
        """测试设置和获取配置项"""
        config = Config()
        config.set("app.name", "New App Name")
        self.assertEqual(config.get("app.name"), "New App Name")
        
        # 测试设置新的嵌套配置项
        config.set("new.nested.item", "value")
        self.assertEqual(config.get("new.nested.item"), "value")
    
    def test_user_config_override(self):
        """测试用户配置覆盖默认配置"""
        # 创建用户配置文件
        user_config = {
            "app": {
                "name": "User App"
            }
        }
        
        with open(self.mock_user_config_path, 'w', encoding='utf-8') as f:
            json.dump(user_config, f)
        
        # 重新加载配置
        config = Config()
        config.load_config()
        
        # 验证用户配置覆盖了默认配置
        self.assertEqual(config.get("app.name"), "User App")
        # 验证未覆盖的配置项仍然从默认配置加载
        self.assertEqual(config.get("app.version"), "1.0.0")
    
    def test_save_config(self):
        """测试保存配置到用户配置文件"""
        config = Config()
        config.set("app.name", "Saved App")
        config.save()
        
        # 验证配置文件已保存
        self.assertTrue(os.path.exists(self.mock_user_config_path))
        
        # 读取保存的配置文件
        with open(self.mock_user_config_path, 'r', encoding='utf-8') as f:
            saved_config = json.load(f)
        
        # 验证保存的配置正确
        self.assertEqual(saved_config["app"]["name"], "Saved App")
    
    def test_reset_config(self):
        """测试重置配置为默认值"""
        # 创建用户配置文件
        user_config = {
            "app": {
                "name": "User App"
            }
        }
        
        with open(self.mock_user_config_path, 'w', encoding='utf-8') as f:
            json.dump(user_config, f)
        
        # 加载配置
        config = Config()
        config.load_config()
        
        # 验证用户配置已加载
        self.assertEqual(config.get("app.name"), "User App")
        
        # 重置配置
        config.reset()
        
        # 验证配置已重置为默认值
        self.assertEqual(config.get("app.name"), "Test App")
        
        # 验证用户配置文件已删除
        self.assertFalse(os.path.exists(self.mock_user_config_path))

if __name__ == "__main__":
    unittest.main() 