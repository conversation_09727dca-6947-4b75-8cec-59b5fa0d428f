#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
串口协议测试模块

提供测试串口通信功能的简单测试程序。
"""

import logging
import sys
import time
from typing import List, Tuple

# 配置日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)

# 导入协议类
try:
    from src.hardware.protocols.serial_protocol import SerialProtocol
    from src.hardware.protocol_base import ProtocolEventType
except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保当前目录是项目根目录，或者已正确设置PYTHONPATH。")
    sys.exit(1)

logger = logging.getLogger("SerialTest")

def list_ports() -> None:
    """列出系统上可用的串口"""
    ports = SerialProtocol.list_available_ports()
    if not ports:
        print("未找到可用的串口设备")
        return
        
    print("\n可用串口设备:")
    print("-" * 80)
    print(f"{'端口名':<10} | {'描述':<40} | {'硬件ID':<30}")
    print("-" * 80)
    
    for port, desc, hwid in ports:
        print(f"{port:<10} | {desc[:40]:<40} | {hwid[:30]:<30}")

def on_data_received(data) -> None:
    """数据接收回调函数"""
    if isinstance(data, bytes):
        # 如果是字节数据，以十六进制显示
        hex_data = ' '.join([f'{b:02X}' for b in data])
        logger.info(f"收到数据 (HEX): {hex_data}")
    else:
        logger.info(f"收到数据: {data}")

def on_connected() -> None:
    """连接成功回调函数"""
    logger.info("串口已连接")

def on_disconnected() -> None:
    """断开连接回调函数"""
    logger.info("串口已断开连接")

def on_error(error) -> None:
    """错误回调函数"""
    logger.error(f"发生错误: {error}")

def test_serial_connection(port: str = None) -> None:
    """测试串口连接"""
    if port is None:
        # 如果未指定端口，获取可用端口列表
        ports = SerialProtocol.list_available_ports()
        if not ports:
            logger.error("未找到可用的串口设备")
            return
            
        # 使用第一个可用端口
        port = ports[0][0]
        logger.info(f"使用自动选择的端口: {port}")
    
    # 创建串口协议实例
    serial_protocol = SerialProtocol(port)
    
    # 设置串口参数
    serial_protocol.set_config({
        "baudrate": 9600,
        "bytesize": 8,
        "parity": "N",
        "stopbits": 1,
        "timeout": 1.0,
        "read_mode": "line"
    })
    
    # 注册事件回调
    serial_protocol.register_event_callback(ProtocolEventType.DATA_RECEIVED, on_data_received)
    serial_protocol.register_event_callback(ProtocolEventType.CONNECTED, on_connected)
    serial_protocol.register_event_callback(ProtocolEventType.DISCONNECTED, on_disconnected)
    serial_protocol.register_event_callback(ProtocolEventType.ERROR, on_error)
    
    # 连接到串口
    try:
        if serial_protocol.connect():
            logger.info(f"成功连接到串口 {port}")
            
            # 发送测试命令
            print(f"准备发送命令: 'AT\\r\\n'，波特率：9600，等待响应：是，超时：2.0秒")
            try:
                # 修改send_command调用，增加调试输出
                print("开始发送命令...")
                response = serial_protocol.send_command("AT\r\n", wait_response=True, timeout=5.0)  # 将超时延长到5秒
                print(f"命令发送完成，响应类型：{type(response)}，响应值：{repr(response)}")
                if response is not None:
                    logger.info(f"收到响应: {response}")
                else:
                    logger.warning("未收到响应")
            except Exception as e:
                logger.error(f"发送命令失败: {e}")
                # 添加异常详细信息
                import traceback
                print(f"异常详细信息: {traceback.format_exc()}")
            
            # 保持连接一段时间以接收数据
            logger.info("等待接收数据 (10秒)...")
            time.sleep(10)
            
            # 断开连接
            if serial_protocol.disconnect():
                logger.info("成功断开串口连接")
            else:
                logger.error("断开串口连接失败")
                
        else:
            logger.error(f"连接到串口 {port} 失败")
            
    except Exception as e:
        logger.error(f"测试过程中发生异常: {e}")
        
def main() -> None:
    """主函数"""
    print("\n==== 串口通信测试程序 ====\n")
    
    # 列出可用串口
    list_ports()
    
    # 用户选择测试模式
    print("\n请选择测试模式:")
    print("1. 使用自动选择的串口")
    print("2. 手动指定串口")
    print("3. 退出程序")
    
    choice = input("请输入选项 (1-3): ")
    
    if choice == "1":
        test_serial_connection()
    elif choice == "2":
        port = input("请输入串口名称 (如 COM1 或 /dev/ttyUSB0): ")
        test_serial_connection(port)
    elif choice == "3":
        print("程序已退出")
        return
    else:
        print("无效选项，程序已退出")
        return

if __name__ == "__main__":
    main() 