#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
箱线图示例 - 展示如何使用BoxPlotChart类创建和管理箱线图
"""

import sys
import numpy as np
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QHBoxLayout
from PyQt5.QtCore import QTimer

# 导入箱线图类
from src.visualization.charts.box_plot import BoxPlotChart

class BoxPlotDemo(QMainWindow):
    """箱线图示例应用"""
    def __init__(self):
        super(BoxPlotDemo, self).__init__()
        self.setWindowTitle("箱线图示例")
        self.resize(800, 600)
        
        # 创建中央窗口部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout()
        central_widget.setLayout(main_layout)
        
        # 创建箱线图
        self.box_plot = BoxPlotChart("不同群体身高数据对比")
        
        # 创建按钮布局
        button_layout = QHBoxLayout()
        
        # 添加数据按钮
        self.add_btn = QPushButton("添加数据")
        self.add_btn.clicked.connect(self.add_random_data)
        
        # 更新数据按钮
        self.update_btn = QPushButton("更新数据")
        self.update_btn.clicked.connect(self.update_random_data)
        
        # 删除数据按钮
        self.remove_btn = QPushButton("删除数据")
        self.remove_btn.clicked.connect(self.remove_data)
        
        # 清空数据按钮
        self.clear_btn = QPushButton("清空数据")
        self.clear_btn.clicked.connect(self.clear_data)
        
        # 添加按钮到布局
        button_layout.addWidget(self.add_btn)
        button_layout.addWidget(self.update_btn)
        button_layout.addWidget(self.remove_btn)
        button_layout.addWidget(self.clear_btn)
        
        # 添加图表和按钮到主布局
        main_layout.addWidget(self.box_plot)
        main_layout.addLayout(button_layout)
        
        # 添加示例数据
        self.data_count = 0
        self.group_names = ["男性", "女性", "儿童", "青少年", "成年人", "老年人"]
        
        # 添加几组初始数据
        self.add_random_data()
        self.add_random_data()
        self.add_random_data()
        
    def add_random_data(self):
        """添加随机箱线图数据"""
        if self.data_count >= len(self.group_names):
            return
            
        # 生成随机数据
        mean = np.random.uniform(150, 180)
        std = np.random.uniform(5, 15)
        data = np.random.normal(mean, std, 100)
        
        # 计算箱线图所需统计量
        min_val = np.min(data)
        q1 = np.percentile(data, 25)
        median = np.median(data)
        q3 = np.percentile(data, 75)
        max_val = np.max(data)
        
        # 组装箱线图数据
        box_data = [min_val, q1, median, q3, max_val]
        
        # 添加到图表
        group_name = self.group_names[self.data_count]
        self.box_plot.add_data(group_name, box_data)
        
        # 更新计数
        self.data_count += 1
        
        # 禁用添加按钮（如果已添加所有组）
        if self.data_count >= len(self.group_names):
            self.add_btn.setEnabled(False)
        
    def update_random_data(self):
        """更新随机箱线图数据"""
        if self.data_count == 0:
            return
            
        # 随机选择一个组进行更新
        idx = np.random.randint(0, self.data_count)
        
        # 生成新的随机数据
        mean = np.random.uniform(150, 180)
        std = np.random.uniform(5, 15)
        data = np.random.normal(mean, std, 100)
        
        # 计算箱线图所需统计量
        min_val = np.min(data)
        q1 = np.percentile(data, 25)
        median = np.median(data)
        q3 = np.percentile(data, 75)
        max_val = np.max(data)
        
        # 组装箱线图数据
        box_data = [min_val, q1, median, q3, max_val]
        
        # 更新图表数据
        self.box_plot.update_data(idx, box_data)
        
    def remove_data(self):
        """移除最后一组数据"""
        if self.data_count == 0:
            return
            
        # 移除最后一组数据
        self.data_count -= 1
        self.box_plot.remove_data(self.data_count)
        
        # 启用添加按钮
        self.add_btn.setEnabled(True)
        
    def clear_data(self):
        """清空所有数据"""
        self.box_plot.clear()
        self.data_count = 0
        
        # 启用添加按钮
        self.add_btn.setEnabled(True)

if __name__ == "__main__":
    app = QApplication(sys.argv)
    demo = BoxPlotDemo()
    demo.show()
    sys.exit(app.exec_()) 