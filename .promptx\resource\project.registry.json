{"version": "2.0.0", "source": "project", "metadata": {"version": "2.0.0", "description": "project 级资源注册表", "createdAt": "2025-07-31T02:34:17.527Z", "updatedAt": "2025-07-31T02:34:17.531Z", "resourceCount": 6}, "resources": [{"id": "ba", "source": "project", "protocol": "role", "name": "Ba 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/ba/ba.role.md", "metadata": {"createdAt": "2025-07-31T02:34:17.528Z", "updatedAt": "2025-07-31T02:34:17.528Z", "scannedAt": "2025-07-31T02:34:17.528Z", "path": "role/ba/ba.role.md"}}, {"id": "cm", "source": "project", "protocol": "role", "name": "Cm 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/cm/cm.role.md", "metadata": {"createdAt": "2025-07-31T02:34:17.529Z", "updatedAt": "2025-07-31T02:34:17.529Z", "scannedAt": "2025-07-31T02:34:17.529Z", "path": "role/cm/cm.role.md"}}, {"id": "ld", "source": "project", "protocol": "role", "name": "Ld 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/ld/ld.role.md", "metadata": {"createdAt": "2025-07-31T02:34:17.529Z", "updatedAt": "2025-07-31T02:34:17.529Z", "scannedAt": "2025-07-31T02:34:17.529Z", "path": "role/ld/ld.role.md"}}, {"id": "pl", "source": "project", "protocol": "role", "name": "Pl 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/pl/pl.role.md", "metadata": {"createdAt": "2025-07-31T02:34:17.530Z", "updatedAt": "2025-07-31T02:34:17.530Z", "scannedAt": "2025-07-31T02:34:17.530Z", "path": "role/pl/pl.role.md"}}, {"id": "pm", "source": "project", "protocol": "role", "name": "Pm 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/pm/pm.role.md", "metadata": {"createdAt": "2025-07-31T02:34:17.530Z", "updatedAt": "2025-07-31T02:34:17.530Z", "scannedAt": "2025-07-31T02:34:17.530Z", "path": "role/pm/pm.role.md"}}, {"id": "sa", "source": "project", "protocol": "role", "name": "Sa 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/sa/sa.role.md", "metadata": {"createdAt": "2025-07-31T02:34:17.530Z", "updatedAt": "2025-07-31T02:34:17.530Z", "scannedAt": "2025-07-31T02:34:17.530Z", "path": "role/sa/sa.role.md"}}], "stats": {"totalResources": 6, "byProtocol": {"role": 6}, "bySource": {"project": 6}}}